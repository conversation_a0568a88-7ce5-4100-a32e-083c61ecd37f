<?php

namespace App\Providers;

use App\Models\Page;
use App\Models\TourCategory;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        View::composer('frontend.layouts.*', function ($view) {
            $tourCategory = TourCategory::where('status', 1)->select('slug', 'category_title', 'id')->with('tour')->get();
            $pages = Page::where('status', 1)->get();

            $view->with(['tourCategory' => $tourCategory, 'pages' => $pages]);
        });

        View::composer('frontend.*', function ($view) {
            $currency= session()->get('currency') ;
            $view->with(['currency' => $currency]);
        });
    }


}
