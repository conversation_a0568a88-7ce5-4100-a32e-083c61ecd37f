<?php

namespace App\Providers;


use Foundation\Lib\Impersonated;

use Foundation\Models\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Gate::before(function ($user, $ability) {
            if ($user->hasRole(User::DEFAULT_ROLE) || (Impersonated::isImpersonating() && request()->is('spanel/revert-impersonation'))) {
                return true;
            }
        });

//        Passport::routes();

    }
}
