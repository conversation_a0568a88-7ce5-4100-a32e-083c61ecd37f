<?php

namespace App\Providers;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;
use Monolog\Processor\GitProcessor;
use Monolog\Processor\MemoryUsageProcessor;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
//        $this->app['theme']->init(active_theme());
        Passport::ignoreRoutes();
        Paginator::useBootstrapFive();
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->logger();

        if (request()->server('HTTP_X_FORWARDED_PROTO') == 'https') {
            \Illuminate\Support\Facades\URL::forceScheme('https');
        }


        Collection::macro('transpose', function () {
            $items = array_map(function (...$items) {
                return $items;
            }, ...$this->values());
            return new static($items);
        });

    }

    private function logger()
    {
        $log = $this->app->get('log');

        $log->pushProcessor(function ($record) {
            $record['extra']['ip'] = request()->ip();

            if (auth()->check()) {
                $record['extra']['user_id'] = auth()->id();
            }

            return $record;
        });

        if ($this->app->environment('local')) {

            $log->pushProcessor(new MemoryUsageProcessor());
            $log->pushProcessor(new GitProcessor());

        }
    }
}
