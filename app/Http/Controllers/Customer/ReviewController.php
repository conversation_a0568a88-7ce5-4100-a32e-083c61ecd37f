<?php

namespace App\Http\Controllers\Customer;

use App\Models\Tour;
use App\Models\Review;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        $reviews = Review::whereUserId($user->id)->paginate();

        return view('frontend.customer.reviews.index', compact('reviews', 'user'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'rating' => ['required', 'numeric', 'min:1', 'max:5'],
            'comment' => ['required', 'string', 'max:255'],
            'tour_id' => ['required', 'exists:tours,id'],
        ]);

        $tour = Tour::findOrFail($request->tour_id);

        // check if user has already reviewed this tour
        if ($tour->reviews()->whereUserId(Auth::user()->id)->exists()) {
            return redirect()->route('tour.detail', $tour->slug)->with('error', 'You have already reviewed this tour');
        }

        $validated['user_id'] = Auth::user()->id;
        $tour->reviews()->create($validated);

        return redirect()->route('customer.reviews.index')->with('success', 'Review created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $review = Review::with(['user', 'reviewable'])
            ->whereUserId($user->id)
            ->findOrFail($id);

        return view('frontend.customer.reviews.show', compact('review', 'user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = Auth::user();

        $review = Review::with(['reviewable'])
            ->whereUserId($user->id)
            ->findOrFail($id);

        return view('frontend.customer.reviews.edit', compact('review', 'user'));
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $review = Review::where('user_id', Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'rating' => ['required', 'numeric', 'min:1', 'max:5'],
            'comment' => ['required', 'string', 'max:255'],
            'tour_id' => ['required', 'exists:tours,id'],
        ]);

        $validated['is_published'] = false;
        $review->update($validated);

        return redirect()->route('customer.reviews.show', $review->id)
            ->with('success', 'Review updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $review = Review::whereUserId(Auth::id())->findOrFail($id);

        $review->delete();

        return redirect()->route('customer.reviews.index')
            ->with('success', 'Review deleted successfully');
    }
}
