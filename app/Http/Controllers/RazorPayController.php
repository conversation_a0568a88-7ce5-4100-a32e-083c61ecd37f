<?php

namespace App\Http\Controllers;

use App\Jobs\SendBookingConfirmationEmail;
use App\Jobs\SendBookingInquiryEmail;
use App\Models\Booking;
use App\Models\Cart;
use App\Models\Payment;
use App\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Razorpay\Api\Api;
use Stripe\Charge;
use Stripe\Checkout\Session;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class RazorPayController extends Controller
{


    public function payViaRazorPay(Request $request)
    {
        $input = $request->all();
        dd($input);
        $api = new Api(config('razor.razor_key_id'), config('razor.razor_key_secret'));
        $payment = $api->payment->fetch($input['razorpay_payment_id']);
        dd($payment);
        if(count($input) && !empty($input['razorpay_payment_id'])) {
            try {
                $response = $api->payment->fetch($input['razorpay_payment_id'])->capture(array('amount' => $payment['amount']));
                $payment = Payment::create([
                    'r_payment_id' => $response['id'],
                    'method' => $response['method'],
                    'currency' => $response['currency'],
                    'user_email' => $response['email'],
                    'amount' => $response['amount'],
                    'json_response' => json_encode((array)$response)
                ]);
            } catch(\Exception $e) {
                Log::error('error', (array)$e->getMessage());
                return redirect()->back();
            }
        }
        session()->flash('success', 'Payment Success');
        return redirect()->back();
    }


    /**
     * @param Authenticatable|User|null $user
     * @param Cart $row
     * @param Session $response
     * @return void
     */
    public function bookingPost(User|Authenticatable|null $user, Cart $row, Session $response)
    {
        $booking = Booking::create([
            'user_id' => $user->id,
            'tour_id' => $row->tour_id,
            'cart_id' => $row->id,
            'total_price' => $row->price,
            'status' => 'completed', //payment status
            'order_status' => 'Pending',
            'payment_gateway' => 'stripe',
            'payment_meta' => $response,
        ]);

        $row->status = 'completed';
        $row->order_status = 'Pending';
        $row->save();
        session()->forget('booking_process_data');

        SendBookingConfirmationEmail::dispatch(auth()->user(), $booking);
        SendBookingInquiryEmail::dispatch($booking);
    }


}
