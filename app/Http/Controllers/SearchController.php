<?php

namespace App\Http\Controllers;

use App\Models\Tour;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $query = $request->input('query');

        $tours = Tour::with(['category:id,category_title,slug'])
            ->select('id', 'name', 'slug', 'image', 'description', 'category_id')
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', '%' . $query . '%')
                    ->orWhere('description', 'like', '%' . $query . '%')
                    ->orWhereHas('category', function ($catQuery) use ($query) {
                        $catQuery->where('category_title', 'like', '%' . $query . '%')
                            ->orWhere('slug', 'like', '%' . $query . '%');
                    });
            })
            ->whereStatus(1)
            ->limit(10)
            ->get()
            ->map(function ($tour) {
                return [
                    'id' => $tour->id,
                    'name' => $tour->name,
                    'slug' => $tour->slug,
                    'image' => $tour->image,
                    'description' => $tour->description,
                    'category' => $tour->category ? [
                        'id' => $tour->category->id,
                        'category_title' => $tour->category->category_title,
                        'slug' => $tour->category->slug
                    ] : null
                ];
            });

        // Return JSON for AJAX requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'tours' => $tours,
                'total' => $tours->count()
            ]);
        }

        // Return view for regular requests (existing functionality)
        return view('search', compact('tours'));
    }
}
