<?php

namespace App\Http\Controllers\Actions;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\Page;
use App\Models\Tour;
use Illuminate\Http\Request;
use Spatie\Sitemap\Sitemap;
use <PERSON>tie\Sitemap\Tags\Url;

class BuildSitemap extends Controller
{
    public function build()
    {
        Sitemap::create()
            ->add($this->build_index(Blog::active()->get(), 'sitemap_blogs.xml'))
            ->add($this->build_index(Tour::active()->get(), 'sitemap_tours.xml'))
            ->add($this->build_index(Page::active()
                ->whereNotIn('slug',['blogs','tours','news'])
                ->get(), 'sitemap_page.xml'))
            ->writeToFile(public_path('sitemap.xml'));
        $sitemapPath = public_path('sitemap.xml');
        $xmlContent = file_get_contents($sitemapPath);

        // XSLT reference line
        $xsltReference = '<?xml-stylesheet type="text/xsl" href="/sitemap.xsl"?>' . PHP_EOL;

        // Insert the XSLT reference after the XML declaration
        $xmlContentWithXSLT = str_replace('<?xml version="1.0" encoding="UTF-8"?>', '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL . $xsltReference, $xmlContent);

        // Save the modified XML
        file_put_contents($sitemapPath, $xmlContentWithXSLT);
    }

    protected function build_index($model, $path)
    {
        Sitemap::create()
            ->add($model)
            ->writeToFile(public_path($path));

        $sitemapPath = public_path($path);
        $xmlContent = file_get_contents($sitemapPath);

        // XSLT reference line
        $xsltReference = '<?xml-stylesheet type="text/xsl" href="/sitemap.xsl"?>' . PHP_EOL;

        // Insert the XSLT reference after the XML declaration
        $xmlContentWithXSLT = str_replace('<?xml version="1.0" encoding="UTF-8"?>', '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL . $xsltReference, $xmlContent);

        // Save the modified XML
        file_put_contents($sitemapPath, $xmlContentWithXSLT);

        return $path;
    }


}
