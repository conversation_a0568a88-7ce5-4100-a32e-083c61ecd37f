<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Razorpay\Api\Api;
use App\Models\Enquiry;
use App\Models\Inquiry;
use App\Models\WaysToGo;
use App\Rules\Recaptcha;
use App\Models\ContactUs;
use Spatie\Sitemap\Sitemap;
use Illuminate\Http\Request;
use Spatie\Sitemap\Tags\Url;
use App\Rules\IntPhoneNumber;
use App\Mail\AdminContactUsMail;
use App\Mail\InquiryReceivedMail;
use App\Mail\AdminAskQuestionMail;
use Illuminate\Support\Facades\Log;
use App\Mail\NewInquiryNotification;
use Foundation\Services\PageService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Foundation\Services\SettingService;
use App\Foundation\Services\BlogService;
use App\Foundation\Services\TourService;
use Illuminate\Support\Facades\Validator;
use App\Foundation\Services\BannerService;
use App\Foundation\Services\SliderService;
use App\Foundation\Services\PartnersService;
use App\Foundation\Services\WaysToGoService;
use Illuminate\Contracts\Support\Renderable;
use App\Foundation\Services\PopularTourService;
use App\Foundation\Services\TourCategoryService;

class HomeController extends Controller
{

    protected $blogService;
    protected $tourService;
    protected $tourCategoryService;
    protected $pageService;
    protected $sliderService;
    protected $settingService;
    protected $bannerService;
    protected $popularTourService;
    protected $partnersService;
    protected $waysToGoService;

    public function __construct(
        BlogService $blogService,
        TourCategoryService $tourCategoryService,
        TourService $tourService,
        PageService $pageService,
        SliderService $sliderService,
        SettingService $settingService,
        BannerService $bannerService,
        PopularTourService $popularTourService,
        PartnersService $partnersService,
        WaysToGoService $waysToGoService
    ) {
        $this->blogService = $blogService;
        $this->tourService = $tourService;
        $this->tourCategoryService = $tourCategoryService;
        $this->pageService = $pageService;
        $this->sliderService = $sliderService;
        $this->settingService = $settingService;
        $this->bannerService = $bannerService;
        $this->popularTourService = $popularTourService;
        $this->partnersService = $partnersService;
        $this->waysToGoService = $waysToGoService;
    }

    /**
     * Show the application dashboard.
     *
     * @return Renderable
     */
    public function index()
    {
        $data = [];
        $data['banners'] = $this->bannerService->getBannerList();
        $data['sliders'] = $this->sliderService->getSliderList();
        $data['blogs'] = $this->blogService->getBlogList(4);
        $data['tours'] = $this->tourService->getTourList();
        $data['meta'] = $this->settingService->getMetadata('home');
        $data['video'] = $this->settingService->getHomeVideo();
        $data['tourCategory'] = $this->tourCategoryService->getCategory();
        $data['popularTours'] = $this->popularTourService->filter();
        $data['partners'] = $this->partnersService->filter();
        $data['waysToGo'] = $this->waysToGoService->getHomePageList();
        return view('frontend.pages.index', compact('data'));
    }

    public function blogs()
    {
        $data = [];
        $data['blogs'] = $this->blogService->getBlogList();
        $data['recent_blogs'] = $this->blogService->getRecentBlogList();
        $data['meta'] = $this->settingService->getMetadata('blog');
        return view('frontend.pages.blogs', compact('data'));
    }
    public function tours($category = null)
    {
        $data = [];
        $data['category'] = $this->tourCategoryService->getBySlug($category);
        if (!$data['category']) {
            abort(404);
        }
        $data['tours'] = $this->tourService->getTourList($category);
        $data['meta'] = [
            'meta_title' => $data['category']?->meta_title,
            'meta_desc' => $data['category']?->meta_desc,
            'meta_keyword' => $data['category']?->meta_keyword,
        ];

        return view('frontend.pages.tours', compact('data'));
    }

    public function popularTours()
    {
        $data = [];
        $data['tours'] = $this->tourService->getPoplarList();
        $data['meta'] = $this->settingService->getMetadata('popular_tours');

        return view('frontend.pages.popular-tours', compact('data'));
    }

    public function blogDetail($slug)
    {
        $row = $this->blogService->getBlogBySlug($slug);
        $data['recent_blogs'] = $this->blogService->getRecentBlogList();
        return view('frontend.pages.blog_detail', compact('row', 'data'));
    }
    public function tourDetail($slug)
    {
        $row = Cache::remember('tour.detail.' . $slug, now()->addMinutes(60), function () use ($slug) {
            return $this->tourService->getTourBySlug($slug, ['gearCategories.gears']);
        });

        if (!$row) {
            abort(404);
        }

        $similarTours = Cache::remember('tour.similar.' . $row->id, now()->addMinutes(60), function () use ($row) {
            return $this->tourService->getSimilarTours($row->category_id, $row->id);
        });

        $reviews = $this->tourService->getPaginatedReviews($row->id, 5);

        return view('frontend.pages.tour_detail', compact('row', 'similarTours', 'reviews'));
    }

    /**
     * Load more reviews via AJAX
     *
     * @param Request $request
     * @param int $tourId
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMoreReviews(Request $request, $tourId)
    {
        $page = $request->get('page', 1);
        $perPage = 5;

        $reviews = $this->tourService->getPaginatedReviews($tourId, $perPage, $page);

        if ($request->ajax()) {
            $html = '';
            if ($reviews->count() > 0) {
                $html = view('frontend.pages.tour._review_items', ['reviews' => $reviews])->render();
            }

            return response()->json([
                'success' => true,
                'html' => $html,
                'hasMore' => $reviews->hasMorePages(),
                'nextPageUrl' => $reviews->nextPageUrl()
            ]);
        }

        return redirect()->back();
    }

    public function tourDetailNew($slug)
    {
        $row = $this->tourService->getTourBySlug($slug);
        if (!$row) {
            abort(404);
        }
        return view('frontend.pages.tour_detail_new', compact('row'));
    }

    public function waysToGo($slug)
    {
        $row = WaysToGo::where('slug', $slug)
            ->where('status', 1)
            ->with(['tours' => function ($query) {
                $query->where('status', 1)->orderBy('created_at', 'desc');
            }])
            ->first();
        if (!$row) {
            abort(404);
        }
        $data['meta'] = [
            'meta_title' => $row->meta_title,
            'meta_desc' => $row->meta_desc,
            'meta_keyword' => $row->meta_keyword,
        ];

        return view('frontend.pages.ways_to_go', compact('row', 'data'));
    }

    public function pages($slug)
    {
        $row = $this->pageService->getPageBySlug($slug);
        return view('frontend.pages.pages', compact('row'));
    }

    public function contactUs()
    {
        $data['meta'] = $this->settingService->getMetadata('contact');
        return view('frontend.pages.contact_us', compact('data'));
    }

    public function contactUsPost(Request $request)
    {
        $request->validate([
            'full_name' => 'required|max:25',
            'email' => 'required|email|max:255',
            'phone_no' => ['required', new IntPhoneNumber],
            'query' => 'required|max:255',
            'recaptcha_token' => ['required', new Recaptcha, 'exclude'],
        ]);

        $contactUs = ContactUs::create($request->all());

        if ($contactUs) {
            // send email to admin
            $contactUsEmails = get_email_receivers('contact_us');
            if (!empty($contactUsEmails)) {
                Mail::to($contactUsEmails)->queue(new AdminContactUsMail($contactUs));
            }
        }

        session()->flash('success', 'Your query has been sent successfully. We will get back to you shortly.');
        return redirect()->back();
    }

    public function postAskedQuestion(Request $request)
    {
        $request->validate([
            'name' => 'required|max:25',
            'email' => 'required|email|max:255',
            'phone' => ['required', new IntPhoneNumber],
            'message' => 'required|max:500',
            'tour_id' => 'required|exists:tours,id',
            'recaptcha_token' => ['required', new Recaptcha, 'exclude'],
        ]);

        $inquiry = Inquiry::create($request->all());

        if ($inquiry) {
            // send email to admin
            $askQuestionEmails = get_email_receivers('ask_question');
            if (!empty($askQuestionEmails)) {
                Mail::to($askQuestionEmails)->queue(new AdminAskQuestionMail($inquiry));
            }
        }

        session()->flash('success', 'Your query has been sent successfully. We will get back to you shortly.');
        return redirect()->back();
    }


    public function bookingTour(Request $request)
    {
        $tour = $this->tourService->findOrFail($request->get('tour_id'));

        if (!$tour) {
            abort(404);
        }

        $price  = $tour->adult_price * $request->get('adults') + $tour->child_price * $request->get('children');
        $data['adult_price'] = $tour->adult_price * $request->get('adults');
        $data['children_price'] = $tour->child_price * $request->get('children');
        if (usd_currency()) {
            $price  = $tour->usd_adult_price * $request->get('adults') + $tour->usd_child_price * $request->get('children');
            $data['adult_price'] = $tour->usd_adult_price * $request->get('adults');
            $data['children_price'] = $tour->usd_child_price * $request->get('children');
        }
        $dates = explode('to', $request->get('dates'));

        $data['total_price'] = $price;
        $data['dates'] = $request->get('dates');
        $data['adults'] = $request->get('adults');
        $data['children'] = $request->get('children');
        $data['start_date'] = $dates[0];
        $data['end_date'] = $dates[1];
        $data['tour'] = $request->get('tour_id');

        session()->put('booking_process_data', $data);
        session()->flash('success', 'Tour added to cart successfully');
        return redirect()->route('carts', ['tour' => $tour->slug]);
    }

    public function cartDetail(Request $request)
    {
        $data = [];
        $data['carts'] = session()->get('booking_process_data');
        if (!$data['carts']) {
            if ($request->tour) {
                return redirect()->route('tour.detail', ['slug' => $request->tour]);
            }
            return redirect()->route('home');
        }
        $data['tour'] = $this->tourService->findOrFail($data['carts']['tour']);
        return view('frontend.pages.cart', compact('data'));
    }

    public function checkout()
    {
        $data = [];
        $data['carts'] = session()->get('booking_process_data');
        if (!$data['carts']) {
            return redirect()->route('home');
        }
        $order = null;
        if (!usd_currency()) {
            $api = new Api(config('razor.razor_key_id'), config('razor.razor_key_secret'));
            $order = $api->order->create([
                'receipt'         => 'order_' . uniqid(),
                'amount'          => $data['carts']['total_price'] * 100, // Amount in paise, e.g., 500 = Rs 5.00
                'currency'        => 'INR',
            ]);
        }
        return view('frontend.pages.checkout', ['data' => $data['carts'], 'order' => $order]);
    }

    public function sitemap(Request $request)
    {
        $sitemap = Sitemap::create();

        $sitemap->add(Url::create(route('home'))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'))
            ->add(Url::create(route('contact-us'))->setPriority(1.0)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'))
            ->add(Url::create(route('tours'))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'))
            ->add(Url::create(route('blogs'))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'));

        $blogs = $this->blogService->filter();
        foreach ($blogs as $blog) {
            $sitemap->add(Url::create(route('blog.detail', $blog->slug))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'));
        }

        $pages = $this->pageService->filter();
        foreach ($pages as $page) {
            $sitemap->add(Url::create(route('page.detail', $page->slug))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'));
        }

        $tourCategories = $this->tourCategoryService->filter();
        foreach ($tourCategories as $tourCategory) {
            $sitemap->add(Url::create(route('tours', $tourCategory->slug))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'));
        }

        $tours = $this->tourService->filter();
        foreach ($tours as $tour) {
            $sitemap->add(Url::create(route('tour.detail', $tour->slug))->setPriority(0.6)->setLastModificationDate(Carbon::now())->setChangeFrequency('daily'));
        }

        return $sitemap->toResponse($request);
    }

    public function changeCurrency(Request $request)
    {
        $currency = $request->get('currency');
        session()->put('currency', $currency);
        session()->forget('booking_process_data');
        return response()->json(['success' => true]);
    }


    /*public function operator(Request $request)
    {
        return view('frontend.pages.operator');
    }

    public function dashboard(Request $request)
    {
        return view('frontend.pages.dashboard');
    }
    public function booking(Request $request)
    {
        return view('frontend.pages.booking');
    }
    public function bookingdetails(Request $request)
    {
        return view('frontend.pages.bookingdetails');
    }
    public function resetpassword(Request $request)
    {
        return view('frontend.pages.resetpassword');
    }*/

    public function inquiry()
    {
        $data['meta'] = $this->settingService->getMetadata('inquiry');
        $data['categories'] = $this->tourCategoryService->getCategory();
        $data['countries'] = config('countries');
        return view('frontend.pages.inquiry', compact('data'));
    }

    /**
     * Handle the inquiry form submission
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function inquiryPost(Request $request)
    {
        // Define validation rules
        $rules = [
            'first_name' => 'required|string|min:2|max:50',
            'last_name' => 'required|string|min:2|max:50',
            'email' => 'required|email|max:255',
            'phone_no' => ['required', new IntPhoneNumber],
            'country' => 'nullable|string|max:50',
            'travel_date' => 'nullable|date',
            'duration' => 'nullable|integer|min:1|max:365',
            'adults' => 'required|integer|min:1|max:100',
            'children' => 'nullable|integer|min:0|max:100',
            'tour_type' => 'nullable|string|max:50',
            'budget' => 'nullable|string|max:50',
            'query' => 'required|string|min:10|max:2000',
            'source' => 'nullable|string|max:50',
            'preferred_language' => 'nullable|string|max:255',
            'recaptcha_token' => ['required', new Recaptcha, 'exclude'],
        ];

        // Define custom validation messages
        $messages = [
            'first_name.required' => 'Please enter your first name',
            'first_name.min' => 'First name must be at least 2 characters',
            'last_name.required' => 'Please enter your last name',
            'last_name.min' => 'Last name must be at least 2 characters',
            'email.required' => 'Please enter your email address',
            'email.email' => 'Please enter a valid email address',
            'phone_no.required' => 'Please enter your phone number',
            'phone_no.min' => 'Phone number must be at least 7 digits',
            'adults.required' => 'Please specify the number of adults',
            'adults.min' => 'At least 1 adult is required',
            'query.required' => 'Please specify your travel requirements',
            'query.min' => 'Please provide more details about your requirements',
            'preferred_language.max' => 'Preferred language must be 255 characters or less',
        ];

        // Run the validator
        $validator = Validator::make($request->all(), $rules, $messages);

        // Check if validation fails
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }



        try {
            // Prepare data for the inquiry
            $inquiryData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_no' => $request->phone_no,
                'country' => $request->country,
                'travel_date' => $request->travel_date,
                'duration' => $request->duration,
                'adults' => $request->adults,
                'children' => $request->children,
                'tour_type' => $request->tour_type,
                'budget' => $request->budget,
                'query' => $request->get('query'),
                'source' => $request->source,
                'preferred_language' => $request->preferred_language,
                'status' => 'pending'
            ];


            // Create the inquiry record
            $inquiry = Enquiry::create($inquiryData);

            // Send email to admin
            $this->sendAdminNotification($inquiry);

            // Send thank you email to customer
            $this->sendCustomerNotification($inquiry);


            // Return success message
            return redirect()->back()->with('success', 'Thank you for your inquiry! We have received your details and will contact you shortly.');
        } catch (\Exception $e) {
            dd($e->getMessage());
            // Log the error
            Log::error('Inquiry form submission error: ' . $e->getMessage());

            // Return error message
            return redirect()->back()
                ->with('error', 'Sorry, there was a problem processing your inquiry. Please try again or contact us directly.')
                ->withInput();
        }
    }

    /**
     * Send notification email to admin
     *
     * @param Enquiry $inquiry
     * @return void
     */
    private function sendAdminNotification(Enquiry $inquiry)
    {
        try {
            // Send email to admin
            $inquiryEmails = get_email_receivers('inquiry');
            if (empty($inquiryEmails)) {
                Log::error('No valid admin email found for inquiry notifications.');
                return;
            }

            Mail::to($inquiryEmails)->queue(new NewInquiryNotification($inquiry));

            Log::info('Admin notification email sent successfully for inquiry #' . $inquiry->id);
        } catch (\Exception $e) {
            Log::error('Failed to send admin notification email: ' . $e->getMessage());
        }
    }

    /**
     * Send thank you email to customer
     *
     * @param Enquiry $inquiry
     * @return void
     */
    private function sendCustomerNotification(Enquiry $inquiry)
    {
        try {
            Mail::to($inquiry->email)
                ->send(new InquiryReceivedMail($inquiry));

            Log::info('Customer thank you email sent successfully for inquiry #' . $inquiry->id);
        } catch (\Exception $e) {
            Log::error('Failed to send customer thank you email: ' . $e->getMessage());
        }
    }
}
