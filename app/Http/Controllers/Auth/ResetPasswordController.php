<?php

namespace App\Http\Controllers\Auth;

use Foundation\Lib\Role;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    protected function redirectTo()
    {
        $user = auth()->user();

        if ($user->hasRole(Role::$current[Role::ROLE_CUSTOMER])) {
            return route('customer.dashboard'); // ✅ return string URL
        }

        return route('admin.dashboard.index'); // ✅ fallback
    }
}
