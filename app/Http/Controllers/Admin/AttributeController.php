<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\AttributesService;
use App\Models\Attributes;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;

class AttributeController extends BaseController
{
    use Image;

    protected AttributesService $attributesService;

    public function __construct(AttributesService $attributesService)
    {
        $this->attributesService = $attributesService;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->attributesService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'attributes';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->editColumn('image',function ($data){
                    return '<img src="'.get_image_full_url($data->image).'" alt="" width="75" height="75" />';
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status', 'image'])
                ->make(true);
        }

        return view('admin.attributes.index');
    }

    public function create()
    {
        return view('admin.attributes.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'image' => 'required|string',
            'status' => 'required|boolean'
        ]);

        $this->imageNameUrl($request);
        $this->attributesService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    public function show(Attributes $attribute)
    {
        $data = [];
        $data['row'] = $attribute;
        return view('admin.attributes.show', compact('data'));
    }

    public function edit(Attributes $attribute)
    {
        $data = [];
        $data['row'] = $attribute;
        return view('admin.attributes.edit', compact('data'));
    }

    public function update(Request $request, Attributes $attribute)
    {
        $request->validate([
            'name' => 'required|string',
            'image' => 'required|string',
            'status' => 'required|boolean'
        ]);

        $this->imageNameUrl($request);
        $this->attributesService->update($request->all(), $attribute);

        flash('success', 'Record successfully updated.');

        return $this->redirect($request);
    }

    public function destroy(Attributes $attribute)
    {
        $this->attributesService->delete($attribute);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.attributes.index');
    }

    public function sort(Request $request)
    {
        if($request->method() == 'POST') {
            $items = $request->get('list');

            foreach($items as $order => $id) {
                $this->attributesService->createOrUpdate(compact('order'), ['id' => $id]);
            }
        } else {
            $attributes = $this->attributesService->filter();

            return view('admin.attributes.sort', compact('attributes'));
        }
    }
    

    public function getAttributes(Request $request) 
    {

        checkAjax($request);

        $term = $request->get('term');

        $attributes = Attributes::when(isset($term['term']), function ($query, $term) {
            $query->where('name', 'like', '%' . $term . '%');
        })->get(['id', 'name as text', 'image']);

        return $this->responseOk([
            'attributes' => $attributes
        ]);
    }
}
