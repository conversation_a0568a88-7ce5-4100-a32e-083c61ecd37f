<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ItineraryActivity;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Ya<PERSON>ra\DataTables\DataTables;

class ItineraryActivityController extends BaseController
{
    use Image;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (request()->ajax()) {
            $data = ItineraryActivity::latest()->get();
            return DataTables::of($data)
                ->addIndexColumn()
                ->editColumn('image', function ($row) {
                    if ($row->image) {
                        return '<img src="' . get_image_full_url($row->image) . '" alt="' . $row->title . '" width="100" height="100">';
                    }
                })
                ->addColumn('action', function ($data) {
                    $model = "itinerary-activities";
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->rawColumns(['action', 'image'])
                ->make(true);
        }
        return view('admin.tour.itinerary-activities.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.tour.itinerary-activities.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'image' => 'nullable|string',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $this->imageNameUrl($request);

        ItineraryActivity::create($request->only(['image', 'title', 'description']));

        flash('success', 'Record successfully created.');

        return redirect()->route('admin.itinerary-activities.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $itineraryActivity = ItineraryActivity::findOrFail($id);
        return view('admin.tour.itinerary-activities.show', compact('itineraryActivity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {

        $itineraryActivity = ItineraryActivity::findOrFail($id);

        $data = [
            'row' => $itineraryActivity
        ];

        return view('admin.tour.itinerary-activities.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {

        $validated = $request->validate([
            'image' => 'nullable|string',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $this->imageNameUrl($request);

        $activity = ItineraryActivity::findOrFail($id);
        $activity->update($request->only(['image', 'title', 'description']));

        flash('success', 'Record successfully created.');

        return redirect()->route('admin.itinerary-activities.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $itineraryActivity = ItineraryActivity::findOrFail($id);
        $itineraryActivity->delete();

        return redirect()->route('admin.itinerary-activities.index')
            ->with('success', 'Itinerary Activity deleted successfully.');
    }



    public function getItineraryActivities(Request $request)
    {
        checkAjax($request);

        $term = $request->get('term');

        $itineraryActivities = ItineraryActivity::when(isset($term['term']), function ($q) use ($term) {
            $q->where('title', 'like', '%' . $term['term'] . '%');
        })->get(['id', 'title as text']);

        return $this->responseOk(['activities' => $itineraryActivities]);
    }

}
