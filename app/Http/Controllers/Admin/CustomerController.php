<?php

namespace App\Http\Controllers\Admin;

use App\Due;
use Carbon\Carbon;
use Foundation\Lib\paymentHistory\PaymentStatus;
use Foundation\Lib\Role;
use Foundation\Models\User;
use Foundation\Requests\Customer\StoreRequest;
use Foundation\Services\CustomerService;
use Foundation\Services\DueService;
use Foundation\Services\FollowUpService;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Foundation\Lib\DeliveryStatus;
use Foundation\Services\RoleService;
use Foundation\Services\UserService;
use Illuminate\Support\Arr;
use Neputer\Supports\BaseController;
use Foundation\DataTables\UserDatatable;

class CustomerController extends BaseController
{

    /**
     * The UserService instance
     *
     * @var $userService
     */
    private $userService;

    /**
     * @var RoleService
     */
    private $roleService;
    private $customerService;

    public function __construct(
        UserService     $userService,
        RoleService     $roleService,
        CustomerService $customerService)
    {
        $this->userService = $userService;
        $this->roleService = $roleService;
        $this->customerService = $customerService;
    }

    /**
     * @param Request $request
     * @return Factory|\Illuminate\View\View|mixed
     * @throws \Exception|\Throwable
     */
    public function index(Request $request)
    {
        $role = Role::$current[Role::ROLE_CUSTOMER];
        if ($request->ajax()) {
            return UserDatatable::get(
                $this->userService->filterByRole($request->only('search.value', 'filter'), $role), $role);
        }
        $data['roles'] = $this->roleService->getRoles();
        $data['status'] = $this->userService->getCountByStatus($role);
        return view('admin.customer.index', compact('data'));
    }

    /**
     * @param Request $request
     * @param User $user
     * @return Factory|\Illuminate\View\View
     * @throws \Exception
     */
    public function show(Request $request, User $user)
    {
        if ($request->ajax()) {
            return $this->getOrderListDatatables($request, $user);
        }
        $data['user'] = $user;
        return view('admin.customer.show', compact('data'));
    }

    public function getOrderListDatatables(Request $request, $user)
    {
        return datatables()
            ->of($this->userService->getOrderList($user->id, $request->input('search.value')))
            ->addColumn('order_id', function ($data) {
                return $data->order_id;
            })
            ->addColumn('quantity', function ($data) {
                return $data->quantity;
            })
            ->addColumn('amount', function ($data) {
                return $data->amount;
            })
            ->addColumn('status', function ($data) {
                return view('admin.order.partials.status', compact('data'))->render();
            })
            ->addColumn('created_at', function ($data) {
                return $data->created_at;
            })
            ->addColumn('updated_at', function ($data) {
                return $data->updated_at;
            })
            ->addColumn('checkbox', function ($data) {
                return view('admin.common.checkbox', compact('data'))->render();
            })
            ->addColumn('action', function ($data) {

                $model = 'order';
                return view('admin.customer.partials.customer-order-view-action', compact('data', 'model'))->render();
            })
            ->rawColumns(['status', 'checkbox', 'action'])
            ->make(true);
    }

    public function create()
    {
        $data = [];
        $data['roles'] = $this->roleService->getRoles();
        return view('admin.customer.create', compact('data'));
    }

    public function store(StoreRequest $request)
    {
        $this->customerService->new($request->validated());

        /* Create default password and assign role as customer */
        $customer = array_merge($request->validated(), ['password' => bcrypt(config('neputer.user.default_password'))]);
        $followUpCustomer = App(UserService::class)->firstOrCreate(['phone_number' => $customer['phone_number']], $customer);
        $customerId = getId(Role::getCustomer());
        $followUpCustomer->assignRole((array) $customerId);

        /* If customer has due then create followup for customer */
        if ($request->has('is_due')) {
            $totalAmount = Arr::get($request, 'due_amount');
            $dueClearanceDate = Arr::get($request, 'due_clearance_date');

            $remarks = "Due created by admin";
            $model = app(DueService::class)->createDue($totalAmount, $remarks, $request);

            App(FollowUpService::class)->createOrUpdateFollowUp($totalAmount, $totalAmount, $followUpCustomer->id, $model , $request->get('remarks'), $dueClearanceDate);

        }

        flash('success', 'Customer successfully created.');
        return $this->redirect($request);
    }
}
