<?php

namespace App\Http\Controllers\Admin\Appearance;

use Foundation\Lib\Category;
use Foundation\Lib\Nav;
use Foundation\Lib\PostType;
use Foundation\Services\CollectionService;
use Foundation\Services\NavService;
use Foundation\Services\PostService;
use Foundation\Services\ProductCollectionService;
use Foundation\Services\ProductService;
use Neputer\Supports\BaseController;
use Foundation\Services\ProductCategoryService;

/**
 * Class MenuController
 * @package App\Http\Controllers\Admin\Appearance
 */
class MenuController extends BaseController
{
    public function __invoke()
    {
        $data = [];
        $data['targets'] = Nav::getTargets();
        $data['menu-sections'] = Nav::getSections();
        return view('admin.appearance.menu.index', compact('data'));
    }

}
