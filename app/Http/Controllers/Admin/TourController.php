<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\PopularTourService;
use App\Foundation\Services\TourCategoryService;
use App\Foundation\Services\TourService;
use App\Foundation\Services\WaysToGoService;
use App\Foundation\Services\GearCategoryService;
use App\Models\Tour;
use App\Models\TourDay;
use App\Models\TourFaq;
use App\Models\TourMedia;
use Foundation\Requests\Tour\StoreTourRequest;
use Foundation\Requests\Tour\UpdateTourRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use LaravelIdea\Helper\App\Models\_IH_Tour_C;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class TourController extends BaseController
{
    use Image;
    /**
     * @var TourCategoryService
     */
    protected TourCategoryService $tourCategoryService;
    protected TourService $tourService;
    protected PopularTourService $popularTourService;
    protected WaysToGoService $waysToGoService;
    protected GearCategoryService $gearCategoryService;

    public function __construct(TourCategoryService $tourCategoryService,
                                TourService $tourService,
                                WaysToGoService $waysToGoService,
                                PopularTourService $popularTourService,
                                GearCategoryService $gearCategoryService
    )
    {
        $this->tourCategoryService = $tourCategoryService;
        $this->tourService = $tourService;
        $this->popularTourService = $popularTourService;
        $this->waysToGoService = $waysToGoService;
        $this->gearCategoryService = $gearCategoryService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->tourService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at', compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'tour';

                    $listRoute = route('admin.itineraries.index', $data->id);

                    $html = <<<EOF
                        <li style="margin-right: 5px">
                            <a href="$listRoute" title="List Itineraries"> <i
                                    class="fa fa-list btn btn-success btn-xs"></i> </a>
                        </li>
                    EOF;

                    $view = view('admin.common.data-table-action', compact('data', 'model', 'html'))->render();


                    return $view;
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->editColumn('category', function ($data) {
                    return $data->category?->category_title ?? 'N/A';
                })
                ->rawColumns(['action', 'created_at', 'status'])
                ->make(true);
        }

        return view('admin.tour.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        $data['category'] = $this->tourCategoryService->pluck();
        $data['waysToGo'] = $this->waysToGoService->pluck();
        $data['gear_categories'] = $this->gearCategoryService->pluck();
        return view('admin.tour.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTourRequest $request)
    {
        $request->merge([
            'slug' => str_slug($request->get('name')),
        ]);

        $this->sortItineraryItems($request);
        $this->kbItems($request);
        $this->imageNameUrl($request);
        $this->imageNameUrl($request, 'map_image');


        $tourMatrix = collect($request->only([
            'gallery_type',
            'gallery',
            'caption',
            'alt_text',
        ]));

        $attributeMatrix = collect($request->only([
            'attribute',
            'attribute_value',
        ]));

        $request->merge(['alt_text'=> $request->get('image_alt_text')]);
        try {
            DB::beginTransaction();
            $tour = $this->tourService->new($request->all());
            $this->updateFaqTour($request, $tour->id);

            if ($request->has('gear_categories')) {
                $syncData = [];
                foreach ($request->input('gear_categories') as $index => $categoryId) {
                    $syncData[$categoryId] = ['order' => $index + 1];
                }
                $tour->gearCategories()->sync($syncData);
            }

            $tourTranspose  = $tourMatrix->transpose();
            $attributeMatrix = $attributeMatrix->transpose();

            $this->updateTourGallery($tourTranspose, $tour);

            $attributeMatrix->each(function ($item, $key) use ($tour) {
              if($item[$key]){
                  $tour->attributes()->sync([$item[0] => ['value' => $item[1]]], false);
              }
            });
            DB::commit();

            flash('success', 'Record successfully created.');

        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }

        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(Tour $tour)
    {
        $data = [];
        $data['row'] = $tour;
        return view('admin.tour.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tour $tour)
    {
        $data = [];
        $data['row'] = $tour->load(['faqs', 'gallery', 'attributes', 'gearCategories']);
        $data['row']->short_itinerary = json_decode($data['row']->short_itinerary, true);
        $data['category'] = $this->tourCategoryService->pluck();
        $data['faqs'] = TourFaq::where('tour_id', $tour->id)->get()->groupBy('group_name');
        $data['waysToGo'] = $this->waysToGoService->pluck();
        $data['gear_categories'] = $this->gearCategoryService->pluck();
        return view('admin.tour.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTourRequest $request, Tour $tour)
    {

        if (!$tour->slug) {
            $request->merge(['slug' => str_slug($request->get('tour_title'))]);
        }

        $this->updateFaqTour($request, $tour->id);
        $this->imageNameUrl($request);
        $this->kbItems($request);
        $this->imageNameUrl($request, 'map_image');

        if ($request->has('gear_categories')) {
            $syncData = [];
            foreach ($request->input('gear_categories') as $index => $categoryId) {
                $syncData[$categoryId] = ['order' => $index + 1];
            }
            $tour->gearCategories()->sync($syncData);
        } else {
            $tour->gearCategories()->detach();
        }

        $tourMatrix = collect($request->only([
            'gallery_type',
            'gallery',
            'caption',
            'alt_text',
        ]));
        $request->merge(['alt_text'=> $request->get('image_alt_text')]);
        $this->tourService->update($request->all(), $tour);
        $tour->gallery()->delete();
        $tour->attributes()->detach();

        $attributeMatrix = collect($request->only([
            'attribute',
            'attribute_value',
        ]));

        $attributeMatrix = $attributeMatrix->transpose();

        $attributeMatrix->each(function ($item) use ($tour) {
            if (!$item[0]) {
                return;
            }
            $tour->attributes()->sync([$item[0] => ['value' => $item[1]]], false);
        });

        $tourTranspose  = $tourMatrix->transpose();

        $this->updateTourGallery($tourTranspose, $tour);

        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tour $tour)
    {
        $this->tourService->delete($tour);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.tour.index');
    }

    /**
     * @param Request $request
     * @return mixed
     */
    public function storeGallery(Request $request)
    {
        $tour = Tour::find($request->tour_id);
        $request->validate([
            'gallery.0' => 'required|string',
        ]);
        $tourMatrix = collect($request->only([
            'gallery_type',
            'gallery',
            'caption',
            'alt_text',
        ]));

        $tourTranspose  = $tourMatrix->transpose();

        $this->updateTourGallery($tourTranspose, $tour);
        flash('success', 'Image successfully added.');
        return redirect()->back();
    }

    public function deleteGallery($galleryId)
    {

        $gallery = TourMedia::find($galleryId);
        if ($gallery) {
            $gallery->delete();
            flash('success', 'Image successfully deleted.');
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }
    /**
     * @param Request $request
     * @return mixed
     */
    public function storeItinerary(Request $request)
    {
        $request->validate([
            'image' => 'required|string',
        ]);
        $this->imageNameUrl($request);
        $row = TourDay::create([
            'tour_id' => $request->tour_id,
            'day' => $request->day,
            'image' => $request->image,
            'detail' => $request->detail
        ]);
        $row->image =  get_image_full_url($row->image);
        flash('success', 'Itinerary successfully added.');
        return redirect()->back();
    }

    public function deleteItinerary($galleryId)
    {

        $gallery = TourMedia::find($galleryId);
        if ($gallery) {
            $gallery->delete();
            flash('success', 'Image successfully deleted.');
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }

    public function popular()
    {
        $populars = $this->popularTourService->filter();

        $tours = $this->tourService->getTour();

        return view('admin.tour.popular', compact('populars', 'tours'));
    }

    public function addPopular(Request $request)
    {
        $validated = $request->validate([
            'tour_id' => 'required|exists:tours,id|unique:popular_tours,tour_id',
        ], [], [
            'tour_id' => 'tour',
        ]);

        $validated['order'] = $this->popularTourService->maxOrder();

        //        dd($validated);

        $this->popularTourService->new($validated);

        flash('success', 'Record successfully created.');

        return redirect()->route('admin.tour.popular-list');
    }

    public function sortPopular(Request $request)
    {
        $items = $request->get('list');

        foreach ($items as $order => $id) {
            $this->popularTourService->createOrUpdate(compact('order'), ['tour_id' => $id]);
        }
    }

    public function destroyPopular($id)
    {
        $this->popularTourService->deleteById($id);

        flash('success', 'Record successfully deleted.');
    }

    private function sortItineraryItems(Request $request)
    {
        $list = [];

        if ($request->get('short_itinerary')) {
            foreach ($request->get('short_itinerary') as $itinerary) {

                $list[] = ['title' => $itinerary['title']];

            }

            $request->merge(['short_itinerary' => json_encode($list)]);
        }

    }

    private function kbItems(Request $request)
    {
        $list = [];

        $know_before = $request->get('know_before');

        if (!empty($know_before)) {
            foreach ($request->get('know_before') as $before) {
                $items = [];

                if (isset($before['items']) && is_array($before['items'])) {
                    foreach ($before['items'] as $item) {
                        $items[] = [
                            'title' => $item['title'],
                            'content' => $item['content']
                        ];
                    }
                }


                $list[] = [
                    'title' => $before['title'],
                    'items' => $items
                ];
            }
        }

        $request->merge(['know_before' => $list]);

        return $request;
    }

    private function updateFaqTour(Request $request, $id)
    {
        $tour = Tour::find($id);
        $tour->faqs()->delete();

        $tourFaqs = $request->get('tour_faq');

        if (is_array($tourFaqs)) {
            foreach ($tourFaqs as $faq) {
                $groupName = $faq['group_name'];
                foreach ($faq['items'] as $item) {
                    if ($groupName && $item['question'] && $item['answer']) {
                        TourFaq::updateOrCreate(
                            ['group_name' => $groupName, 'question' => $item['question'], 'tour_id' => $id],
                            [
                                'group_name' => $groupName,
                                'question' => $item['question'],
                                'answer' => $item['answer'],
                                'tour_id' => $id
                            ]
                        );
                    }

                }
            }
        }
    }

    /**
     * @param $tourTranspose
     * @param _IH_Tour_C|Tour|array|null $tour
     * @return void
     */
    public function updateTourGallery($tourTranspose, $tour)
    {
        $tourTranspose->each(function ($item) use ($tour) {
            $source = $item[0];
            $media = $item[1];


            if ($source === 'image') {
                $path = parse_url($media, PHP_URL_PATH);
                $media = str_replace('/storage', '', $path);
            }

            if ($media) {
                $tour->gallery()->create([
                    'source' => $source,
                    'media' => $media,
                    'caption' => $item[2],
                    'alt_text' => $item[3],
                ]);
            }
        });
    }
}
