<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\TourCategoryService;
use App\Models\TourCategory;
use Foundation\Requests\TourCategory\StoreTourCategoryRequest;
use Foundation\Requests\TourCategory\UpdateTourCategoryRequest;

use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class TourCategoryController extends BaseController
{
 use Image;
    /**
     * @var TourCategoryService
     */
    protected $tourCategoryService;

    public function __construct(TourCategoryService $tourCategoryService)
    {
        $this->tourCategoryService = $tourCategoryService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->tourCategoryService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'tour-category';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->editColumn('parent', function ($data) {
                  return $data->parent->category_title ?? 'N/A';
                })
                ->editColumn('category_title',function ($data){
                    return '<img src="'.get_image_full_url($data->image).'" alt="" width="75" height="75">'. $data->category_title .'</img>';
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status','category_title'])
                ->make(true);
        }

        return view('admin.tour-category.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        $data['category']= $this->tourCategoryService->pluck();
        return view('admin.tour-category.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTourCategoryRequest $request)
    {
        $request->merge(['slug' => str_slug($request->get('category_title'))]);
        $request->merge(['parent_id' => $request->get('parent') ?? 0]);
        $this->imageNameUrl($request);
         $this->tourCategoryService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(TourCategory $tourCategory)
    {
        $data = [];
        $data['row'] = $tourCategory;
        return view('admin.tour-category.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TourCategory $tourCategory)
    {
        $data = [];
        $data['row'] = $tourCategory;
        $data['category']= $this->tourCategoryService->pluck();
        return view('admin.tour-category.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTourCategoryRequest $request, TourCategory $tourCategory)
    {
        $request->merge(['parent_id' => $request->get('parent') ?? 0]);
        $this->imageNameUrl($request);
        $this->tourCategoryService->update($request->all(), $tourCategory);
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TourCategory $tourCategory)
    {
        $this->tourCategoryService->delete($tourCategory);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.tour-category.index');
    }
}
