<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gear;
use App\Models\GearCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class GearController extends Controller
{
    public function index()
    {
        $gears = Gear::with('category')->orderBy('order')->get();
        return view('admin.gears.index', compact('gears'));
    }

    public function create(Request $request)
    {
        $categories = GearCategory::orderBy('name')->get();
        $gear_category_id = $request->get('gear_category_id');
        $view = $request->ajax() ? 'admin.gears._form' : 'admin.gears.form';
        return view($view, compact('categories', 'gear_category_id'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name'             => 'required|string|max:255',
            'gear_category_id' => 'required|exists:gear_categories,id',
            'description'      => 'nullable|string',
            'status'           => 'boolean',
        ]);

        $data = $request->only(['name', 'gear_category_id', 'description', 'status']);
        $data['slug'] = Str::slug($request->name);
        $data['status'] = $request->status ?? true;

        $maxOrder = Gear::where('gear_category_id', $request->gear_category_id)->max('order');
        $data['order'] = $maxOrder + 1;

        $gear = Gear::create($data);

        if ($request->ajax()) {
            return response()->json(['success' => true, 'gear' => $gear]);
        }

        return redirect()->route('admin.packing-lists.index')
            ->with('success', 'Packing List created successfully');
    }

    public function edit(Request $request, Gear $gear)
    {
        $categories = GearCategory::orderBy('name')->get();
        if ($request->ajax()) {
            return view('admin.gears._form', compact('gear', 'categories'));
        }
        return view('admin.gears.form', compact('gear', 'categories'));
    }

    public function update(Request $request, Gear $gear)
    {
        $request->validate([
            'name'             => 'required|string|max:255',
            'gear_category_id' => 'required|exists:gear_categories,id',
            'description'      => 'nullable|string',
            'status'           => 'boolean',
        ]);

        $data = $request->only(['name', 'gear_category_id', 'description']);
        $data['slug'] = Str::slug($request->name);
        $data['status'] = $request->has('status');

        $gear->update($data);

        if ($request->ajax()) {
            return response()->json(['success' => true, 'gear' => $gear->fresh()]);
        }

        return redirect()->route('admin.packing-lists.index')
            ->with('success', 'Packing List updated successfully');
    }

    public function destroy(Request $request, Gear $gear)
    {
        $gear->delete();

        if ($request->ajax()) {
            return response()->json(['success' => true]);
        }

        return redirect()->route('admin.packing-lists.index')
            ->with('success', 'Packing List deleted successfully');
    }

    public function reorder(Request $request)
    {
        $order = $request->input('order');

        if ($order) {
            foreach ($order as $index => $id) {
                Gear::where('id', $id)->update(['order' => $index + 1]);
            }
        }

        return response()->json(['success' => true]);
    }
}
