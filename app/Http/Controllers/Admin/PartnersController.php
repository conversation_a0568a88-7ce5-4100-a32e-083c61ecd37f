<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\PartnersService;
use App\Models\Partner;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;

class PartnersController extends BaseController
{
    use Image;

    protected PartnersService $partnersService;

    public function __construct(PartnersService $partnersService)
    {
        $this->partnersService = $partnersService;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->partnersService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'partners';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->editColumn('image',function ($data){
                    return '<img src="'.get_image_full_url($data->image).'" alt="" width="75" height="75" />';
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status', 'image'])
                ->make(true);
        }

        return view('admin.partners.index');
    }

    public function create()
    {
        return view('admin.partners.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'image' => 'required|string',
            'link' => 'nullable|string',
            'status' => 'required|boolean'
        ]);

        $this->imageNameUrl($request);
        $this->partnersService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    public function show(Partner $partner)
    {
        $data = [];
        $data['row'] = $partner;
        return view('admin.partners.show', compact('data'));
    }

    public function edit(Partner $partner)
    {
        $data = [];
        $data['row'] = $partner;
        return view('admin.partners.edit', compact('data'));
    }

    public function update(Request $request, Partner $partner)
    {
        $request->validate([
            'name' => 'required|string',
            'image' => 'required|string',
            'link' => 'nullable|string',
            'status' => 'required|boolean'
        ]);

        $this->imageNameUrl($request);
        $this->partnersService->update($request->all(), $partner);

        flash('success', 'Record successfully updated.');

        return $this->redirect($request);
    }

    public function destroy(Partner $partner)
    {
        $this->partnersService->delete($partner);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.partners.index');
    }

    public function sort(Request $request)
    {
        if($request->method() == 'POST') {
            $items = $request->get('list');

            foreach($items as $order => $id) {
                $this->partnersService->createOrUpdate(compact('order'), ['id' => $id]);
            }
        } else {
            $partners = $this->partnersService->filter();

            return view('admin.partners.sort', compact('partners'));
        }
    }
}
