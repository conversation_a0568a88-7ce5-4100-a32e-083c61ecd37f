<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\InquiryService;
use App\Models\Inquiry;
use Illuminate\Http\Request;
use Neputer\Supports\Mixins\Image;
use Neputer\Supports\BaseController;
use Foundation\Services\UserService;
use Ya<PERSON>ra\DataTables\DataTables;

class AskQuestionController extends BaseController
{
    use Image;
    /**
     * @var UserService
     */
    public  $inquiryService;

    public function __construct(InquiryService $inquiryService)
    {
        $this->inquiryService = $inquiryService;

    }


    public function index(Request $request)
    {
        if ($request->ajax()){
            $rows = $this->inquiryService->filter($request->all());
            return DataTables::of($rows)
                ->editColumn('created_at', function ($data) {
                    return format_date_time($data->created_at);
                })
                ->addColumn('action', function ($data) {
                    $model = 'inquiry';
                    return view('admin.ask-question.partials.action', compact('data','model'))->render();
                })
                ->editColumn('status', function ($data) {
                    if ($data->status == 'pending') {
                        $html = '<span class="badge badge-warning">' . __(ucwords($data->status)) . '</span>';
                    } elseif ($data->status == 'completed') {
                        $html = '<span class="badge badge-success">' . __(ucwords($data->status)) . '</span>';
                    } else {
                        $html = '<span class="badge badge-danger">' . __(ucwords($data->status)) . '</span>';
                    }
                    return $html;
                })
                ->addColumn('tour_name', function ($data) {
                    return ($data->tour) ? '<a href="' . route('admin.tour.show', $data->tour_id) . '">' . $data->tour->name . '</a>' : 'N/A';
                })
                ->rawColumns(['created_at','action','status','tour_name'])
                ->make();
        }
        return view('admin.ask-question.index');

    }

    /**
     */
    public function update(Request $request,$askQuestionId)
    {
        $inquiry = Inquiry::find($askQuestionId);
        $inquiry->status = $request->status;
        $inquiry->save();

//       SendInquiryUpdateEmail::dispatch( $inquiry);

     flash('success', 'Record successfully updated.');

       return response()->json(['success' => true,'message' => 'Record successfully updated.'], 200);
    }

    public function detail($askQuestionId)
    {
        $data = [];
        $data['row'] = Inquiry::find($askQuestionId);

        return view('admin.ask-question.show', compact('data'));
    }

    public function delete($askQuestionId)
    {

        $row = Inquiry::find($askQuestionId);
        $cart = $row->cart;
        $cart->delete();
        $row->delete();
        session()->flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.ask-question.index');
    }
}
