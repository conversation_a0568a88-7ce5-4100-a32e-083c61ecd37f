<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogCategory;
use Foundation\Requests\BlogCategory\StoreBlogCategoryRequest;
use Foundation\Requests\BlogCategory\UpdateBlogCategoryRequest;
use Foundation\Services\BlogCategoryService;
use Foundation\Services\PermissionService;
use Foundation\Services\RoleService;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Yajra\DataTables\Exceptions\Exception;

class BlogCategoryController extends BaseController
{

    /**
     * @var BlogCategoryService
     */
    protected $blogCategoryService;

    public function __construct(BlogCategoryService $blogCategoryService)
    {
        $this->blogCategoryService = $blogCategoryService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->blogCategoryService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'blog-category';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status'])
                ->make(true);
        }

        return view('admin.blog-category.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        return view('admin.blog-category.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBlogCategoryRequest $request)
    {
        $request->merge(['slug' => str_slug($request->get('category_title'))]);

         $this->blogCategoryService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(BlogCategory $blogCategory)
    {
        $data = [];
        $data['row'] = $blogCategory;
        return view('admin.blog-category.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogCategory $blogCategory)
    {
        $data = [];
        $data['row'] = $blogCategory;
        return view('admin.blog-category.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBlogCategoryRequest $request, BlogCategory $blogCategory)
    {
        $this->blogCategoryService->update($request->all(), $blogCategory);
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlogCategory $blogCategory)
    {
        $this->blogCategoryService->delete($blogCategory);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.blog-category.index');
    }
}
