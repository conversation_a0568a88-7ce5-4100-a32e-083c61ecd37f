<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdatePageRequest;
use App\Models\Page;
use Foundation\Requests\Page\StoreRequest;
use Foundation\Services\PageService;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class PageController extends BaseController
{
    use Image;

    protected PageService $pageService;

    public function __construct( PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->pageService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'page';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->editColumn('image', function ($data) {
                    return '<img src="' . get_image_full_url($data->image) . '" width="100" />' ;
                })

                ->rawColumns([ 'action', 'created_at', 'status','image'])
                ->make(true);
        }

        return view('admin.page.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        return view('admin.page.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request)
    {
        $this->imageNameUrl($request);
        $request->merge(['slug'=>str_slug($request->page_name)]);
        $this->pageService->new($request->all());
        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(Page $page)
    {
        $data = [];
        $data['row'] = $page;
        return view('admin.page.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page)
    {
        $data = [];
        $data['row'] = $page;
        return view('admin.page.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePageRequest $request, Page $page)
    {

        $this->imageNameUrl($request);
        $this->pageService->update($request->all(), $page);
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page)
    {
        $this->pageService->delete($page);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.page.index');
    }
}
