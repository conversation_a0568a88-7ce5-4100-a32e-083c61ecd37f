<?php

namespace App\Http\Controllers\Admin\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class GetCustomFileManager extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, $field)
    {
        return view('admin.tour.itinerary.partials.input-gallery', [
            'field' => $field,
            'id' => $request->id
        ]);
    }
}
