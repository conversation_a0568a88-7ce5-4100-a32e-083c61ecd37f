<?php


namespace App\Http\Controllers\Admin\Actions;


use Foundation\Lib\Category;
use Foundation\Services\ProductCategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class CategoryAction
{
    /**
     * Show Quick edit categories page
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View
     */
    public function edit()
    {
        $data = app(ProductCategoryService::class)->pluckByType(Category::TYPE_GAMING_PRODUCT_CATEGORY)->sortBy('order');
        return view('gaming-product-category.order', compact('data'));
    }

}
