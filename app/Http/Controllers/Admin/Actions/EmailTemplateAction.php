<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Models\EmailCampaign;
use Foundation\Services\EmailTemplateService;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;

/**
 * Class EmailTemplateAction
 * @package App\Http\Controllers\Admin\Actions
 */
class EmailTemplateAction extends BaseController
{
    /**
     * @var EmailTemplateService
     */
    private $emailTemplateService;

    /**
     * EmailTemplateAction constructor.
     * @param EmailTemplateService $emailTemplateService
     */
    public function __construct(EmailTemplateService $emailTemplateService)
    {
        $this->emailTemplateService = $emailTemplateService;
    }

    /**
     * Get a Email Template with Patterns via AJAX.
     *
     * @param Request $request
     * @return mixed
     */
    public function get(Request $request)
    {
        $data = $this->emailTemplateService->findOrFail($request->get('template_id'));
        return response()->json($data);
    }

    /**
     * Get Email Template Preview
     *
     * @param EmailCampaign $campaign
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View
     */
    public function getPreview(EmailCampaign $campaign)
    {
        if($email = $campaign->email){
            $data['email'] = $email;
            return view('admin.email.templates.' . $email->email_markup, ['content' => $email->content, 'data']);
        }
        return response()->json([ 'msg' => 'Template not Created'], 400);
    }

}
