<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Services\EmailCampaignService;
use Illuminate\Http\Request;

class EmailCampaignAction
{
    /**
     * @var EmailCampaignService
     */
    private $service;

    public function __construct(EmailCampaignService $service)
    {
        $this->service = $service;
    }

    /**
     * Get All Clicks for a Campaign
     *
     * @param Request $request
     * @param $campaign
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Exception
     */
    public function getAllClicks(Request $request, $campaign)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->service->filterClicks($campaign, $request->input('search.value')))
                ->addColumn('link', function ($data) {
                    return "<a href={$data->link} target='_blank'> {$data->link} </a>";
                })
                ->addColumn('click_count', function ($data) {
                    return "<code> $data->click_count </code>";
                })
                ->rawColumns(['link', 'click_count', ])
                ->make(true);
        }

        return view('admin.campaign.email.link-clicks', compact('campaign'));
    }

    /**
     * Get Links Clicks Count by Individual User
     *
     * @param Request $request
     * @return array|string
     * @throws \Throwable
     */
    public function getClicksByUser(Request $request)
    {
        $links = $this->service->filterClicks($request->get('campaign_id'), $request->get('user_email'));
        return view('admin.campaign.partials.clicks-list', compact('links'))->render();
    }

}
