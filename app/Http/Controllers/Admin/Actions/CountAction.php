<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Models\User;
use Schema;
use Neputer\Config\Status;
use Illuminate\Http\Request;

/**
 * Class CountAction
 * @package App\Http\Controllers\Admin\Actions
 */
class CountAction
{

    /**
     * Return the count of models by status
     *
     * @param Request $request
     * @param $table
     * @return mixed
     */
    public function index(Request $request, $table)
    {
        $startDate = $this->getStartDate($request);

        if (Schema::hasTable($table)){
            $query = app('db')
                ->table($table)
                ->selectRaw('count(*) as total')
                ->selectRaw("count(case when status = '".Status::ACTIVE_STATUS."' then 1 end) as active")
                ->selectRaw("count(case when status = '".Status::INACTIVE_STATUS."' then 1 end) as inactive");

            if($startDate){
                $query->whereBetween('created_at', [ $startDate, now(), ]);
            }
            $all = $query->first();
        } else {
            return response()->json(['msg' => 'Error Getting Data'], 503);
        }

        $data['status'] = (array) $all;

        return $data;
    }

    /**
     * Get Count of Users by status
     *
     * @param Request $request
     * @return mixed
     */
    public function user(Request $request)
    {
        $startDate = $this->getStartDate($request);
        $query = User::selectRaw('count(*) as total')
            ->selectRaw("count(case when status = '".Status::ACTIVE_STATUS."' then 1 end) as active")
            ->selectRaw("count(case when status = '".Status::INACTIVE_STATUS."' then 1 end) as inactive");

        if($startDate){
            $query->whereBetween('created_at', [ $startDate, now(), ]);
        }

        if($role = $request->get('role')){
            $query->whereHas('roles', function ($query) use ($role){
                $query->where('slug', $role);
            });
        }
        $data['status'] = $query->first()->toArray();
        return $data;
    }

    /**
     * Get The starting created at date
     *
     * @param Request $request
     * @return \Illuminate\Support\Carbon|null
     */
    private function getStartDate(Request $request)
    {
        $startDate = null;
        if( $type = $request->get('type') ){
            switch ($type){
                case 2:
                    $startDate =now()->startOfDay();
                    break;
                case 3:
                    $startDate =now()->startOfWeek();
                    break;
                case 4:
                    $startDate =now()->startOfMonth();
                    break;
                case 5:
                    $startDate =now()->startOfYear();
                    break;
                default:
                    $startDate = null;
            }
        }

        return $startDate;
    }
}
