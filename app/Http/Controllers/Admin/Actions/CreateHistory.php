<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Services\PaymentHistoryService;

class CreateHistory
{
    /**
     * @param $amount
     * @param $model
     * @param $request
     * @param array|null $ids
     * @return void
     */
    public function __invoke($amount, $model, $request, array $ids = null)
    {
        app(PaymentHistoryService::class)->updateOrCreate($amount, $model, $request, $ids);
    }

}
