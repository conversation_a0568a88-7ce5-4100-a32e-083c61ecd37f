<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Lib\Category;
use Foundation\Lib\Order;
use Foundation\Lib\Product;
use Foundation\Services\OrderService;
use Illuminate\Http\Request;


/**
 * Class UpdateOrderStatus
 * @package App\Http\Controllers\Admin\Actions
 */
final class UpdateOrderStatus
{

    private $orderService;
    private $giftCardService;

    public function __construct(
        OrderService $orderService
    )
    {
        $this->orderService = $orderService;
    }

    public function __invoke(Request $request)
    {
        $update = false;
        $message = 'Cannot update the delivery status for the given order. Order quantity is not already fullfilled.';

        $order = $this->orderService->query()->find($request->get('pk'));
        if ($order) {
            if ($order->order_type === Product::PRODUCT_GIFT_CARD_INDEX && $order->transaction->product_type == Category::PRODUCT_TYPE_SYSTEM){
                if ($this->giftCardService->getGiftCardCode($order->id)->count() === $order->quantity)
                    $update = true;
            }
            elseif($order->transaction->product_type == Category::PRODUCT_TYPE_GAMING)
                $update = true;

        }
        else{
            $message = 'Order not Fount';
        }

        if ($update){
            $order->update(['delivery_status' => $request->get('value'),]);
            $message = 'You have successfully '. Order::$current[$request->get('value')] . ' the order.';
        }
        return response()
            ->json([
                'success' => $update,
                'message' => $message,
            ]);
    }

}
