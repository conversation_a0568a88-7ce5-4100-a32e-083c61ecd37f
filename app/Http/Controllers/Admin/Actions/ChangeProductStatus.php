<?php

namespace App\Http\Controllers\Admin\Actions;

use Foundation\Lib\Product;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;

/**
 * Class ChangeProductStatus
 * @package App\Http\Controllers\Admin\Actions
 */
final class ChangeProductStatus extends BaseController
{

    /**
     * @param Request $request
     * @return mixed
     */
    public function __invoke(Request $request)
    {
        dd($request->all());
        $status = false;

        $productSlug = $request->get('product-slug');
        $statusType  = $request->get('status-type');
        $statusValue  = $request->get('status-value');


        $builder = app('db')
            ->table('products')
            ->where('id', $request->get('product-id'));

        if ($statusType === 'status') {
            $status = $this->changeStatus( $builder, !$statusValue );
        }

        return $this->responseOk(
            $status,
            $status ? 'You have successfully updated the status.' : 'You have failed to update the status.'
        );
    }

    /**
     * Change status for the given product
     *
     * @param $builder
     * @param $status
     * @return mixed
     */
    private function changeStatus( $builder, $status )
    {
        return $builder->update([
            'status' => $status,
        ]);
    }

}
