<?php

namespace App\Http\Controllers\Admin\Actions;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Neputer\Supports\BaseController;

/**
 * Class BulkAction
 * @package App\Http\Controllers\Admin\Actions
 */
class DeleteBulkAction extends BaseController
{

    public function __invoke(string $model, Request $request)
    {
        if (Schema::hasTable($model)) {
            $ids = explode(',', $request->get('ids')); // Get IDs as an array

            $sanitizedIds = array_map(function ($id) {
                return intval($id);
            }, $ids);
            app('db')
                ->table($model)
                ->whereIn('id', $sanitizedIds)
                ->delete();
            flash('success', 'Records are deleted successfully !');
        } else {
            flash('error', 'Records could not be deleted !');
        }

        return redirect()->back();
    }

}
