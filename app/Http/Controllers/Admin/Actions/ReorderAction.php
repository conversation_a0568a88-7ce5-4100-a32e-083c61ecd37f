<?php


namespace App\Http\Controllers\Admin\Actions;


use Foundation\Lib\Category;
use Foundation\Services\ProductCategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class ReorderAction
{
    /**
     * Update Order
     *
     * @param Request $request
     * @param $model
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $model)
    {
        foreach ($request->get('item') as $id => $item){
            if (Schema::hasTable($model)) {
                app('db')->table($model)->where(['id' => $id])->update($item);
            }
        }
        return response()->json(['message' => ucwords(str_replace('_', ' ', $model)) . ' Order Updated']);
    }

}
