<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Foundation\Lib\Cache;
use Foundation\Requests\Setting\{UpdateRequest};
use Foundation\Services\SettingService;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;

/**
 * Class SettingController
 * @package App\Http\Controllers\Admin
 */
class SettingController extends BaseController
{
    use Image;

    /**
     * The SettingService instance
     *
     * @var SettingService $settingService
     */
    private SettingService $settingService;

    private string $folder = 'setting';

    public function __construct(SettingService $settingService)
    {
        $this->settingService = $settingService;
    }

    /**
     * Get the Settings Page with the data in the edit form
     *
     * @return Factory|View
     */
    public function edit()
    {
        $data = [];
        $data['settings'] = $this->settingService->getSettings();
        return view('admin.setting.edit', compact('data'));
    }

    /**
     * Update the website settings
     *
     * @param UpdateRequest $request
     * @return JsonResponse|RedirectResponse
     * @throws Exception
     */
    public function update(UpdateRequest $request)
    {
        $imageName = $this->settingService->pluckToArray();
        $image = $imageName['logo'] ?? null;
        $menuImage = $imageName['menu_image'] ?? null;
        $this->imageNameUrl($request, 'image');
        $this->imageNameUrl($request, 'footer_banner');
        $this->imageNameUrl($request, 'middle_banner');
        $this->imageNameUrl($request, 'blog_banner');
        $this->imageNameUrl($request, 'tour_banner');
        $this->imageNameUrl($request, 'contact_banner');
        $this->imageNameUrl($request, 'adventure_banner');
        $this->imageNameUrl($request, 'login_banner');
        $this->imageNameUrl($request, 'register_banner');
        $this->imageNameUrl($request, 'popular_tours_banner');

        if ($request->hasFile('photo')) {
            $image = $this->uploadImage($request->file('photo'), $this->folder, $image);
        }
        if ($request->hasFile('image')) {
            $menuImage = $this->uploadImage($request->file('image'), $this->folder, $menuImage);
        }

        $this->settingService->update($request->merge([
            'logo' => $image,
            'menu_image' => $menuImage,
        ])->except('_token', 'photo', 'image',));

        Cache::clear();

        if($request->ajax()) {
            return response()->json(['msg' => 'Setting Updated']);
        }

        flash('success', 'Record successfully updated.');
        return redirect()->route('admin.setting.edit');
    }

    public function updatePlanYourTrips(Request $request)
    {
        $request->validate([
            'section_title' => 'required',
            'section_subtitle' => 'required',
            'first_title' => 'required',
            'second_title' => 'required',
            'third_title' => 'required',

        ]);
        $this->settingService->updatePlanYourTrips($request->except('_token'));

        flash('success', 'Record successfully updated.');
        return redirect()->route('admin.plan-your-trips.edit');

   }

    public function editPlanYourTrips()
    {
        $row = get_site_config_value('plan_your_trips');
        $row = json_decode($row, true);
        return view('admin.setting.edit-plan-your-trips',compact('row'));
   }

}
