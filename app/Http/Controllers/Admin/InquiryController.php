<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\InquiryService;
use App\Models\Enquiry;
use Illuminate\Http\Request;
use Neputer\Supports\Mixins\Image;
use Neputer\Supports\BaseController;
use Yajra\DataTables\DataTables;

class InquiryController extends BaseController
{
    use Image;
    /**
     * @var UserService
     */
    public  $inquiryService;

    public function __construct(InquiryService $inquiryService)
    {
        $this->inquiryService = $inquiryService;

    }


    public function index(Request $request)
    {
        if ($request->ajax()){
            $rows = Enquiry::query()->latest();
            return DataTables::of($rows)
                ->editColumn('created_at', function ($data) {
                    return format_date_time($data->created_at);
                })
                ->addColumn('action', function ($data) {
                    $model = 'inquiry';
                    return view('admin.inquiry.partials.action', compact('data','model'))->render();
                })
                ->editColumn('status', function ($data) {
                    if ($data->status == 'pending') {
                        $html = '<span class="badge badge-warning">' . __(ucwords($data->status)) . '</span>';
                    } elseif ($data->status == 'completed') {
                        $html = '<span class="badge badge-success">' . __(ucwords($data->status)) . '</span>';
                    } else {
                        $html = '<span class="badge badge-danger">' . __(ucwords($data->status)) . '</span>';
                    }
                    return $html;
                })
                ->addColumn('full_name', function ($data) {
                    return $data->first_name . ' ' . $data->last_name;
                })
                ->addColumn('preferred_language', function ($data) {
                    return $data->preferred_language ?? 'N/A';
                })
                ->rawColumns(['created_at','action','status'])
                ->make();
        }
        return view('admin.inquiry.index');

    }

    /**
     */
    public function update(Request $request,$inquiryId)
    {
        $inquiry = Enquiry::find($inquiryId);


        if ($request->status){
            $oldStatus = $inquiry->status;
            $newStatus = $request->status;

            $inquiry->status = $newStatus;
            $inquiry->save();
            $inquiry->addCommunication(
                'status_change',
                "Status changed from '{$oldStatus}' to '{$newStatus}'",
                auth()->id()
            );


        }

        if ($request->admin_notes){
            $oldNotes = $inquiry->admin_notes;
            $newNotes = $request->admin_notes;

            $inquiry->admin_notes = $newNotes;
            $inquiry->save();
            if ($oldNotes !== $newNotes) {
                $inquiry->addCommunication(
                    'note',
                    "Admin notes updated",
                    auth()->id()
                );
            }
        }


     flash('success', 'Record successfully updated.');

       return response()->json(['success' => true,'message' => 'Record successfully updated.'], 200);
    }

    public function addCommunication(Request $request, $inquiryId)
    {
        $request->validate([
            'comm_type' => 'required|in:email,phone,meeting,other',
            'comm_details' => 'required|string|min:3',
        ]);

        $inquiry = Enquiry::findOrFail($inquiryId);

        $inquiry->addCommunication(
            $request->comm_type,
            $request->comm_details,
            auth()->id()
        );

        return response()->json(['success' => true]);
    }


    public function detail($inquiryId)
    {
        $data = [];
        $inquiry = Enquiry::findOrFail($inquiryId);

        $data = [
            'row' => $inquiry,
            'communications' => $inquiry->communications()
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->get(),
        ];


        return view('admin.inquiry.show', compact('data'));
    }

    public function delete($inquiryId)
    {

        $row = Enquiry::find($inquiryId);
        $cart = $row->cart;
        $cart->delete();
        $row->delete();
        session()->flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.inquiry.index');
    }
}
