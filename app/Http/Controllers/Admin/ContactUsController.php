<?php

namespace App\Http\Controllers\Admin;

use App\Models\ContactUs;
use Illuminate\Http\Request;
use Neputer\Supports\Mixins\Image;
use Neputer\Supports\BaseController;
use Foundation\Services\UserService;
use Yajra\DataTables\DataTables;

class ContactUsController extends BaseController
{
    use Image;
    /**
     * @var UserService
     */
    public  $inquiryService;



    public function index(Request $request)
    {
        if ($request->ajax()){
            $rows = ContactUs::latest();
            return DataTables::of($rows)
                ->editColumn('created_at', function ($data) {
                    return format_date_time($data->created_at);
                })
                ->addColumn('action', function ($data) {
                    $model = 'inquiry';
                    return view('admin.contact-us.partials.action', compact('data','model'))->render();
                })
                ->editColumn('status', function ($data) {
                    if ($data->status == '1') {
                        $html = '<span class="badge badge-warning">Pending</span>';
                    } elseif ($data->status == '2') {
                        $html = '<span class="badge badge-success">Completed</span>';
                    } else {
                        $html = '<span class="badge badge-danger">Canceled</span>';
                    }
                    return $html;
                })
                ->rawColumns(['created_at','action','status'])
                ->make();
        }
        return view('admin.contact-us.index');

    }

    /**
     */
    public function update(Request $request,$contactUs)
    {
        $inquiry = ContactUs::find($contactUs);
        $inquiry->status = $request->status;
        $inquiry->save();


     flash('success', 'Record successfully updated.');

       return response()->json(['success' => true,'message' => 'Record successfully updated.'], 200);
    }

    public function detail($contactUs)
    {
        $data = [];
        $data['row'] = ContactUs::find($contactUs);

        return view('admin.contact-us.show', compact('data'));
    }

    public function delete($contactUs)
    {

        $row = ContactUs::find($contactUs);
        $cart = $row->cart;
        $cart->delete();
        $row->delete();
        session()->flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.contact-us.index');
    }
}
