<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\BlogService;
use App\Http\Controllers\Controller;
use App\Models\Blog;
use Foundation\Requests\Blog\UpdateBlogRequest;
use Foundation\Requests\BlogCategory\StoreBlogCategoryRequest;
use Foundation\Requests\BlogCategory\UpdateBlogCategoryRequest;
use Foundation\Services\BlogCategoryService;
use Foundation\Services\PermissionService;
use Foundation\Services\RoleService;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class BlogController extends BaseController
{

    use Image;
    /**
     * @var BlogCategoryService
     */
    protected BlogCategoryService $blogCategoryService;
    protected BlogService $blogService;

    public function __construct(BlogCategoryService $blogCategoryService, BlogService $blogService)
    {
        $this->blogCategoryService = $blogCategoryService;
        $this->blogService = $blogService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->blogService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'blog';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status'])
                ->make(true);
        }

        return view('admin.blog.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        return view('admin.blog.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBlogCategoryRequest $request)
    {
        $request->merge(['slug' => str_slug($request->get('blog_title'))]);

        $this->imageNameUrl($request);
         $this->blogService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        $data = [];
        $data['row'] = $blog;
        return view('admin.blog.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $data = [];
        $data['row'] = $blog;
        return view('admin.blog.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBlogRequest $request, Blog $blog)
    {
        if (!$blog->slug){
            $request->merge(['slug' => str_slug($request->get('blog_title'))]);
        }
        $this->imageNameUrl($request);
        $this->blogService->update($request->all(), $blog);

        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        $this->blogService->delete($blog);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.blog.index');
    }
}
