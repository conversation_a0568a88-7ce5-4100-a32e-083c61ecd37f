<?php

namespace App\Http\Controllers\Admin;

use App\Due;
use Carbon\Carbon;
use Exception;
use Foundation\Lib\paymentHistory\PaymentStatus;
use Foundation\Lib\Role;
use Foundation\Models\User;
use Foundation\Requests\Operator\StoreRequest;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use App\Foundation\Services\OperatorService;
use Foundation\Services\RoleService;
use Foundation\Services\UserService;
use Illuminate\Support\Arr;
use Illuminate\View\View;
use Neputer\Supports\BaseController;
use Foundation\DataTables\UserDatatable;
use Neputer\Supports\Mixins\Image;
use Throwable;

class OperatorController extends BaseController
{
    use Image;
    /**
     * The UserService instance
     *
     * @var $userService
     */
    private $userService;

    /**
     * @var RoleService
     */
    private $roleService;
    private $operatorService;
    private $folder = 'user';

    public function __construct(
        UserService     $userService,
        RoleService     $roleService,
        OperatorService $operatorService)
    {
        $this->userService = $userService;
        $this->roleService = $roleService;
        $this->operatorService = $operatorService;
    }

    /**
     * @param Request $request
     * @return Factory|View|mixed
     * @throws Exception|Throwable
     */
    public function index(Request $request)
    {
        $role = Role::$current[Role::ROLE_OPERATOR];
        if ($request->ajax()) {
            return UserDatatable::get(
                $this->userService->filterByRole($request->only('search.value', 'filter'), $role), $role);
        }
        $data['roles'] = $this->roleService->getRoles();
        $data['status'] = $this->userService->getCountByStatus($role);
        return view('admin.operator.index', compact('data'));
    }

    /**
     * @param Request $request
     * @param User $user
     * @return Factory|View
     * @throws Exception
     */
    public function show(Request $request, User $user)
    {
        if ($request->ajax()) {
            return $this->getOrderListDatatables($request, $user);
        }
        $data['user'] = $user;
        return view('admin.operator.show', compact('data'));
    }

    public function getOrderListDatatables(Request $request, $user)
    {
        return datatables()
            ->of($this->userService->getOrderList($user->id, $request->input('search.value')))
            ->addColumn('order_id', function ($data) {
                return $data->order_id;
            })
            ->addColumn('quantity', function ($data) {
                return $data->quantity;
            })
            ->addColumn('amount', function ($data) {
                return $data->amount;
            })
            ->addColumn('status', function ($data) {
                return view('admin.order.partials.status', compact('data'))->render();
            })
            ->addColumn('created_at', function ($data) {
                return $data->created_at;
            })
            ->addColumn('updated_at', function ($data) {
                return $data->updated_at;
            })
            ->addColumn('checkbox', function ($data) {
                return view('admin.common.checkbox', compact('data'))->render();
            })
            ->addColumn('action', function ($data) {

                $model = 'order';
                return view('admin.operator.partials.operator-order-view-action', compact('data', 'model'))->render();
            })
            ->rawColumns(['status', 'checkbox', 'action'])
            ->make(true);
    }

    public function create()
    {
        $data = [];
        $data['roles'] = $this->roleService->getRoles();
        return view('admin.operator.create', compact('data'));
    }

    /**
     * @throws FileNotFoundException
     */
    public function store(StoreRequest $request)
    {
        $user = $this->userService->new($request->merge([
            'is_verified' => 1,
            'password' => bcrypt($request->get('password')),
            'image' => $request->has('photo') ? $this->uploadImage($request->file('photo'), $this->folder) : null,
        ])->all());

        if ($user) {
            $operatorId = getId(Role::getOperator());
            $user->assignRole((array) $operatorId);
        }

        flash('success', 'Operator successfully created.');
        return $this->redirect($request);
    }
}
