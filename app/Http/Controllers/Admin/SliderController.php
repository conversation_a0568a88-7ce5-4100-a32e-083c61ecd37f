<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\SliderService;
use App\Http\Controllers\Controller;
use App\Models\Slider;
use Foundation\Requests\Slider\UpdateSliderRequest;
use Foundation\Requests\Slider\StoreSliderRequest;
    ;
use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class SliderController extends BaseController
{
    use Image;

    protected SliderService $sliderService;

    public function __construct( SliderService $sliderService)
    {
        $this->sliderService = $sliderService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->sliderService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'slider';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->editColumn('image', function ($data) {
                    return '<img src="' . get_image_full_url($data->image) . '" width="100" />' ;
                })

                ->rawColumns([ 'action', 'created_at', 'status','image'])
                ->make(true);
        }

        return view('admin.slider.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        return view('admin.slider.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSliderRequest $request)
    {
        $this->imageNameUrl($request);
         $this->sliderService->new($request->all());
        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(Slider $slider)
    {
        $data = [];
        $data['row'] = $slider;
        return view('admin.slider.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Slider $slider)
    {
        $data = [];
        $data['row'] = $slider;
        return view('admin.slider.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSliderRequest $request, Slider $slider)
    {

        $this->imageNameUrl($request);
        $this->sliderService->update($request->all(), $slider);
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Slider $slider)
    {
        $this->sliderService->delete($slider);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.slider.index');
    }
}
