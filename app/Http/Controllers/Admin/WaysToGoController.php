<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\WaysToGoService;
use App\Models\WaysToGo;
use Foundation\Requests\WaysToGo\StoreWaysToGoRequest;
use Foundation\Requests\WaysToGo\UpdateWaysToGoRequest;

use Illuminate\Http\Request;
use Neputer\Supports\BaseController;
use Neputer\Supports\Mixins\Image;
use Yajra\DataTables\Exceptions\Exception;

class WaysToGoController extends BaseController
{
 use Image;
    /**
     * @var WaysToGoService
     */
    protected $waysToGoService;

    public function __construct(WaysToGoService $waysToGoService)
    {
        $this->waysToGoService = $waysToGoService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->waysToGoService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'ways-to-go';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->editColumn('title',function ($data){
                    return '<img src="'.get_image_full_url($data->image).'" alt="" width="75" height="75">'. $data->title .'</img>';
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', 'status','title'])
                ->make(true);
        }

        return view('admin.ways-to-go.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [];
        $data['category']= $this->waysToGoService->pluck();
        return view('admin.ways-to-go.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreWaysToGoRequest $request)
    {
        $request->merge(['slug' => str_slug($request->get('title'))]);
        $request->merge(['parent_id' => $request->get('parent') ?? 0]);
        $this->imageNameUrl($request);
         $this->waysToGoService->new($request->all());

        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(WaysToGo $waysToGo)
    {
        $data = [];
        $data['row'] = $waysToGo;
        return view('admin.ways-to-go.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WaysToGo $waysToGo)
    {
        $data = [];
        $data['row'] = $waysToGo;
        $data['category']= $this->waysToGoService->pluck();
        return view('admin.ways-to-go.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateWaysToGoRequest $request, WaysToGo $waysToGo)
    {
        $request->merge(['parent_id' => $request->get('parent') ?? 0]);
        $this->imageNameUrl($request);
        $this->waysToGoService->update($request->all(), $waysToGo);
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WaysToGo $waysToGo)
    {
        $this->waysToGoService->delete($waysToGo);
        flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.ways-to-go.index');
    }
}
