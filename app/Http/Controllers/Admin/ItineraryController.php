<?php

namespace App\Http\Controllers\Admin;

use App\Models\TourDay;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Tour;
use Neputer\Supports\Mixins\Image;

class ItineraryController extends Controller
{
    use Image;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, $id)
    {
        $itineraries = TourDay::where('tour_id', $id)->get();
        if ($request->ajax()) {
            return datatables()
                ->of($itineraries)
                ->addColumn('tour', function ($data) {
                    return $data->tour->name;
                })
                ->addColumn('action', function ($data) {
                    $model = 'itineraries';
                    return view('admin.tour.itinerary.partials.data-table-action', compact('data', 'model'));
                })
                ->editColumn('image', function ($data) {
                    if ($data->image) {
                        return '<img src="' . get_image_full_url($data->image) . '" alt="Image" width="50" height="50">';
                    }
                    return 'No Image';
                })
                ->rawColumns(['action', 'image'])
                ->make(true);
        }

        return view("admin.tour.itinerary.index", ['tour_id' => $id]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($id)
    {
        $tour_id = $id;

        return view("admin.tour.itinerary.create", compact('tour_id'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $id)
    {
        $request->merge([
            'images' => explode(",", $request->images)
        ]);

        $request->validate([
            'image' => 'nullable|string',
            'day' => 'required|string',
            'detail' => 'required|string',
            'itinerary_activities' => "sometimes|array",
            'gallery_type' => 'required|array',
            'gallery' => 'required|array',
            'caption' => 'nullable|sometimes|array',
            'alt_text' => 'nullable|sometimes|array',
            "attribute.*" => "required|string",
            'attribute_value.*' => "required|string"
        ]);

        $attributeMatrix = collect($request->only([
            'attribute',
            'attribute_value',
        ]));


        $itineraryMatrix = collect($request->only([
            'gallery_type',
            'gallery',
            'caption',
            'alt_text',
        ]));



        $itineraryTranspose  = $itineraryMatrix->transpose();

        $this->imageNameUrl($request, 'image');

        $itinerary = Tour::findOrFail($id)->itinerary()->create($request->except(['images', 'itinerary_activities']));

        $attributeTranspose  = $attributeMatrix->transpose();

        $attributeTranspose->map(function ($item) use ($itinerary) {
            $attribute = $item[0];
            $value = $item[1];

            $itinerary->attributes()->attach($attribute, [
                'value' => $value
            ]);
        });


        $itinerary->itineraryActivities()->sync($request->itinerary_activities);

        $itineraryTranspose->map(function ($item) use ($itinerary) {
            $source = $item[0];
            $media = $item[1];

            if ($source === 'image') {
                $path = parse_url($media, PHP_URL_PATH);
                $media = str_replace('/storage', '', $path);
            }

            $itinerary->itineraryMedia()->create([
                'source' => $source,
                'media' => $media,
                'caption' => $item[2],
                'alt_text' => $item[3],
            ]);
        });

        flash('success', 'Record successfully created.');

        return redirect()->route('admin.itineraries.index', $id);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id, string $itinerary)
    {

        $itinerary = TourDay::findOrFail($itinerary);
        return view('admin.tour.itinerary.show', compact('itinerary'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id, string $itinerary)
    {
        $itinerary = TourDay::with(['itineraryMedia', 'attributes'])->findOrFail($itinerary);

        $data = [
            'row' => $itinerary
        ];

        return view('admin.tour.itinerary.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id, string $itinerary)
    {

        $request->validate([
            'image' => 'nullable|string',
            'day' => 'sometimes|string',
            'detail' => 'sometimes|string',
            'itinerary_activities' => "sometimes|array",
            'gallery_type' => 'sometimes|array',
            'gallery' => 'sometimes|array',
            'caption' => 'sometimes|sometimes|array',
            'alt_text' => 'nullable|sometimes|array',
            'attribute.*' => "required|string",
            'attribute_value.*' => "required|string"
        ]);


        $itineraryMatrix = collect($request->only([
            'gallery_type',
            'gallery',
            'caption',
            'alt_text',
        ]));

        $itineraryTranspose  = $itineraryMatrix->transpose();

        $this->imageNameUrl($request, 'image');

        $itinerary = TourDay::findOrFail($itinerary);

        $itinerary->update($request->except(['itinerary_activities', 'gallery_type', 'gallery', 'caption', 'alt_text']));

        $itinerary->itineraryMedia()->delete();

        $itinerary->itineraryActivities()->sync($request->itinerary_activities);

        $itineraryTranspose->map(function ($item) use ($itinerary) {
            $source = $item[0];
            $media = $item[1];

            if ($source === 'image') {
                $path = parse_url($media, PHP_URL_PATH);
                $media = str_replace('/storage', '', $path);
            }

            $itinerary->itineraryMedia()->create([
                'source' => $source,
                'media' => $media,
                'caption' => $item[2],
                'alt_text' => $item[3],
            ]);
        });


        $attributeMatrix = collect($request->only([
            'attribute',
            'attribute_value',
        ]));

        $attributeTranspose  = $attributeMatrix->transpose();

        $itinerary->attributes()->detach();

        $attributeTranspose->map(function ($item) use ($itinerary) {
            $attribute = $item[0];
            $value = $item[1];

            $itinerary->attributes()->attach($attribute, [
                'value' => $value
            ]);
        });


        flash('success', 'Record successfully updated.');

        return redirect()->route('admin.itineraries.index', $id);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id, string $itinerary)
    {
        $itinerary = TourDay::findOrFail($itinerary);
        // Delete related itinerary_attributes
        $itinerary->attributes()->detach();
        $itinerary->delete();

        flash('success', 'Record successfully deleted.');
    }
}
