<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Booking;
use Foundation\Lib\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Foundation\Services\UserService;
use Neputer\Supports\BaseController;
use Foundation\Services\SettingService;

/**
 * Class DashboardController
 *
 * @package App\Http\Controllers\Admin
 */
final class DashboardController extends BaseController
{

    /**
     * @var UserService
     */
    private UserService $userService;

    private SettingService $settingService;

    /**
     * DashboardController constructor.
     * @param UserService $userService
     * @param SettingService $settingService
     */
    public function __construct(
        UserService $userService,
        SettingService $settingService
    )
    {
        $this->userService  = $userService;
        $this->settingService = $settingService;
    }

    public function index(Request $request)
    {
        // Get total users
        $totalUsers = User::count();

        // Get total active users
        $activeUsers = User::where('status', 1)->count();

        // Get recent users (last 30 days)
        $recentUsers = User::where('created_at', '>=', now()->subDays(30))->count();

        // Get available years (last 2 years including current year)
        $years = [];
        for ($i = 0; $i <= 2; $i++) {
            $years[] = now()->subYears($i)->year;
        }

        // Get selected year from request or use current year as default
        $selectedYear = $request->get('year') ?? now()->year;

        // Get user registration data for the selected year
        $registrationData = $this->getUserRegistrationData($selectedYear);

        // Get latest bookings
        $latestBookings = Booking::orderBy('created_at', 'desc')->take(10)->get();

        $data = [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'recent_users' => $recentUsers,
            'today' => now()->format('Y-m-d'),
            'last_month' => now()->subDays(30)->format('Y-m-d'),
            'years' => $years,
            'selected_year' => $selectedYear,
            'registration_data' => $registrationData,
            'latest_bookings' => $latestBookings
        ];

        return view('admin.dashboard', $data);
    }

    private function getUserRegistrationData($year)
    {
        // Get monthly registration data for the year
        $data = User::select(
            DB::raw("DATE_FORMAT(created_at, '%M') as month"),
            DB::raw("COUNT(*) as registrations")
        )
        ->whereYear('created_at', $year)
        ->groupBy('month')
        ->orderBy('created_at')
        ->get()
        ->toArray();

        // Fill in missing months with 0 registrations
        $months = ['January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December'];

        $completeData = [];
        foreach ($months as $month) {
            $monthData = array_first($data, function($item) use ($month) {
                return $item['month'] === $month;
            });

            $completeData[] = [
                'month' => $month,
                'registrations' => $monthData ? $monthData['registrations'] : 0
            ];
        }

        return $completeData;
    }

    public function getSummaryCharts(Request $request)
    {
        $data = [];

        $dailyDate = $request->get('dailyDate');
        $startDate = $request->get('startDate') ?? Carbon::now()->subDays(30);
        $endDate = $request->get('endDate') ?? Carbon::now();

        // Line Charts
        $data['customerStatisticsChart'] = $this->getUserLineChartData($this->userService->getUserForAPeriod($dailyDate, $startDate, $endDate, Role::$current[Role::ROLE_CUSTOMER]));
        return response()->json($data);
    }

    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');
            return redirect()
                ->back()
                ->with('notify', ['type' => 'success', 'response' => 'Application cache has been cleared successfully!']);
        } catch (\Exception $e) {
            Log::error('Cache clear failed: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('notify', ['type' => 'error', 'response' => 'Failed to clear cache. Please check the logs.']);
        }
    }

    private function getUserLineChartData($data)
    {
        $users = [];
        foreach ($data as $value){
            $item = [
                'period'    => $value->date,
                'phone'     => $value->total_user_verified_with_phone ?? 0,
                'email'     => $value->total_user_verified_with_email ?? 0,
                'both'      => $value->total_user_verified ?? 0,
                'none'      => $value->total_user_unverified ?? 0,
                'total'     => $value->total ?? 0,
            ];
            array_push($users, (object) $item);
        }
        return $users;
    }

}
