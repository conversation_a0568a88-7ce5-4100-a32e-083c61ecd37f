<?php

    namespace App\Http\Controllers\Admin;

    use App\Foundation\Requests\Slider\StoreBannerRequest;
    use App\Foundation\Services\BannerService;
    use App\Models\Banner;
    use Illuminate\Http\Request;
    use Illuminate\Support\Str;
    use Neputer\Supports\BaseController;
    use Neputer\Supports\Mixins\Image;

    class BannerController extends BaseController
    {
        use Image;

        protected BannerService $bannerService;

        public function __construct(BannerService $bannerService)
        {
            $this->bannerService = $bannerService;
        }

        public function index(Request $request)
        {
            if($request->ajax()) {
                return datatables()
                    ->of($this->bannerService->filter())
                    ->addColumn('created_at', function ($data) {
                        return view('admin.common.created-at',compact('data'))->render();
                    })
                    ->addColumn('action', function ($data) {
                        $model = 'banner';

                        return view('admin.common.data-table-action', compact('data', 'model'))->render();
                    })
                    ->addColumn('status', function ($data) {
                        return view('admin.common.status', compact('data'))->render();
                    })
                    ->addColumn('layout', function ($data) {
                        return "<img src='" . asset("assets/img/layout-" . Str::slug($data->layout, '-') .".png") ."' style='width: auto; height: 50px' />";
                    })
                    ->rawColumns([ 'action', 'created_at', 'status', 'layout'])
                    ->make(true);
            }

            return view('admin.banner.index');
        }

        public function create()
        {
            $data = [
                'layout' => '1'
            ];

            return view('admin.banner.create', compact('data'));
        }

        public function store(StoreBannerRequest $request)
        {
            $this->imageNameUrl($request, 'images', true);
            $this->bannerService->new($request->all());

            flash('success', 'Record successfully created.');
            return $this->redirect($request);
        }

        public function show(Banner $banner)
        {
            $data = $banner;

            return view('admin.banner.show', compact('data'));
        }

        public function edit(Banner $banner)
        {
            $data = $banner;

            return view('admin.banner.edit', compact('data'));
        }

        public function update(Request $request, Banner $banner)
        {
            $this->imageNameUrl($request, 'images', true);
            $this->bannerService->update($request->all(), $banner);

            flash('success', 'Record successfully updated.');
            return $this->redirect($request);
        }

        public function destroy(Banner $banner)
        {
            $this->bannerService->delete($banner);

            flash('success', 'Record successfully deleted.');
            return redirect()->route('admin.banner.index');
        }
    }
