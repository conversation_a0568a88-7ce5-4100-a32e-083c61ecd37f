<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GearCategory;
use App\Foundation\Services\GearCategoryService;
use Foundation\Services\PageService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Neputer\Supports\Mixins\Image;

class GearCategoryController extends Controller
{
    use Image;

    protected GearCategoryService $GearCategoryService;

    public function __construct( GearCategoryService $GearCategoryService)
    {
        $this->GearCategoryService = $GearCategoryService;
    }

    public function index(Request $request)
    {
        if ($request->get('all')) {
            return response()->json(['data' => GearCategory::orderBy('order')->get()]);
        }

        if ($request->ajax()) {
            return datatables()
                ->of($this->GearCategoryService->filter())
                ->addColumn('created_at', function ($data) {
                    return view('admin.common.created-at',compact('data'))->render();
                })
                ->addColumn('action', function ($data) {
                    $model = 'packing-list-categories';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                    return view('admin.common.status', compact('data'))->render();
                })
                ->editColumn('image', function ($data) {
                    return '<img src="' . get_image_full_url($data->image) . '" width="100" />' ;
                })
                ->addColumn('order', function ($data) {
                    return $data->order;
                })

                ->rawColumns([ 'action', 'created_at', 'status','image'])
                ->make(true);
        }

        return view('admin.gear-categories.index');
    }

    public function create()
    {
        return view('admin.gear-categories.form');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name'   => 'required|string|max:255',
            'status' => 'boolean',
            'image'  => 'nullable|string',
        ]);

        $data = $request->only(['name', 'status', 'image']);
        if ($request->filled('image')) {
            $data['image'] = str_replace(url('/storage') . '/', '', $request->image);
        }
        $data['slug'] = Str::slug($request->name);
        $data['status'] = $request->status ?? true;

        $maxOrder = GearCategory::max('order');
        $data['order'] = $maxOrder + 1;

        GearCategory::create($data);

        return redirect()->route('admin.packing-list-categories.index')
            ->with('success', 'Packing List Category created successfully');
    }

    public function show(GearCategory $packing_list_category)
    {
        $gearCategory = $packing_list_category;
        return view('admin.gear-categories.show', compact('gearCategory'));
    }

    public function edit(Request $request, $id)
    {
        $gearCategory = GearCategory::findOrFail($id);
        return view('admin.gear-categories.form', compact('gearCategory'));
    }

    public function update(Request $request, GearCategory $packing_list_category)
    {
        $request->validate([
            'name'   => 'required|string|max:255',
            'status' => 'boolean',
            'image'  => 'nullable|string',
        ]);

        $data = $request->only(['name', 'status', 'image']);
        if ($request->filled('image')) {
            $data['image'] = str_replace(url('/storage') . '/', '', $request->image);
        }
        $data['slug'] = Str::slug($request->name);

        $packing_list_category->update($data);

        return redirect()->route('admin.packing-list-categories.index')
            ->with('success', 'Packing List Category updated successfully');
    }

    public function destroy(GearCategory $packing_list_category)
    {
        $packing_list_category->delete();
        return redirect()->back()->with('success', 'Packing List Category has been deleted successfully!');
    }

    public function reorder(Request $request)
    {
        $request->validate([
            'order' => 'required|array',
        ]);

        foreach ($request->order as $index => $id) {
            GearCategory::where('id', $id)->update(['order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    public function getGears(GearCategory $gear_category)
    {
        return response()->json($gear_category->gears()->get(['name']));
    }
}
