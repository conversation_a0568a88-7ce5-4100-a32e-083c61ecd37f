<?php

namespace App\Http\Controllers;

use App\Jobs\SendBookingConfirmationEmail;
use App\Jobs\SendBookingInquiryEmail;
use App\Models\Booking;
use App\Models\Cart;
use App\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Charge;
use Stripe\Checkout\Session;
use Stripe\Stripe;
use Stripe\StripeClient;

class PaymentController extends Controller
{
    public function payViaStripe(Request $request)
    {
        Stripe::setApiKey(config('stripe.stripe_secret_key'));

        $user = auth()->user();
        $data = $request->all();
        $cart =  session()->get('booking_process_data');

        $row = Cart::create([
            'user_id' => auth()->user()->id,
            'tour_id' => $cart['tour'],
            'price' => $cart['total_price'],
            'start_date' => $cart['start_date'],
            'end_date' => $cart['end_date'],
            'adult' => $cart['adults'],
            'child' => $cart['children'],
            'status' => 'pending',
            'order_status' => 'pending',
            'type' => 'tour',
            'currency' => 'USD',
        ]);

        if (!$row) {
            return redirect()->back()->with('error', 'Your cart is empty.');
        }

        $totalAmount = $row->price;

        try {
            $charge = Charge::create([
                'amount' => $row->price * 100, // Amount in cents
                'currency' => 'USD',
                'source' => $request->stripeToken,
                'description' => 'Payment for tour: ' . $row->tour?->name,
            ]);
            $this->bookingPost($user, $row, $charge);

            return redirect()->route('payment.success')->with('success', 'Payment successful and booking confirmed!');
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return redirect()->back()->with('error', 'Error! ' . $e->getMessage());
        }
    }

    public function paymentSuccess()
    {
        return view('frontend.pages.payment_success');
    }

    public function stripeCheckout(Request $request)
    {
        $stripe = new StripeClient(config('stripe.stripe_secret_key'));

        $redirectUrl = route('stripe.checkout.success') . '?session_id={CHECKOUT_SESSION_ID}';

        $user = auth()->user();
        $cart =  session()->get('booking_process_data');

        $row = Cart::create([
            'user_id' => auth()->user()->id,
            'tour_id' => $cart['tour'],
            'price' => $cart['total_price'],
            'start_date' => $cart['start_date'],
            'end_date' => $cart['end_date'],
            'adult' => $cart['adults'],
            'child' => $cart['children'],
            'status' => 'pending',
            'order_status' => 'pending',
            'type' => 'tour',
            'currency' => 'USD',
        ]);

        if (!$row) {
            return redirect()->back()->with('error', 'Your cart is empty.');
        }

        try {
            $response = $stripe->checkout->sessions->create([
                'success_url' => $redirectUrl,

                'customer_email' => $user->email,

                'payment_method_types' => ['card'],

                'line_items' => [
                    [
                        'price_data' => [
                            'product_data' => [
                                'name' => 'Payment for tour: ' . $row->tour?->name,
                            ],
                            'unit_amount' => $row->price * 100,
                            'currency' => 'USD',
                        ],
                        'quantity' => 1
                    ],
                ],

                'mode' => 'payment',
                'allow_promotion_codes' => true,
            ]);

            return redirect($response['url']);
        } catch (\Throwable $th) {
            Log::error('Stripe Error: ' . $th->getMessage());
            return redirect()->back()->with('error', 'Payment failed');
        }
    }

    public function stripeCheckoutSuccess(Request $request)
    {
        $stripe = new StripeClient(config('stripe.stripe_secret_key'));

        $response = $stripe->checkout->sessions->retrieve($request->session_id);

        $user = auth()->user();
        $cart =  session()->get('booking_process_data');

        $row = Cart::create([
            'user_id' => auth()->user()->id,
            'tour_id' => $cart['tour'],
            'price' => $cart['total_price'],
            'start_date' => $cart['start_date'],
            'end_date' => $cart['end_date'],
            'adult' => $cart['adults'],
            'child' => $cart['children'],
            'status' => 'pending',
            'order_status' => 'pending',
            'type' => 'tour',
            'currency' => 'USD',
        ]);
        if ($response->status === 'complete') {
            $this->bookingPost($user, $row, $response);
        }
        return view('frontend.pages.payment_success');
    }

    /**
     * @param Authenticatable|User|null $user
     * @param Cart $row
     * @param Session $response
     * @return void
     */
    public function bookingPost(User|Authenticatable|null $user, Cart $row, Session $response)
    {
        $booking = Booking::create([
            'user_id' => $user->id,
            'tour_id' => $row->tour_id,
            'cart_id' => $row->id,
            'total_price' => $row->price,
            'status' => 'completed', //payment status
            'order_status' => 'Pending',
            'payment_gateway' => 'stripe',
            'payment_meta' => $response,
        ]);

        $row->status = 'completed';
        $row->order_status = 'Pending';
        $row->save();
        session()->forget('booking_process_data');

        SendBookingConfirmationEmail::dispatch(auth()->user(), $booking);
        SendBookingInquiryEmail::dispatch($booking);
    }
}
