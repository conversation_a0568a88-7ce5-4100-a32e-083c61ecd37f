<?php

namespace App\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Foundation\Lib\Trial\TrialStatus;
use Illuminate\Http\Request;

class TrialSession
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Carbon::parse(config('trialSession.ending_time')) < now()) {
            \Auth::guard()->logout();
           return response()->view('trial.expired');
        }
        elseif (TrialStatus::checkTrialSession()) {
            if (! $request->expectsJson()) {
                return $next($request);
            }
        }

        return response()->view('trial.end-trial');
    }
}
