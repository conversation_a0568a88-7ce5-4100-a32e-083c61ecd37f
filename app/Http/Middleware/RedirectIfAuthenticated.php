<?php

namespace App\Http\Middleware;

use Closure;
use Foundation\Lib\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Providers\RouteServiceProvider;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {
            if (str_contains($request->url(), 'game')) // If URL Contains 'game', redirect to gaming home
                return redirect()->route('gaming.home');
            else {
                if ($request->user()->hasRole(Role::$current[Role::ROLE_SUPER_ADMIN])) {
                    return redirect(RouteServiceProvider::HOME);
                } else if ($request->user()->hasRole(Role::$current[Role::ROLE_CUSTOMER])) {
                    return redirect(RouteServiceProvider::Customer);
                } else {
                    return redirect('/');
                }
            }
        }

        return $next($request);
    }
}
