<?php

namespace App\Http\Traits;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

/**
 * Trait ImageUploadable
 * @package App\Http\Traits
 */
trait ImageUploadable
{
    /**
     * @param string $path
     * @param string $disk
     */
    public function uploadAndResizeImage(string $path, string $disk = 'public'): void
    {
        $image = Image::make(Storage::disk($disk)->get($path));

        // Create a WebP version of the original image
        $webpPath = pathinfo($path, PATHINFO_DIRNAME) . '/' . pathinfo($path, PATHINFO_FILENAME) . '.webp';
        Storage::disk($disk)->put($webpPath, (string) $image->encode('webp', 80));

        // Define sizes for resizing
        $sizes = [
            'sm' => 320,
            'md' => 768,
            'lg' => 1200,
        ];

        // Create and save resized versions
        foreach ($sizes as $key => $width) {
            $resizedPath = pathinfo($path, PATHINFO_DIRNAME) . '/' . pathinfo($path, PATHINFO_FILENAME) . '-' . $key . '.' . pathinfo($path, PATHINFO_EXTENSION);

            $resizedImage = clone $image;
            $resizedImage->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });

            Storage::disk($disk)->put($resizedPath, (string) $resizedImage->encode(null, 75));
        }
    }
}
