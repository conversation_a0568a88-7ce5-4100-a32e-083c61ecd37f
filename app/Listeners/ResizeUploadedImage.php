<?php

namespace App\Listeners;

use App\Http\Traits\ImageUploadable;
use Unisharp\Laravelfilemanager\Events\ImageWasUploaded;

class ResizeUploadedImage
{
    use ImageUploadable;

    /**
     * Handle the event.
     *
     * @param  ImageWasUploaded  $event
     * @return void
     */
    public function handle(ImageWasUploaded $event)
    {
        $this->uploadAndResizeImage($event->path());
    }
}
