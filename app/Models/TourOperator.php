<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TourOperator extends Model
{
   protected $fillable = [
       'tour_id', 'operator_id', 'adult', 'usd_adult', 'child', 'usd_child',
   ];

    public function tours()
    {
        return $this->belongsTo(Tour::class, 'tour_id');
   }

    public function operators()
    {
        $this->belongsTo(Operator::class, 'operator_id');
   }
}
