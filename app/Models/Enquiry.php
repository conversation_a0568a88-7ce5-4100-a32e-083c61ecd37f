<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Enquiry extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone_no',
        'country',
        'travel_date',
        'duration',
        'adults',
        'children',
        'tour_type',
        'budget',
        'query',
        'source',
        'preferred_language',
        'status',
        'admin_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'travel_date' => 'date',
        'adults' => 'integer',
        'children' => 'integer',
        'duration' => 'integer',
    ];

    /**
     * Get the full name of the inquiry contact.
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Scope a query to only include pending inquiries.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include in-progress inquiries.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope a query to only include completed inquiries.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include cancelled inquiries.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function communications(): HasMany
    {
        return $this->hasMany(InquiryCommunication::class);
    }

    /**
     * Add a new communication record for this inquiry.
     *
     * @param string $type
     * @param string $details
     * @param int|null $userId
     * @return InquiryCommunication
     */
    public function addCommunication(string $type, string $details, ?int $userId = null): InquiryCommunication
    {
        return $this->communications()->create([
            'type' => $type,
            'details' => $details,
            'user_id' => $userId ?? auth()->id(),
        ]);
    }

}
