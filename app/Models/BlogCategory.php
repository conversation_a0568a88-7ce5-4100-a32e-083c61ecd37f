<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_title',
        'slug',
        'description',
        'image_id',
        'image',
        'status',
        'is_featured',
        'sort_order',
        'meta_title',
        'meta_keyword',
        'meta_desc',
    ];

    public function blog()
    {
        return $this->hasMany(Blog::class, 'blog_category_id', 'id');
    }
}
