<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WaysToGo extends Model
{
    use HasFactory;

    protected $table = 'ways_to_go';

    protected $fillable = [
        'title',
        'slug',
        'short_description',
        'description',
        'image',
        'status',
        'meta_title',
        'meta_keyword',
        'meta_desc',
        'alt_text',
        'image_caption',
        'sort_order',
    ];

    public function tours()
    {
        return $this->hasMany(Tour::class, 'waysToGo_id');
    }
}
