<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Page extends Model implements Sitemapable
{
    protected $fillable = [
        'page_name','slug','page_heading','page_content','image','status','meta_title','meta_desc','meta_keyword',
        'alt_text',
        'image_caption'];

    public function scopeActive()
    {
        return $this->where('status', 1);
    }

    public function toSitemapTag(): Url|string|array
    {
        return Url::create(route('page.detail', $this->slug))
            ->setPriority(0.6)
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY);
    }
}
