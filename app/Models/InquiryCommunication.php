<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InquiryCommunication extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'enquiry_id',
        'user_id',
        'type',
        'details',
    ];

    /**
     * Get the inquiry that owns the communication.
     */
    public function inquiry(): BelongsTo
    {
        return $this->belongsTo(Enquiry::class);
    }

    /**
     * Get the user who created the communication.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
