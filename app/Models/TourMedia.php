<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TourMedia extends Model
{
    protected $fillable = [
        'tour_id',
        'image',
        'alt_text',
        'caption',
        'source',
        'media',
        'status',
        'sort_order',
    ];

    public function tour()
    {
        return $this->belongsTo(Tour::class, 'tour_id', 'id');
    }
}
