<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'tour_id',
        'user_id',
        'cart_id',
        'intents_id',
        'checkout_indents_id',
        'order_status',
        'status', //payment status
        'total_price',
        'meta',
        'payment_gateway',
        'payment_meta',
    ];

    protected $casts = [
        'meta' => 'array',
        'payment_meta' => 'array',
    ];

    public function tour()
    {
        return $this->belongsTo(Tour::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }
}
