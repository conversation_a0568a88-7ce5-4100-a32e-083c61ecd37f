<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inquiry extends Model
{
   protected $fillable = [
       'name',
       'email',
       'phone',
       'how_did_you_hear', 
       'preferred_language', 
       'is_read', 
       'message', 
       'status',
       'tour_id'
   ];


    public function tour()
    {
        return $this->belongsTo(Tour::class);
   }
}
