<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Operator extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'wallet_amount',
        'status',
        'image_id',
        'verify_key',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
