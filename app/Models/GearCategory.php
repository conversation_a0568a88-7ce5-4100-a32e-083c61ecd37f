<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Tour;
use App\Models\Gear;

class GearCategory extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'image',
        'order',
        'status'
    ];

    public function gears()
    {
        return $this->hasMany(Gear::class)->orderBy('order');
    }

    public function tours()
    {
        return $this->belongsToMany(Tour::class, 'gear_category_tour', 'gear_category_id', 'tour_id');
    }
}
