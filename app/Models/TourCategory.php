<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TourCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_title',
        'slug',
        'description',
        'parent_id',
        'image_id',
        'image',
        'status',
        'meta_title',
        'meta_keyword',
        'meta_desc',
        'alt_text',
        'image_caption'
    ];

    public function tour()
    {
        return $this->hasMany(Tour::class, 'category_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo(TourCategory::class, 'parent_id', 'id');
    }
}
