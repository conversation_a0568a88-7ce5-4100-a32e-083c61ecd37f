<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class TourDay extends Model
{
   protected $fillable = [
       'tour_id', 'day', 'detail', 'image',
   ];

    public function tour()
    {
        return $this->belongsTo(Tour::class);
   }

   public function images() {
    return $this->morphMany(Image::class, 'imageable');
   }

   public function itineraryActivities(): BelongsToMany {
    return $this->belongsToMany(ItineraryActivity::class, 'itinerary_activity_itinerary', 'itinerary_id', 'itinerary_activity_id');
   }

   public function itineraryMedia() {
    return $this->hasMany(ItineraryMedia::class, 'itinerary_id');
   }

   public function attributes(): BelongsToMany {
    return $this->belongsToMany(Attributes::class, 'itinerary_attribute', 'itinerary_id', 'attribute_id')
        ->withPivot('value', 'info');
   }
}
