<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Blog extends Model implements Sitemapable
{
    use HasFactory;

    protected $fillable = [
        'blog_title',
        'slug',
        'short_desc',
        'blog_desc',
        'category_id',
        'image',
        'status',
        'sort_order',
        'meta_title',
        'meta_keyword',
        'meta_desc',
        'alt_text',
        'image_caption'
    ];

    public function category()
    {
        return $this->belongsTo(BlogCategory::class, 'blog_category_id', 'id');
    }

    public function scopeActive()
    {
        return $this->where('status', 1);
    }
    public function toSitemapTag(): Url|string|array
    {
        return Url::create(route('blog.detail', $this->slug))
            ->setPriority(0.6)
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY);
    }
}
