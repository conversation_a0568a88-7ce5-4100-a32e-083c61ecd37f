<?php

namespace App\Models;

use Carbon\Carbon;
use Spatie\Sitemap\Tags\Url;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sitemap\Contracts\Sitemapable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tour extends Model implements Sitemapable
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'category_id',
        'price',
        'days',
        'night',
        'sale_price',
        'adult_price',
        'child_price',
        'usd_adult_price',
        'usd_child_price',
        'description',
        'inclusion',
        'rates',
        'know_before',
        'is_featured',
        'cancellation',
        'image',
        'status',
        'meta_title',
        'meta_keyword',
        'meta_desc',
        'whats_included',
        'sort_order',
        'alt_text',
        'image_caption',
        'meta_slug',
        'alt_txt',
        'exclusion',
        'highlights',
        'short_desc',
        'map_image',
        'video_code',
        'highlight_title',
        'map_title',
        'video_title',
        'waysToGo_id',
        'short_itinerary',
        'gears_description',
        ];
    protected $casts = [
        'whats_included' => 'array',
        'know_before' => 'array',
    ];
    public function scopeActive()
    {
        return $this->where('status', 1);
    }
    public function toSitemapTag(): Url|string|array
    {
        return Url::create(route('tour.detail', $this->slug))
            ->setPriority(0.6)
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY);
    }
    public function category()
    {
        return $this->belongsTo(TourCategory::class, 'category_id', 'id');
    }

    public function gallery()
    {
        return $this->hasMany(TourMedia::class, 'tour_id', 'id');
    }

    public function itinerary()
    {
        return $this->hasMany(TourDay::class, 'tour_id', 'id');
    }

    public function popular_tours()
    {
        return $this->hasMany(PopularTour::class);
    }

    public function faqs()
    {
        return $this->hasMany(TourFaq::class);
    }

    public function attributes() {
        return $this->belongsToMany(Attributes::class, 'tour_attributes', 'tour_id', 'attribute_id')->withPivot(['value']);
    }

    public function waysToGo()
    {
        return $this->belongsTo(WaysToGo::class, 'waysToGo_id', 'id');
    }

    public function gearCategories()
    {
        return $this->belongsToMany(GearCategory::class, 'gear_category_tour', 'tour_id', 'gear_category_id')
            ->withPivot('order')
            ->orderBy('pivot_order');
    }

    public function reviews()
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    public function publishedReviews()
    {
        return $this->reviews()->where('is_published', 1)->latest();
    }

    public function hasUserReviewed()
    {
        return $this->reviews()->whereUserId(Auth::user()->id)->exists();
    }
}
