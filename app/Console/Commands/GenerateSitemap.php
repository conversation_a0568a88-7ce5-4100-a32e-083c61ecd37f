<?php

namespace App\Console\Commands;

use App\Http\Controllers\Actions\BuildSitemap;
use Illuminate\Console\Command;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Sitemap';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating Sitemap');
        (new BuildSitemap())->build();
        $this->info('Sitemap Generated');
    }
}
