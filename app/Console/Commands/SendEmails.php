<?php

namespace App\Console\Commands;

use App\Mail\FollowupMail;
use App\Mail\FollowupNotifyToAdmin;
use Carbon\Carbon;
use Foundation\Lib\EmailTime\EmailTime;
use Foundation\Lib\FollowUp\Status;
use Foundation\Lib\Meta;
use Foundation\Services\TrackingService;
use Illuminate\Console\Command;

class SendEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:followup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Email notification to user who has due payment less then a threshold';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $lastUpdate = app(TrackingService::class)->getLastNextUpdated();

        if (!$lastUpdate) {
            $this->sendEmail();
        }

        elseif (Carbon::parse($lastUpdate->next_notify)->format('Y-m-d') === now()->format('Y-m-d')) {
            $this->sendEmail();
        }
    }

    private function sendEmail()
    {

        // send emails in this period of time
        if (now()->format('H:i') >= '10:00' && now()->format('H:i') <= '10:30') {

            $defaultThreshold = Meta::get('due_threshold');
            if (empty($defaultThreshold)) {
                $this->alert('Default threshold is not set yet. !');
                return;
            } else {
                $users = app('db')->table('follow_ups as fup')
                    ->select('fup.module_type', 'fup.total_amount', 'fup.due_amount', 'users.id as userId', 'fup.created_at',
                        'fup.created_at', 'fup.remarks', 'users.email', 'users.phone_number', 'users.full_name', 'users.due_threshold')
                    ->join('users', 'users.id', '=', 'fup.customer_id')
                    ->where('paid', Status::UNPAID)
                    ->get()->groupBy('userId');

                $userWithEmails = [];
                foreach ($users as $user) {
                    $totalDueAmount = $user->sum('due_amount');

                    $firstUser = $user->first();

                    $isTrue = empty($firstUser->due_threshold)
                        ? $this->checkReadyToSendEmail($defaultThreshold, $totalDueAmount)
                        : $this->checkReadyToSendEmail($firstUser->due_threshold, $totalDueAmount);

                    if ($isTrue) {
                        app(TrackingService::class)->createTracking($user);
                        \Mail::to($firstUser->email ?? '<EMAIL>')->send(new FollowupMail($user));
                        array_push($userWithEmails, $user);
                    }
                }

                \Mail::to(Meta::get('email_notify_mail') ?? config('mail.from.address'))->send(new FollowupNotifyToAdmin($userWithEmails));
            }
        }
    }

    private function checkReadyToSendEmail($thresholdAmount, $dueAmount): bool
    {
        return $thresholdAmount <= $dueAmount;
    }
}
