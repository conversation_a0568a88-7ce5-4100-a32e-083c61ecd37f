<?php

namespace App\Console\Commands;

use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset Data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Resetting Data...');
        DB::table('accounts')->truncate();
        DB::table('air_tickets')->truncate();
        DB::table('bankdeposites')->truncate();
        DB::table('cash_flows')->truncate();
        DB::table('dues')->truncate();
        DB::table('employee_salaries')->truncate();
        DB::table('expenditures')->truncate();
        DB::table('follow_ups')->truncate();
        DB::table('history')->truncate();
        DB::table('incomes')->truncate();
        DB::table('payment_histories')->truncate();
        DB::table('photocopies')->truncate();
        DB::table('recharge_sales')->truncate();
        DB::table('remittances')->truncate();
        DB::table('sim_sales')->truncate();
        DB::table('sms_message_records')->truncate();
        DB::table('trial_balance_history')->truncate();

        $this->info('Data Reset Successfully');

    }
}
