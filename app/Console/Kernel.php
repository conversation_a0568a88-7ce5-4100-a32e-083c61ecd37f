<?php

namespace App\Console;

use App\Console\Commands\SendEmails;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use <PERSON><PERSON>\Backup\Commands\BackupCommand;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        if($this->app->environment('production')) {
            $schedule->command(SendEmails::class)->everyMinute();
            $schedule->command(BackupCommand::class)->at('01:00');
            $schedule->command(BackupCommand::class, [
                '--only-db'
            ])->cron('5 9,10,11,12,13,14,15,16,17,18,19,20,21 * * *');
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
