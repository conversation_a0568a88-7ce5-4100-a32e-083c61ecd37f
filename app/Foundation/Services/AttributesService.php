<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\Attributes;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class AttributesService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param Attributes $attributes
     */
    public function __construct(Attributes $attributes)
    {
        $this->model = $attributes;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }
}
