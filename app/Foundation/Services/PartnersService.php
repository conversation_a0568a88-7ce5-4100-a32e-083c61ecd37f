<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\Partner;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class PartnersService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param Partner $partner
     */
    public function __construct(Partner $partner)
    {
        $this->model = $partner;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->oldest('order')
            ->get();
    }
}
