<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\Inquiry;
use Foundation\Models\Role;
use Illuminate\Support\Facades\DB;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class InquiryService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param Inquiry $inquiry
     */
    public function __construct(Inquiry $inquiry)
    {
        $this->model = $inquiry;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    /**
     * Get the list of Active roles
     *
     * @return mixed
     */
    public function getRoles()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('title', 'id');
    }

    /**
     * Get title and Slug of Role
     *
     * @return mixed
     */
    public function getRolesWithSlug()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('title', 'slug');
    }

    /**
     * Get the role by its slug
     *
     * @param $slug
     * @return mixed
     */
    public function getRoleBySlug($slug)
    {
        return $this->model->where('slug', $slug)->first();
    }

    public function getId($slug)
    {
        return $this->model->where('slug', $slug)->value('id') ?? 0;
    }

    /**
     * Return parent roles
     *
     * [ 'parent_id' === 0 ]
     *
     * @param $id
     * @return mixed
     */
    public function getParentCategory($id = null)
    {
        return $this->model->where([
            ['id', '!=', $id],
            ['status', Status::ACTIVE_STATUS],
        ])->get();
    }

    /**
     * Ret roles titles from role id
     *
     * @param $inquiry
     * @return mixed
     */
    public function getNameFromId($inquiry)
    {
        return $this->model->whereIn('id', $inquiry)->pluck('title', 'id');
    }

    public function getInquiryList()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->orderBy('sort_order', 'asc')->get();
    }
}
