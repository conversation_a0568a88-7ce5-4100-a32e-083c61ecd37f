<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\PopularTour;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class PopularTourService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param PopularTour $tour
     */
    public function __construct(PopularTour $tour)
    {
        $this->model = $tour;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->has('tour')
            ->select('*')
            ->oldest('order')
            ->get();
    }

    public function maxOrder()
    {
        return $this->model->max('order') + 1;
    }


}
