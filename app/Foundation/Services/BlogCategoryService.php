<?php /** @noinspection SqlNoDataSourceInspection */

namespace Foundation\Services;

use App\Models\BlogCategory;
use Foundation\Models\Role;
use Illuminate\Support\Facades\DB;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class BlogCategoryService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param Role $blogCategory
     */
    public function __construct(BlogCategory $blogCategory)
    {
        $this->model = $blogCategory;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    /**
     * Pluck Name and ID of the model.
     *
     * @return mixed
     */
    public function pluck()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('category_title', 'id');
    }
}
