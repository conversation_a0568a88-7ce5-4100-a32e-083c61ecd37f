<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\Blog;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class BlogService
 * @package Foundation\Services
 */
class BlogService extends BaseService
{

    /**
     * The Blog instance
     *
     * @var $model
     */
    protected $model;

    /**
     * BlogService constructor.
     * @param Blog $blog
     */
    public function __construct(Blog $blog)
    {
        $this->model = $blog;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    /**
     * Get the list of Active roles
     *
     * @return mixed
     */
    public function getBlogs()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('title', 'id');
    }

    /**
     * Get title and Slug of Blog
     *
     * @return mixed
     */
    public function getBlogsWithSlug()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('title', 'slug');
    }

    /**
     * Get the role by its slug
     *
     * @param $slug
     * @return mixed
     */
    public function getBlogBySlug($slug)
    {
        return $this->model->where('slug', $slug)->first();
    }

    public function getId($slug)
    {
        return $this->model->where('slug', $slug)->value('id') ?? 0;
    }

    /**
     * Return parent roles
     *
     * [ 'parent_id' === 0 ]
     *
     * @param $id
     * @return mixed
     */
    public function getParentCategory($id = null)
    {
        return $this->model->where([
            ['id', '!=', $id],
            ['status', Status::ACTIVE_STATUS],
        ])->get();
    }

    /**
     * Ret roles titles from role id
     *
     * @param $blog
     * @return mixed
     */
    public function getNameFromId($blog)
    {
        return $this->model->whereIn('id', $blog)->pluck('title', 'id');
    }

    public function getBlogList($perPage = 10)
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->latest()->paginate($perPage);
    }

    public function getRecentBlogList()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->latest()->limit(5)->get();
    }
}
