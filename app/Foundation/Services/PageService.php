<?php

namespace Foundation\Services;

use App\Models\Page;
use Neputer\Supports\BaseService;


/**
 * Class PostService
 * @package Foundation\Services
 */
class PageService extends BaseService
{

    /**
     * The Post instance
     *
     * @var $model
     */
    protected $model;


    /**
     * PageService constructor.
     * @param Page $page
     */
    public function __construct(Page $page)
    {
        $this->model = $page;
    }

    /**
     * Filter
     *
     * @param array $data
     * @return mixed
     */
    public function filter()
    {
        return $this->model
         ->orderBy('id', 'DESC')->get();
    }

    public function checkForTitle($title)
    {
        return $this->model->where('title', $title)->count();
    }

    public function getPageBySlug($slug)
    {
        return $this->model->where('slug', $slug)->first();
    }

    public function sendContactUsEmail(array $data)
    {

    }

}
