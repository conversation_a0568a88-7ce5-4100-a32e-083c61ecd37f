<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\WaysToGo;
use Foundation\Models\Role;
use Illuminate\Support\Facades\DB;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class WaysToGoService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param WaysToGo $waysToGo
     */
    public function __construct(WaysToGo $waysToGo)
    {
        $this->model = $waysToGo;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    public function pluck()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('title', 'id');
    }

    public function getCategory()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->get();
    }

    public function getBySlug($slug)
    {
        return $this->model->whereSlug($slug)->whereStatus(Status::ACTIVE_STATUS)->first();
    }

    public function getHomePageList()
    {
        return $this->model->select('title','slug','short_description')->where('status', Status::ACTIVE_STATUS)->limit(3)->orderBy('sort_order')->get();
    }
}
