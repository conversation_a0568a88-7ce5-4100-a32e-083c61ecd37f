<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\Tour;
use App\Models\Review;
use App\Models\TourMedia;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class TourService extends BaseService
{

    /**
     * The Role instance
     *
     * @var Tour
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param Tour $tour
     */
    public function __construct(Tour $tour)
    {
        $this->model = $tour;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    /**
     * Get the list of Active roles
     *
     * @return mixed
     */
    public function getTour()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('name', 'id');
    }

    /**
     * Get name and Slug of Role
     *
     * @return mixed
     */
    public function getToursWithSlug()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('name', 'slug');
    }

    /**
     * Get the role by its slug
     *
     * @param $slug
     * @return mixed
     */
    public function getTourBySlug($slug, array $with = [])
    {
        $with = array_merge(['gallery', 'faqs'], $with);
        return $this->model->where('slug', $slug)->where('status', 1)
            ->with($with)
            ->withCount('publishedReviews')
            ->withAvg('publishedReviews', 'rating')
            ->first();
    }

    public function getId($slug)
    {
        return $this->model->where('slug', $slug)->value('id') ?? 0;
    }

    /**
     * Return parent roles
     *
     * [ 'parent_id' === 0 ]
     *
     * @param $id
     * @return mixed
     */
    public function getParentCategory($id = null)
    {
        return $this->model->where([
            ['id', '!=', $id],
            ['status', Status::ACTIVE_STATUS],
        ])->get();
    }

    /**
     * Ret roles names from role id
     *
     * @param $tour
     * @return mixed
     */
    public function getNameFromId($tour)
    {
        return $this->model->whereIn('id', $tour)->pluck('name', 'id');
    }

    public function storeGallery(array $data)
    {
      return  TourMedia::create([
            'tour_id' => $data['tour_id'],
            'image' => $data['image'],
            'alt_text' => $data['alt_text'] ?? null,
            'status' => $data['status'] ?? true,
            'sort_order' => $data['sort_order'] ?? 1,
        ]);
    }

    public function getTourList($category=null)
    {

        return $this->model->where('status', Status::ACTIVE_STATUS)
            ->when($category, function ($query) use ($category) {
                $query->whereHas('category', function ($query) use ($category) {
                    $query->where('slug', $category);
                });
            })
            ->with('category')
            ->limit(8)
            ->get();
    }

    public function getPoplarList()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)
            ->join('popular_tours', 'tours.id', '=', 'popular_tours.tour_id')
            ->select(['tours.*', 'popular_tours.order AS order'])
            ->oldest('order')
            ->get();
    }

    public function getSimilarTours($category_id, $id)
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)
            ->where('category_id', $category_id)
            ->where('id', '!=', $id)
            ->limit(8)
            ->get();
    }

    /**
     * Get paginated reviews for a tour
     *
     * @param int $tourId
     * @param int $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    /**
     * Get paginated reviews for a tour
     *
     * @param int $tourId
     * @param int $perPage
     * @param int $page
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getPaginatedReviews($tourId, $perPage = 5, $page = 1)
    {
        return Review::where('reviewable_id', $tourId)
            ->where('reviewable_type', Tour::class)
            ->where('is_published', true)
            ->with('user')
            ->latest()
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
