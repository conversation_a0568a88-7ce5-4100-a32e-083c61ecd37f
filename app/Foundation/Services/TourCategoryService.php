<?php /** @noinspection SqlNoDataSourceInspection */

namespace App\Foundation\Services;

use App\Models\TourCategory;
use Foundation\Models\Role;
use Illuminate\Support\Facades\DB;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class TourCategoryService extends BaseService
{

    /**
     * The Role instance
     *
     * @var $model
     */
    protected $model;

    /**
     * RoleService constructor.
     * @param TourCategory $tourCategory
     */
    public function __construct(TourCategory $tourCategory)
    {
        $this->model = $tourCategory;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->select('*')
            ->latest()
            ->get();
    }

    public function pluck()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('category_title', 'id');
    }

    public function getCategory()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->get();
    }

    public function getBySlug($slug)
    {
        return $this->model->whereSlug($slug)->whereStatus(Status::ACTIVE_STATUS)->first();
    }
}
