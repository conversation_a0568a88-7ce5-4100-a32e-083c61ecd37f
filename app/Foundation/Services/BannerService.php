<?php

    namespace App\Foundation\Services;

    use App\Models\Banner;
    use Neputer\Config\Status;
    use Neputer\Supports\BaseService;

    class BannerService extends BaseService
    {

        protected Banner $model;

        public function __construct(Banner $banner)
        {
            $this->model = $banner;
        }

        public function filter()
        {
            return $this->model->query()
                ->select('*')
                ->latest()
                ->get();
        }

        public function getBannerList()
        {
            return $this->model->where('status', Status::ACTIVE_STATUS)->get();
        }

    }
