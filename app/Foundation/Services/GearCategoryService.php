<?php

namespace App\Foundation\Services;

use App\Models\GearCategory;
use Neputer\Supports\BaseService;


/**
 * Class PostService
 * @package Foundation\Services
 */
class GearCategoryService extends BaseService
{

    /**
     * The Post instance
     *
     * @var $model
     */
    protected $model;


    /**
     * PageService constructor.
     * @param Page $page
     */
    public function __construct(GearCategory $page)
    {
        $this->model = $page;
    }

    /**
     * Filter
     *
     * @param array $data
     * @return mixed
     */
    public function filter()
    {
        return $this->model
         ->orderBy('order', 'ASC')->get();
    }

    public function pluck()
    {
        return $this->model->where('status', 1)->pluck('name', 'id');
    }

}
