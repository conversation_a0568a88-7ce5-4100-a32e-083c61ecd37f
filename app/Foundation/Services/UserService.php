<?php

namespace Foundation\Services;

use Foundation\Builders\Filters\User\Filter;
use Foundation\Models\User;
use Foundation\Models\UserAddress;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class UserService
 * @package Foundation\Services
 */
class UserService extends BaseService
{
//    use Image;
    /**
     * The User instance
     *
     * @var $model
     */
    protected $model;

    /**
     * UserService constructor.
     * @param User $user
     */
    public function __construct(User $user)
    {
        $this->model = $user;
    }

    /**
     * Filter
     *
     * @param array $data
     * @return mixed
     */
    public function filter(array $data)
    {
        $model = $this->model;


        return $model = Filter::apply(
            $model
                ->select('*'), $data)
            ->with('roles')
            ->orderBy('created_at', 'DESC');
    }

    /**
     * Get User's List by given $role
     *
     * @param array $data
     * @param $role
     * @return mixed
     */
    public function filterByRole(array $data, $role)
    {

        return Filter::apply($this->model->select('users.*'), $data)
            ->orderBy('created_at', 'DESC');
    }

    public function pluckUserByRole( string $slug)
    {
        return $this->model
            ->select('users.*')
            ->selectRaw("full_name")
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'role_user.role_id')
            ->where('roles.slug', $slug)
            ->orderBy('users.created_at', 'DESC')
            ->pluck('full_name', 'id');
    }

    public function pluckUserWithEmailByRole( string $slug)
    {
        return $this->model
            ->select('users.*')
            ->selectRaw("CONCAT(COALESCE(full_name,''),' | ', COALESCE(email,'')) AS user")
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'role_user.role_id')
            ->where('roles.slug', $slug)
            ->orderBy('users.created_at', 'DESC')
            ->pluck('user', 'id');
    }

    public function pluckDiscountVoucherCustomer($customerid)
    {
        return $this->model
            ->select('users.*')
            ->selectRaw("CONCAT(COALESCE(full_name,''),' | ', COALESCE(email,'')) AS user")
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'role_user.role_id')
            ->where('roles.slug', 'customer')
            ->where('users.id', $customerid)
            ->orderBy('users.created_at', 'DESC')
            ->pluck('user', 'id');
    }

    /**
     * Find Users by array of IDs
     *
     * @param array $ids
     * @return mixed
     */
    public function find(array $ids)
    {
        return $this->model->select('id', 'full_name', 'email', 'phone_number')->find($ids);
    }

    /**
     * Get Users having the given role
     *
     * @param $role
     * @return mixed
     */
    public function getUsersHavingRole($role)
    {
        return $this->model->whereHas('roles', function (Builder $query) use ($role) {
            $query->where('roles.slug', $role);
        })->get();
    }

    /**
     * Get Users having verified phone numbers
     *
     * @param $role
     * @return mixed
     */
    public function getPhoneHavingRole($role)
    {
        return $this->model->whereHas('roles', function (Builder $query) use ($role) {
            $query->where('roles.slug', $role);
        })
        ->where([['phone_number', '!=', null], ['phone_is_verified', true]])
        ->get();
    }

    /**
     * Find a user by email.
     *
     * @param $email
     * @return mixed
     */
    public function findByEmail($email)
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Find a user by email.
     *
     * @param $id
     * @return mixed
     */
    public function findById($id)
    {
        return $this->model->where('id', $id)->first();
    }

    public function getLoggedInUser()
    {
        return request()->user();
    }

    /**
     * @param String|null $type
     * @param null $date
     * @return int
     */
    public function getCountByUserType(String $type = null, $date = null)
    {
        $builder = app('db')->table('users')
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'role_user.role_id')
            ->where('roles.slug', $type);
        if ($date) {
            $builder->whereDate('users.created_at', $date);
        }
        return $builder->count();
    }

    /**
     * @param $id
     * @return bool
     */
    public function forceDelete($id)
    {
        $user = $this->model->withTrashed()->findOrFail($id);
        $user->forceDelete();
        return true;
    }

    /**
     * @param $id
     * @return bool
     */
    public function restore($id)
    {
        $user = $this->model->onlyTrashed()->findOrFail($id);
        $user->restore();
        return true;
    }

    /**
     * @param $type
     * @param null $role
     * @return mixed
     */
    public function getCountByStatus($role = null)
    {

        $query = $this->model
            ->selectRaw('count(*) as total')
            ->selectRaw("count(case when status = '".Status::ACTIVE_STATUS."' then 1 end) as active")
            ->selectRaw("count(case when status = '".Status::INACTIVE_STATUS."' then 1 end) as inactive");

        if ($role) {
            $query->whereHas('roles', function ($query) use ($role) {
                $query->where('roles.slug', $role);
            });
        }
        $all = $query->first()->toArray();

        return (array) $all;
    }

    public function updateLoggedInUser($model, array $data)
    {
        return $model->update($data);

    }

    public function getOrderList($userId, string $orderId = null)
    {
        return app('db')
            ->table('orders')
            ->select(
                'users.id', 'orders.id as o_id', 'orders.order_id',
                'orders.quantity', 'orders.amount', 'transactions.status',
                'orders.delivery_status', 'orders.created_at', 'orders.updated_at'
            )
            ->leftJoin('transactions', 'transactions.id', 'orders.transaction_id')
            ->leftJoin('users', 'transactions.user_id', 'users.id')
            ->where('transactions.user_id', $userId)
            ->where(function ($query) use($orderId){
                if ($orderId) {
                    $query->where('orders.order_id', 'like', '%'.$orderId.'%');
                }
            })
            ->orderBy('orders.created_at', 'DESC');
    }

    /**
     * Get the first record matching the $condition or create new one with $data
     *
     * @param array $condition
     * @param array $data
     * @return mixed
     */
    public function firstOrCreate(array $condition, array $data)
    {
        return $this->model->firstOrCreate($condition, $data);
    }

    public function getUserForAPeriod($dailyDate, $startDate, $endDate, $role)
    {
        $query = $this->model
            ->where(function ($query) use ($dailyDate, $startDate, $endDate){
                if($dailyDate)
                    $query->whereDate('created_at', $dailyDate);
                else
                    $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->selectRaw('count(id) as total')
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date')
            ->selectRaw("sum(case when is_verified = '".Status::ACTIVE_STATUS."' AND phone_is_verified = '".Status::ACTIVE_STATUS."' then 1 end) as total_user_verified")
            ->selectRaw("sum(case when is_verified = '".Status::INACTIVE_STATUS."' AND phone_is_verified = '".Status::INACTIVE_STATUS."' then 1 end) as total_user_unverified")
            ->selectRaw("sum(case when is_verified = '".Status::ACTIVE_STATUS."' then 1 end) as total_user_verified_with_email")
            ->selectRaw("sum(case when phone_is_verified = '".Status::ACTIVE_STATUS."' then 1 end) as total_user_verified_with_phone");

        if ($role) {
            $query->whereHas('roles', function ($query) use ($role) {
                $query->where('roles.slug', $role);
            });
        }

        return $query->groupBy('date')
        ->orderBy('date', 'desc')
        ->get();
    }

    /**
     * Find USER by Character ID
     *
     * @param $id
     * @return mixed
     */
    public function findByCharId($id)
    {
        return $this->model->where('character_id', $id)->first();
    }

    public function delete($user)
    {
        return $this->model->where('id', $user->id)->delete();
    }

    /**
     * Search User's Full Name and Email
     *
     * @param $search
     * @param $role
     * @return mixed
     */
    public function searchByRole($search, $role)
    {
        return $this->model
            ->select(
                'full_name',
                'id'
            )
            ->where(function ($query) use ($search){
                $query->whereRaw("full_name like ?", ['%' . $search . '%'])
                    ->orWhere('email', 'like', '%' . $search . '%');
            })
            ->whereHas('roles', function ($query) use ($role){
                $query->where('slug', $role);
            })
            ->get('full_name', 'id');
    }

    /**
     * Blacklist User(s)
     *
     * @param array $ids
     * @return mixed
     */
    public function blacklist(array $ids)
    {
        $this->addBlacklistEntries($ids);
        return $this->model->whereIn('id', $ids)->update(['is_blacklisted' => true]);
    }

    /**
     * Blacklist User(s)
     *
     * @param array $ids
     * @return mixed
     */
    public function removeFromBlacklist(array $ids)
    {
        $this->removeBlacklistEntries($ids);
        return $this->model->whereIn('id', $ids)->update(['is_blacklisted' => false]);
    }

    /**
     * Add Black List Entries
     *
     * @param array $ids
     * @return mixed
     */
    private function addBlacklistEntries(array $ids)
    {
        $users = $this->model
            ->select('id', 'full_name', 'email', 'phone_number')
            ->whereIn('id', $ids)
            ->where('is_blacklisted', false)
            ->get();

        $list = [];
        foreach ($users as $user){
            array_push($list, [
                'name' => $user->getFullName(),
                'email' => $user->email,
                'phone' => $user->phone_number,
                'user_id' => $user->id,
                'listed_by' => auth()->id(),
                'created_at' => now(),
                'updated_at' => now(),
                'remarks' => 'User Blacklisted',
            ]);
        }
        return app('db')->table('users_blacklist')->insert($list);
    }

    /**
     * Add Black List Entries
     *
     * @param array $ids
     * @return mixed
     */
    private function removeBlacklistEntries(array $ids)
    {
        return app('db')->table('users_blacklist')->whereIn('id', $ids)->delete();
    }

    public function changePassword($newPassword)
    {
        return auth()
            ->user()
            ->update([
                'password' => bcrypt($newPassword),
            ]);
    }


    /**
     * @param $id
     * @return mixed
     */
    public function findOrFail($id)
    {
        return $this->model->where('id', $id)->firstOrFail();
    }


    /**
     * Add User's Address
     * @param $data
     * @return mixed
     */
    public function addAddress($data)
    {
        return UserAddress::create($data);
    }

    public function removePrimary()
    {
        return UserAddress::where('user_id', auth()->id())
            ->update([
                'is_primary' => false
            ]);
    }

    /**
     * delete User's Address
     * @param $address
     * @return mixed
     */
    public function deleteAddress($address)
    {
        return UserAddress::where('id', $address)->where('user_id', auth()->id())->delete();
    }

    public function updatePassword($request)
    {
        $this->model
            ->where(['id' => Auth::user()->id])
            ->update([
            'password' => bcrypt($request['password']),
        ]);
    }

    public function updateAddress(array $validate)
    {
        $user = Auth::user();

        return UserAddress::updateOrCreate(
            ['id' => $validate['id']],
            [
                'full_name' => $user->full_name,
                'user_id' => $user->id,
                'email' => $validate['email'] ?? $user->email,
                'name' => $validate['name'],
                'city' => $validate['city'],
                'state' => $validate['state'],
                'zip_code' => $validate['zip_code'],
                'phone' => $validate['phone'],
                'is_primary' => $validate['is_primary'] ?? Address::SECONDARY_ADDRESS,
            ]
        );
    }

    public function getUserAddress()
    {
      return $this->model
            ->where('id', \auth()->id())
            ->with(['addresses' => function ($query) {
            $query->latest()->limit(10);
        }])->firstOrFail();
    }

    public function getUserWithCounter()
    {
        return $this->model
            ->withCount(['customOrders', 'transactions', 'addresses'])
            ->where('id', \auth()->id())
            ->firstOrFail();
    }

    public function findUserAddress($addressId)
    {
        return UserAddress::find($addressId)->where('user_id', \auth()->id())->first();
    }

    public function getAllUserAddress()
    {
        return $this->model->with(['addresses'])->where('id', auth()->id())->firstOrFail();
    }

    public function getCustomer($term, $limit)
    {
        return $this->model
            ->select('id')
            ->selectRaw("CONCAT(COALESCE(full_name, ''), ' ( Phone-No. ', COALESCE(phone_number,''), ' ) ') AS text")
            ->where(function ($query) use ($term) {
                $query->where('full_name', 'like', '%' . $term . '%');
                $query->orwhere('phone_number', 'like', '%' . $term . '%');
            })
            ->limit($limit)
            ->get();
    }

    public function getAllUser()
    {
        return $this->model
            ->pluck('full_name', 'id')
            ->toArray();
    }

}
