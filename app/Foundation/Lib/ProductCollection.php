<?php


namespace App\Foundation\Lib;


class ProductCollection
{
    const CREATED_BY_ADMIN = 1;
    const CREATED_BY_CUSTOMER = 2;

    const TYPE_ADMIN_BUILT_SETUP = 1;
    const TYPE_USER_CUSTOMIZABLE_SETUP = 2;
    const TYPE_USER_BUILT_SETUP = 3;

    public static $created = [
        self::CREATED_BY_ADMIN => 'Created By Admin',
        self::CREATED_BY_CUSTOMER => 'Created by Customer',
    ];

    public static $types = [
        self::TYPE_ADMIN_BUILT_SETUP => 'Admin Built Setup', // Built By Admin, User Cannot Customize
        self::TYPE_USER_CUSTOMIZABLE_SETUP => 'User Customizable Setup', // Build By Admin, User Can Customize
        self::TYPE_USER_BUILT_SETUP => 'User Built Setup' // Build By User from Scratch
    ];

}
