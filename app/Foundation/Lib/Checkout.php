<?php


namespace App\Foundation\Lib;

/**
 * Class Checkout
 * @package App\Foundation\Lib
 */
class Checkout
{
    const STEP_INITIATED = 1;
    const STEP_SHIPPING = 2;
    const STEP_BILLING = 3;
    const STEP_PAYMENT = 4;
    const STEP_REVIEW = 5;
    const STEP_COMPLETE = 6;

    const STATUS_INITIATED_SLUG = null;
    const SHIPPING_SLUG = 'shipping';
    const BILLING_SLUG = 'billing';
    const PAYMENT_SLUG = 'payment';
    const REVIEW_SLUG = 'review';
    const COMPLETE_SLUG = 'complete';

    public static $steps = [
        self::STEP_INITIATED => self::STATUS_INITIATED_SLUG,
        self::STEP_SHIPPING => self::SHIPPING_SLUG,
        self::STEP_BILLING => self::BILLING_SLUG,
        self::STEP_PAYMENT => self::PAYMENT_SLUG,
        self::STEP_REVIEW => self::REVIEW_SLUG,
        self::STEP_COMPLETE => self::COMPLETE_SLUG
    ];

}
