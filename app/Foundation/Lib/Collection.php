<?php

namespace Foundation\Lib;

use Neputer\Supports\BaseConstant;

class Collection extends BaseConstant
{
    const PRODUCT_COLLECTION = 1;
    const CATEGORY_COLLECTION = 2;
    const PRODUCT_BUNDLE_COLLECTION = 3;
    const RECENT_PRODUCTS = 4;
    const FEATURE_PRODUCTS = 5;
    const POPULAR_PRODUCTS = 6;
    const LATEST_PRODUCTS = 7;

    /**
     * @var string[]
     */
    public static $current = [

        self::PRODUCT_COLLECTION  => 'product-collection',
        self::CATEGORY_COLLECTION  => 'category-collection',
        self::PRODUCT_BUNDLE_COLLECTION  => 'product-bundle-collection',
        self::RECENT_PRODUCTS => 'recent-products',
        self::FEATURE_PRODUCTS => 'feature-products',
        self::POPULAR_PRODUCTS => 'popular-products',
        self::LATEST_PRODUCTS => 'latest-products',

    ];

}
