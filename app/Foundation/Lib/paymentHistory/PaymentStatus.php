<?php

namespace Foundation\Lib\paymentHistory;

final class PaymentStatus
{

    /* Payment In histories is not clear */
    const UNPAID = 0;

    /* Payment is Done Fully */
    const PAID = 1;

    /* Due Payment Entry for customer */
    const INITIAL_PAYMENT = 2;

    const WITHOUT_CUSTOMER = 3;

    public static $current = [
       self::UNPAID =>  'UNPAID',
        self::PAID => 'PAID',
        self::INITIAL_PAYMENT => 'INITIAL PAYMENT',
        self::WITHOUT_CUSTOMER => 'WITHOUT CUSTOMER'
    ];
}
