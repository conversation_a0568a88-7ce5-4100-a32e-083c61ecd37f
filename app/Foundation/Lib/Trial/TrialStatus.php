<?php

namespace Foundation\Lib\Trial;

use Carbon\Carbon;

final class TrialStatus
{
    /*
     * If Customer buy the subscription then it will ready to use software
     */
    const SUBSCRIBED = 0;

    /*
    * Still remaining to end time
    */
    const RUNNING = 1;

    /*
     *  Trial period ended.
     */
    const COMPLETED = 2;

    public static function checkTrialSession(): bool
    {
       return TrialStatus::RUNNING == config('trialSession.status') || TrialStatus::SUBSCRIBED == config('trialSession.status');
    }

}