<?php

namespace Foundation\Lib\FollowUp;

final class Status
{
    const PAID = 1;
    const UNPAID = 0;

    const PAID_STATUS = 'PAID';

    const UNPAID_STATUS = 'UNPAID';

    const ALL = 'All';

    const ALL_VALUE = 2;

    public static $current = [
        self::PAID_STATUS => 'Paid',
        self::UNPAID_STATUS => 'Unpaid',
    ];

    public static array $paymentMethod = [
        self::ALL_VALUE => self::ALL,
        self::PAID => self::PAID_STATUS,
        self::UNPAID => self::UNPAID_STATUS
    ];
}
