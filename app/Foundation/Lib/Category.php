<?php

namespace Foundation\Lib;

use Neputer\Supports\BaseConstant;

/**
 * Class Category
 * @package App\Foundation\Lib
 */
final class Category extends BaseConstant
{
    const TYPE_GENERAL_CATEGORY = 0;
    const TYPE_PRODUCT_CATEGORY = 1;
    const TYPE_EXPENSE_CATEGORY = 2;

    const PRODUCT_TYPE_SYSTEM = 1;

    public static $types = [
        self::TYPE_GENERAL_CATEGORY => 'General Category', //  CMS
        self::TYPE_PRODUCT_CATEGORY => 'Product Category',
        self::TYPE_EXPENSE_CATEGORY => 'Expense Category',
    ];

    public static $productType = [
        self::PRODUCT_TYPE_SYSTEM => 'System Product',
    ];

    public static function getTypes()
    {
        return static::$types;
    }

    public static function getProductTypes()
    {
        return static::$productType;
    }

}
