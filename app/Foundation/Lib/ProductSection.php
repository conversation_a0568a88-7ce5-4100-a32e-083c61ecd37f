<?php

namespace Foundation\Lib;

use Neputer\Supports\BaseConstant;

/**
 * Class Category
 * @package App\Foundation\Lib
 */
final class ProductSection extends BaseConstant
{
    const LO_IMAGE_LEFT_TEXT_RIGHT = 1;
    const LO_TEXT_LEFT_IMAGE_RIGHT = 2;
    const LO_BG_IMAGE_TEXT_RIGHT = 3;
    const LO_BG_IMAGE_TEXT_LEFT = 4;
    const LO_IMAGE_TOP_TEXT_BOTTOM = 5;

    const TYPE_GENERAL_SECTION = 1;
    const TYPE_FEATURE_SECTION = 2;

    public static $imageLeft = [
        self::LO_IMAGE_LEFT_TEXT_RIGHT,
        self::LO_BG_IMAGE_TEXT_RIGHT,
        self::LO_IMAGE_TOP_TEXT_BOTTOM
    ];

    public static $layouts = [
        self::LO_IMAGE_LEFT_TEXT_RIGHT => 'Image Left Text Right',
        self::LO_TEXT_LEFT_IMAGE_RIGHT => 'Text Left Image Right',
        self::LO_BG_IMAGE_TEXT_RIGHT => 'Image Background Text Right',
        self::LO_BG_IMAGE_TEXT_LEFT => 'Image Background Text Left',
        self::LO_IMAGE_TOP_TEXT_BOTTOM => 'Image Top Text Bottom'
    ];

    public static $type = [
        self::TYPE_GENERAL_SECTION => 'General Section',
        self::TYPE_FEATURE_SECTION => 'Feature Section'
    ];
}
