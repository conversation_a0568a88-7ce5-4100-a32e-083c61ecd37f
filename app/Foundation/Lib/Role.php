<?php

namespace Foundation\Lib;

use Neputer\Supports\BaseConstant;

/**
 * Class Role
 * @package App\Foundation\Lib
 */
final class Role extends BaseConstant
{

    /**
     *  Role Super Admin
     */
    const ROLE_SUPER_ADMIN = 0;

    /**
     * Role Admin
     */
    const ROLE_ADMIN = 2;

    /**
     * Role Customer
     */
    const ROLE_CUSTOMER = 3;


    /**
     * Role Operator
     */
    const ROLE_OPERATOR = 5;

    /**
     * Role Operator
     */
    const ROLE_SALES = 6;

    /**
     * @var $current
     */
    public static $current = [
        self::ROLE_SUPER_ADMIN  => 'super-admin',
        self::ROLE_ADMIN        => 'admin',
        self::ROLE_CUSTOMER     => 'customer',
        self::ROLE_OPERATOR     => 'operator',
        self::ROLE_SALES        => 'sales',
    ];

    public static function getSuperAdmin()
    {
        return self::$current[self::ROLE_SUPER_ADMIN];
    }

    public static function getAdmin()
    {
        return self::$current[self::ROLE_ADMIN];
    }

    public static function getCustomer($returnIndex = false)
    {
        return self::get(self::ROLE_CUSTOMER, $returnIndex);
    }
    public static function getOperator($returnIndex = false)
    {
        return self::get(self::ROLE_OPERATOR, $returnIndex);
    }


    public static function getSalesPerson($returnIndex = false)
    {
        return self::get(self::ROLE_SALES, $returnIndex);
    }

    public static function getHighLevelRoles()
    {
        return [
            self::ROLE_SUPER_ADMIN  => 'super-admin',
            self::ROLE_ADMIN        => 'admin',
        ];
    }

    /**
     * Get Middleware String
     *
     * @return string
     */
    public static function getMiddlewareString()
    {
        $middleware= 'access:';
        foreach (self::getHighLevelRoles() as $role)
            $middleware .=  $role . ',';
        return substr($middleware, 0, -1);
    }

}
