<?php


namespace App\Foundation\Lib;


class History
{
    const TYPE_VIEW = 1;
    const TYPE_UPDATE = 2;
    const TYPE_DELETE = 3;
    const TYPE_PURCHASE = 4;
    const TYPE_AUTOMATIC_ASSIGN = 5;

    public static $type = [
        self::TYPE_VIEW => 'Item is viewed',
        self::TYPE_UPDATE => 'Item is Updated',
        self::TYPE_DELETE => 'Item is Deleted',
        self::TYPE_PURCHASE => 'Item is Purchased',
        self::TYPE_AUTOMATIC_ASSIGN => 'Item is Automatically Assigned'
    ];

}
