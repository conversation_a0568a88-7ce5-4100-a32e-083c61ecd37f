<?php

namespace Foundation\Lib;

use Neputer\Supports\BaseConstant;

/**
 * Class Product
 * @package Foundation\Lib
 */
final class Product extends BaseConstant
{

    const PRODUCT_TYPE_ONE_INDEX = 0;
    const PRODUCT_TYPE_TWO_INDEX = 1;
    const PRODUCT_LOW_TO_HIGH = 2;
    const PRODUCT_HIGH_TO_LOW = 3;

    const PRODUCT_TYPE_ONE = 'type-one';
    const PRODUCT_TYPE_TWO = 'type-two';

    const PRODUCT_PATTERN_BEST_SELLING_INDEX = 0;
    const PRODUCT_PATTERN_TOP_WISHLIST_INDEX = 1;
    const PRODUCT_PATTERN_NEW_RELEASE_INDEX  = 2;

    const PRODUCT_PATTERN_BEST_SELLING = 'Best Selling';
    const PRODUCT_PATTERN_TOP_WISHLIST = 'Top Wishlist';
    const PRODUCT_PATTERN_NEW_RELEASE  = 'Latest / New Release';

    public static array $types = [
        self::PRODUCT_TYPE_ONE_INDEX => 'Type One',
        self::PRODUCT_TYPE_TWO_INDEX => 'Type Two',
    ];

    public static array $patterns = [
        self::PRODUCT_PATTERN_BEST_SELLING_INDEX => self::PRODUCT_PATTERN_BEST_SELLING,
        self::PRODUCT_PATTERN_TOP_WISHLIST_INDEX => self::PRODUCT_PATTERN_TOP_WISHLIST,
        self::PRODUCT_PATTERN_NEW_RELEASE_INDEX  => self::PRODUCT_PATTERN_NEW_RELEASE,
    ];

    public static function getPatterns(): array
    {
        return Product::$patterns;
    }

    public static function getTypes(): array
    {
        return Product::$types;
    }

}
