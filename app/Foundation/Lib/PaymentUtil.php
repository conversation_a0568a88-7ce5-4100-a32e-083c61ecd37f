<?php

namespace Foundation\Lib;

/**
 * Class PaymentUtil
 * @package Foundation\Lib
 */
final class PaymentUtil
{

    /**
     * Return the resolved amount for the transaction for imePay payment
     *
     * @param $transaction
     * @param $orders
     * @return string
     */
    public static function resolveImePayAmount($transaction, $orders)
    {
        $orderFinalPrice = (static::getTotalPrice($orders) +
                static::getGatewayServiceCharge((optional($transaction)->service_charge ?? 0)));
        return static::getPrice($orderFinalPrice - $transaction->voucher_discount);
    }

    /**
     * Return the resolved amount for the transaction for prabhuPay payment
     *
     * @param $transaction
     * @param $orders
     * @return string
     */
    public static function resolvePrabhuPayAmount($transaction, $orders)
    {
        $orderFinalPrice = (static::getTotalPrice($orders) +
                static::getGatewayServiceCharge((optional($transaction)->service_charge ?? 0)));

        return static::getPrice($orderFinalPrice - $transaction->voucher_discount);
    }

    /**
     * Return the resolved amount for the transaction for khalti payment
     *
     * @param $transaction
     * @param $orders
     * @return string
     */
    public static function resolveKhaltiAmount($transaction, $orders)
    {
        $orderFinalPrice = ((static::getTotalPrice($orders) +
                static::getGatewayServiceCharge((optional($transaction)->service_charge ?? 0))) - $transaction->voucher_discount) * 100;
        return static::getPrice($orderFinalPrice);
    }

    /**
     * Return total price for the transactions
     *
     * @param $orders
     * @return float|int
     */
    private static function getTotalPrice($orders)
    {
        $price = 0;

        foreach ($orders  as $order) {
            if ($order->discounted_amount) {
                $price += (($order->quantity * $order->amount) - $order->discounted_amount);
            } else {
                $price += ($order->quantity * $order->amount);
            }
        }
        return $price;
    }

    /**
     * Return the service charge for any gateway
     *
     * @param $serviceCharge
     * @return string|string[]
     */
    private static function getGatewayServiceCharge($serviceCharge)
    {
        return str_replace(',', '',  $serviceCharge);
    }

    /**
     * Return the price with only 2 decimals
     *
     * @param $price
     * @return string
     */
    private static function getPrice($price)
    {
        return number_format((float) $price, 2, '.', '');
    }

}
