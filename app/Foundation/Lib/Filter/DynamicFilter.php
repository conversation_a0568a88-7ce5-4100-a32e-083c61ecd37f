<?php

namespace Foundation\Lib\Filter;

use Illuminate\Support\Arr;

final class DynamicFilter
{

    public static function generateArrsOfWhere($category, $values): array
    {
        $fieldIds = [];

        if ($category && method_exists($category, 'fields')) {

            $filters = $category
                ->fields()
                ->where('is_added_to_filter', 1)
                ->pluck('field_name', 'id')
                ->toArray();

            $values = Arr::only($values, array_values($filters));
            foreach  ($filters as $id => $filter) {
                if (in_array($filter, array_keys($values))) {
                    $fieldIds[] = $id;
                }
            }
        }

        return [
            'fields' => $fieldIds,
            'keys'   => array_keys($values),
            'values' => array_values($values),
        ];
    }

    public static function generate($data): array
    {
        $filters = [];

        foreach ($data as $item) {
            $filters[]         =  [
                'name'         => $item->field_name,
                'display_name' => $item->getDisplayName(),
                'options'      => DynamicFilter::generateOptions($item->multiple_options, $item->field_name),
            ];
        }
        return $filters;
    }

    private static function generateOptions($options, $title): array
    {
        $props = [];

        $options = explode(',', $options);

        if (count($options)) {

            foreach ($options as $option) {
                $props[] = [
                    'title'   => $title,
                    'name'    => ltrim($option),
                    'value'   => (string) ltrim($option),
                ];
            }
        }

        return $props;
    }

}
