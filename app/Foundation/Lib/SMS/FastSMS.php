<?php

namespace Foundation\Lib\SMS;

use Foundation\Services\MessageLogService;
use Illuminate\Support\Facades\App;
use Modules\Sms\Gateway\Fast\FastSmsConfig;

class FastSMS
{
    const MESSAGE_SEND = 1;

    const FAILED = 2;

    public function sendDueCreateMessage($customer, $dueModel, $dueAmount, $dueClearanceData)
    {
        if (FastSmsConfig::getStatus()) {
           $message = $this->getMessage($customer->full_name, $dueAmount);

           $this->sendMessage($message, $customer->phone_number);
        }

    }

    public function sendClearDueSMS($customer, $dueAmount)
    {
        if (FastSmsConfig::getStatus()) {

            $message = $this->getDueClearMessage($customer->full_name, $dueAmount);

            $this->sendMessage($message, $customer->phone_number);
        }
    }

    private function getMessage($fullName, $dueAmount): string
    {
        return  "Hello " . $fullName . ", Rs " . nrp($dueAmount) . " has been added to your Due Amount. Thank You ! ". config('app.name')." TEAM.";
    }

    public function sendMessage(string $message, $phoneNumber)
    {
        $status = FastSMS::FAILED;

        if(FastSmsConfig::getStatus() && App::environment('production')) {

            $status = FastSMS::MESSAGE_SEND;

            $api_url = http_build_query(array(
                    'mobile_number' => $phoneNumber,
                    'message' => $message,
                    'is_otp' => 0
                )
            );

            $url = FastSmsConfig::getEndpoint();

            # Make the call using API.
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

            $token = FastSmsConfig::getToken();
            $headers = [
                'apikey: ' . $token,
            ];

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            // Response
            $response = curl_exec($ch);
            curl_close($ch);
        }

       return app(MessageLogService::class)->createdLog($message, $phoneNumber, $status);
    }

    private function getDueClearMessage($full_name, $dueAmount): string
    {
        return  "Hello " . $full_name . ", Rs " . $dueAmount . " has been clear from your Due Amount. Thank You ! ". config('app.name')." TEAM.";

    }

}