<?php

namespace Foundation\Builders\Aggregations;

use Foundation\Services\FlightTypeService;
use Foundation\Services\PaymentService;
use Foundation\Services\PaymentTypeService;
use Foundation\Services\UserService;
use Foundation\Services\VendorService;
use Illuminate\Http\Response;

final class OverallService
{

    /**
     * @return mixed
     */
    public function getVendors()
    {
        return resolve(VendorService::class)->pluck();
    }

    /**
     * @return mixed
     */
    public function getFlightTypes()
    {
        return resolve(FlightTypeService::class)->getAllFlightType();
    }    /**
     * @return mixed
     */
    public function getActiveFlightTypes()
    {
        return resolve(FlightTypeService::class)->getActiveFlightType();
    }

    /**
     * @param $id
     * @return mixed
     */
    public function findFlightType($id)
    {
        return resolve(FlightTypeService::class)->findOrFail($id);
    }

    /**
     * @return mixed
     */
    public function getUsers()
    {
        return resolve(UserService::class)->getAllUser();
    }

    public function getPaymentTypes()
    {
        return app(PaymentTypeService::class)->getAllPaymentType();
    }

    public function getPayments()
    {
        return app(PaymentService::class)->getPayments();
    }
}
