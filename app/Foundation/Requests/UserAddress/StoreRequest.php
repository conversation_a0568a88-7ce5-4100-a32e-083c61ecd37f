<?php


namespace Foundation\Requests\UserAddress;


use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{

    public function authorize()
    {

        return true;
    }

    public function rules()
    {
        return [
            'id' => 'sometimes|nullable|integer',
            'name' => 'required |max:50',
            'email' => 'required|email|max:50',
            'first_name' => 'sometimes|nullable|max:25',
            'middle_name' => 'sometimes|nullable|max:25',
            'last_name' => 'sometimes|nullable|max:25',
            'phone' => 'required |max:14 |min:10',
            'company' => 'sometimes|nullable|max:50',
            'country' => 'sometimes|nullable |max:25',
            'city' => 'required |max:25',
            'state' => 'required |max:25',
            'zip_code' => 'required|integer',
            'is_primary' => 'sometimes|nullable',

        ];
    }

}
