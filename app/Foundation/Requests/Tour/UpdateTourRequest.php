<?php

namespace Foundation\Requests\Tour;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateTourRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|unique:tours,name,' . $this->get('id'),
            'category_id' => 'required|numeric',
            'price' => 'required|numeric',
            'days' => 'required|numeric',
            'night' => 'required|numeric',
            'description' => 'required|string',
            'image' => 'required|string',
            'gallery_type' => 'required|array',
            'gallery' => 'required|array',
            'caption' => 'nullable|sometimes|array',
            'alt_text' => 'nullable|sometimes|array',
            'attribute_value.*' => 'nullable|string',
            'attribute.*' => 'nullable|string',
        ];
    }
}
