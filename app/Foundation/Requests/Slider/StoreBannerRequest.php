<?php

namespace App\Foundation\Requests\Slider;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreBannerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'required|string',
            'title_plain' => 'required|string',
            'title_colored' => 'nullable|string',
            'sub_title' => 'required|string',
            'layout' => 'required|numeric|between:1,4',
            'status' => 'required|boolean',
            'images' => 'required|array',
            'images.*.image' => 'required|string',
            'images.*.link' => 'nullable|string',
        ];
    }
}
