<?php

namespace Foundation\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'full_name' => 'required|min:2|max:50',
            'email' => 'sometimes|nullable|email|unique:users',
            'phone_number' => 'required|numeric|digits:10|unique:users,phone_number',
            'status' => 'sometimes|nullable',
            'image' => 'sometimes|nullableimage|mimes:jpeg,jpg,png',
        ];
    }

}
