<?php

namespace Foundation\Requests\Page;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $title = $this->title;
        return [
            'page_name' => 'required | unique:pages,page_name|max:150',
            'page_heading' => 'required',
            'page_content'  => 'required',
            'image' => 'nullable|string',
        ];
    }
}
