<?php

namespace Foundation\Models;

use Neputer\Supports\BaseModel as Model;

/**
 * Class Setting
 * @package Foundation\Models
 */
class Setting extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'key', 'value', 'updated_by'
    ];

    public function update(array $attributes = [], array $options = [])
    {
        return parent::update($attributes, $options); // TODO: Change the autogenerated stub
    }

}
