<?php

namespace Foundation\Models;

use Neputer\Supports\BaseModel as Model;

/**
 * Class Bank
 * @package Foundation\Models
 */
class UserAddress extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'company',
        'country',
        'city',
        'state',
        'zip_code',
        'is_primary',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
