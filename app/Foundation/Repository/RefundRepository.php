<?php

namespace Foundation\Repository;

use App\AirTicket;
use App\FollowUp;
use Exception;
use Foundation\Lib\paymentHistory\PaymentStatus;
use Foundation\Models\PaymentHistory;
use Foundation\Models\Remittance;
use Foundation\Repository\Eloquent\RefundRepositoryInterface;
use Illuminate\Http\RedirectResponse;
use Neputer\Supports\BaseService;

class RefundRepository extends BaseService implements RefundRepositoryInterface
{
    /**
     * @var PaymentHistory
     */
    protected PaymentHistory $model;

    /**
     * @param PaymentHistory $paymentHistory
     */
    public function __construct(PaymentHistory $paymentHistory)
    {
        $this->model = $paymentHistory;
    }

    /**
     * @param $paymentHistoryId
     * @return RedirectResponse
     * @throws Exception
     */
    public function refundPayment($paymentHistoryId): RedirectResponse
    {
        $paymentHistory = $this->model->findOrFail($paymentHistoryId);

        $modal = $paymentHistory->paymentable;

        switch (get_class($modal)) {
            case Remittance::class:
            case AirTicket::class:
                $modal = $paymentHistory->getFollowup();

                if ($modal['total_amount'] == ($modal['due_amount'] + $paymentHistory['amount'])) {
                    $modal['due_amount'] = $modal['total_amount'];
                } else {
                    $modal['due_amount'] += $paymentHistory['amount'];
                }
                break;
        }

        $modal['paid'] = PaymentStatus::UNPAID;
        $modal->save();
        $paymentHistory->delete();

        return redirect()->back();
    }
}
