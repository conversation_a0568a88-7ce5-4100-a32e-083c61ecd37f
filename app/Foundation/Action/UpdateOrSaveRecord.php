<?php

namespace App\Foundation\Action;

use App\TrialBalanceHistory;

class UpdateOrSaveRecord
{

    public function updateOrSave(array $data)
    {
        $trialBalanceHistory = TrialBalanceHistory::where('date', today())->where('user_id', auth()->id())->first();

        TrialBalanceHistory::query()->updateOrCreate(
            [
                'id' => optional($trialBalanceHistory)->id
            ],
            [
                'records' => $data,
                'user_id' => auth()->id(),
                'date' => today(),
            ]
        );

    }

}
