<?php

namespace Foundation;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON><PERSON>\Email\Provider as EmailServiceProvider;
use <PERSON><PERSON>les\Sms\Provider as SmsServiceProvider;
/**
 * Class Provider
 * @package Foundation
 */
final class Provider extends ServiceProvider
{

    /**
     * Sharing the data to the views
     *
     * key as view path & value as the view composer
     *
     * @var array
     */
    private static $shareable = [

        ];

    /**
     * Aliasing the component
     *
     * key as view path & value as the alias
     *
     * @var array
     */
    private static $components = [
        'admin.common.components.summary'  => 'summary',
        'admin.common.breadcrumbs'         => 'breadcrumb',
        'admin.common.advanced-filter'     => 'filter',
        'admin.common.summary-script'      => 'summaryscripts',
        'pages.partials.primary-menu'      => 'primary',
        'pages.partials.footer-primary-menu'      => 'fprimary',
        'admin.common.components.box'      => 'box',
        'admin.common.components.piechart' => 'piechart',
        'gaming.pages.partials.menu'      => 'gamingmenu',
    ];

    /**
     * Perform post-registration booting of services.
     *
     * @return void
     */
    public function boot()
    {
        foreach (Provider::$components as $view => $alias ) {
            \Blade::aliasComponent($view, $alias);
        }

        foreach (Provider::$shareable as $view => $composer ) {
            \View::composer( $view, $composer );
        }

        $this->mergeConfigFrom(__DIR__.'/Config/setting.php', 'setting');
        $this->mergeConfigFrom(__DIR__.'/Config/Notifier/notifier.php', 'notifier');
        $this->mergeConfigFrom(__DIR__.'/Config/ecommerce.php', 'ecommerce');
    }

    /**
     * Register bindings in the container.
     *
     * @return void
     */
    public function register()
    {

        $this->app->register(EmailServiceProvider::class);
        $this->app->register(SmsServiceProvider::class);
        $this->app->register(EventServiceProvider::class);

        $this->commands([]);
    }

}
