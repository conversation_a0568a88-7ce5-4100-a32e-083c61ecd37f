<?php

use Foundation\Services\UserService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Intervention\Image\Facades\Image;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

if (! function_exists('get_webp_image_url')) {
    /**
     * @param string|null $url
     * @return string
     */
    function get_webp_image_url(?string $originalImageUrl): string
    {
        if (is_null($originalImageUrl)) {
            return '';
        }

        $pathInfo = pathinfo($originalImageUrl);
        if (isset($pathInfo['extension']) && strtolower($pathInfo['extension']) === 'svg') {
            return ''; // Do not generate WebP for SVG
        }

        // 1. Determine paths
        $originalPath = parse_url($originalImageUrl, PHP_URL_PATH);
        // Ensure the path starts from 'storage' and not the root of the server
        $sourcePath = public_path(ltrim($originalPath, '/'));

        if (!File::exists($sourcePath)) {
            return ''; // Return empty if the source image doesn't exist
        }

        $pathInfo = pathinfo($sourcePath);
        $dirname = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];

        // Create a 'cache' subdirectory if it doesn't exist
        $cacheDir = $dirname . '/cache';
        if (!File::isDirectory($cacheDir)) {
            File::makeDirectory($cacheDir, 0755, true, false);
        }

        $webpPath = $cacheDir . '/' . $filename . '.webp';

        // 2. Check if WebP image exists
        if (!File::exists($webpPath)) {
            try {
                ini_set('memory_limit', '256M');
                // 3. If not, create and save it
                $image = Image::make($sourcePath)->encode('webp', 90);
                $image->save($webpPath);
                ini_restore('memory_limit');
            } catch (\Exception $e) {
                // If conversion fails, return empty to avoid broken links
                return '';
            }
        }

        // 4. Return the public URL to the WebP image
        $webpUrlPath = str_replace(public_path(), '', $webpPath);
        return asset($webpUrlPath);
    }
}

if (! function_exists('get_image_srcset')) {
    /**
     * @param string|null $originalImageUrl
     * @return string
     */
    function get_image_srcset(?string $originalImageUrl): string
    {
        if (is_null($originalImageUrl)) {
            return '';
        }

        $pathInfo = pathinfo($originalImageUrl);
        if (isset($pathInfo['extension']) && strtolower($pathInfo['extension']) === 'svg') {
            return ''; // Do not generate srcset for SVG
        }

        $originalPath = parse_url($originalImageUrl, PHP_URL_PATH);
        $sourcePath = public_path(ltrim($originalPath, '/'));

        if (!File::exists($sourcePath)) {
            return '';
        }

        $pathInfo = pathinfo($sourcePath);
        $dirname = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? 'jpg';

        $cacheDir = $dirname . '/cache';
        if (!File::isDirectory($cacheDir)) {
            File::makeDirectory($cacheDir, 0755, true, false);
        }

        $sizes = [
            'sm' => 320,
            'md' => 768,
            'lg' => 1200,
        ];

        $srcsetStrings = [];

        foreach ($sizes as $suffix => $width) {
            $resizedPath = "{$cacheDir}/{$filename}-{$suffix}.{$extension}";

            if (!File::exists($resizedPath)) {
                try {
                    ini_set('memory_limit', '256M');
                    $image = Image::make($sourcePath);
                    $image->resize($width, null, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                    $image->save($resizedPath);
                    ini_restore('memory_limit');
                } catch (\Exception $e) {
                    continue; // Skip if a size fails to generate
                }
            }

            $resizedUrl = asset(str_replace(public_path(), '', $resizedPath));
            $srcsetStrings[] = "{$resizedUrl} {$width}w";
        }

        return implode(', ', $srcsetStrings);
    }
}



if (!function_exists('cart')):
    function cart()
    {
        return app(\Neputer\Supports\Cart\Cart::class);
    }
endif;

if (!function_exists('get_image_url')):
    function get_image_url($folder, $imageName)
    {
        $filePath = 'storage' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . $folder . DIRECTORY_SEPARATOR . $imageName;

        if (file_exists(public_path() . DIRECTORY_SEPARATOR . $filePath)) {
            if (!is_null($imageName)) {
                return asset($filePath);
            } else {
                return asset('images/no_image.png');
            }
        } else {
            return asset('images/no_image.png');
        }
    }
endif;
if (!function_exists('get_image_full_url')):
    function get_image_full_url($imageName): string
    {
        $imageName = ltrim($imageName, '/\\');
        $filePath = 'storage' . DIRECTORY_SEPARATOR . $imageName;

        if (file_exists(public_path() . DIRECTORY_SEPARATOR . $filePath)) {
            if (!is_null($imageName)) {
                return asset($filePath);
            } else {
                return asset('images/no_image.png');
            }
        } else {
            return asset('images/no_image.png');
        }
    }
endif;
if (!function_exists('get_resize_image')):
    function get_resize_image($imageName, $width, $height)
    {
        $filePath = 'storage' . DIRECTORY_SEPARATOR . $imageName;

        if (file_exists(public_path() . DIRECTORY_SEPARATOR . $filePath)) {
            if (!empty($imageName)) {
                return Image::make($filePath)->resize($width, $height)->encode('data-url')->encoded;
            } else {
                return asset('images/no_image.png');
            }
        } else {
            return asset('images/no_image.png');
        }
    }
endif;

if (!function_exists('generateNanoId')) {
    function generateNanoId()
    {
        $nano = new \Hidehalo\Nanoid\Client();
        $alphabet = 'abcdefghijklmnopqrstuvwxys1234567890';
        return $nano->formattedId($alphabet);
    }
}

if (!function_exists('getId')) {
    function getId($slug)
    {
        return app('db')->table('roles')->where('slug', $slug)->value('id');
    }
}
if (!function_exists('checkValidCustomer')) {
    function checkValidCustomer($request): bool
    {
        $dueAmount = Arr::get($request, 'due_amount');
        $amount = Arr::get($request, 'amount');

        if ($amount < $dueAmount) {
            return false;
        }

        if (Arr::has($request, 'customer_id') && Arr::get($request, 'customer_id')) {
            $user = App(UserService::class)->findById($request['customer_id']);

            if (!$user) {
                return false;
            }
        }

        return true;
    }
}

if (!function_exists('checkAjax')) {
    function checkAjax($request)
    {
        abort_if(!$request->ajax(), ResponseAlias::HTTP_FORBIDDEN);
    }
}

if (!function_exists('get_site_config_value')) {
    function get_site_config_value($key)
    {
        return app('db')->table('settings')->where('key', $key)->value('value');
    }
}

if (!function_exists('render_image')) {
    /**
     * Render an HTML image element with WebP and responsive sources, with SVG support.
     *
     * @param string $imageUrl The URL of the image.
     * @param array $attributes An associative array of HTML attributes for the img tag.
     * @return string The HTML for the image.
     */
    function render_image($imageUrl, $attributes = [])
    {
        if (empty($imageUrl)) {
            return '';
        }

        $fullUrl = get_image_full_url($imageUrl);
        $isSvg = str_ends_with(strtolower($fullUrl), '.svg');

        $attributeString = '';
        if (is_array($attributes)) {
            foreach ($attributes as $key => $value) {
                $attributeString .= $key . '="' . htmlspecialchars($value) . '" ';
            }
        }

        if ($isSvg) {
            return '<img src="' . e($fullUrl) . '" ' . trim($attributeString) . '>';
        }

        $webpUrl = get_webp_image_url($fullUrl);
        $srcset = get_image_srcset($fullUrl);

        $picture = '<picture>';
        $picture .= '<source type="image/webp" srcset="' . e($webpUrl) . '">';
        $picture .= '<img src="' . e($fullUrl) . '" srcset="' . e($srcset) . '" ' . trim($attributeString) . '>';
        $picture .= '</picture>';

        return $picture;
    }
}

if (!function_exists('get_youtube_video_id')) :
    function get_youtube_video_id($url)
    {
        // Parse the URL and extract the query string and path
        $parsedUrl = parse_url($url);

        // Check if the URL contains a query string with "v" parameter
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            if (isset($queryParams['v'])) {
                return $queryParams['v'];
            }
        }

        // Check for a video ID in the path for shortened URLs
        if (isset($parsedUrl['path'])) {
            $pathSegments = explode('/', trim($parsedUrl['path'], '/'));
            if (count($pathSegments) === 1 && strlen($pathSegments[0]) === 11) {
                return $pathSegments[0];
            }
        }

        // Return null if no video ID is found
        return null;
    }
endif;

/**
 * Get email receivers
 *
 * @param string $type
 * @return array
 */
if (!function_exists('get_email_receivers')) {
    function get_email_receivers($type)
    {
        $adminEmails = get_site_config_value('email_receivers');
        $emails = json_decode($adminEmails, true);

        $inquiryEmails = array_filter($emails[$type] ?? [], function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });
        return $inquiryEmails;
    }
}
