<?php

namespace App\Mail;

use Foundation\Models\TicketEvent;
use Foundation\Lib\TicketEvent as TicketEventLib;
use Foundation\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TicketActivityEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var TicketEvent
     */
    public $event;

    /**
     * Create a new message instance.
     *
     * @param TicketEvent $event
     */
    public function __construct(TicketEvent $event)
    {
        $this->event = $event;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'Neputer Notification')
            ->subject('New Ticket Activity | Ticket ' . ucfirst(TicketEventLib::$type[$this->event->type]))
            ->markdown('email.ticket-activity');
    }
}
