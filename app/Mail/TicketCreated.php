<?php

namespace App\Mail;

use Foundation\Lib\Ticket as TicketLib;
use Foundation\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TicketCreated extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var Ticket
     */
    public $ticket;

    /**
     * Create a new message instance.
     *
     * @param Ticket $ticket
     */
    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'Kunyo Notification')
            ->subject('New Ticket Created | ' . TicketLib::$priority[$this->ticket->priority])
            ->markdown('email.ticket-created');
    }
}
