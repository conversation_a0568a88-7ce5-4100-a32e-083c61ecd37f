<?php

namespace App\Mail;

use App\Models\Enquiry;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewInquiryNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The inquiry instance.
     *
     * @var \App\Models\Enquiry
     */
    public $inquiry;

    /**
     * Create a new message instance.
     *
     * @param  \App\Models\Enquiry  $inquiry
     * @return void
     */
    public function __construct(Enquiry $inquiry)
    {
        $this->inquiry = $inquiry;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('New Travel Inquiry from ' . $this->inquiry->full_name)
                    ->markdown('emails.inquiries.admin-notification');
    }
}
