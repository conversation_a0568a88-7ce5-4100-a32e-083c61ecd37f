<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class FollowupMail extends Mailable
{
    use Queueable, SerializesModels;

    public $duePayment;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($duePaymentList)
    {
        $this->duePayment = $duePaymentList;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $paymentList = $this->duePayment;
        return $this->view('email.followupSend', compact('paymentList'));
    }
}
