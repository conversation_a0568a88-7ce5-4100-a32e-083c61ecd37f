@push('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/css/intlTelInput.min.css">
@endpush

@push('js')
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/js/intlTelInput.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const inputs = document.querySelectorAll("input[data-intl-tel-input]");

            inputs.forEach(function (input) {
                const iti = window.intlTelInput(input, {
                    initialCountry: "auto",
                    geoIpLookup: function (callback) {
                        fetch('https://ipapi.co/json')
                            .then(res => res.json())
                            .then(data => callback(data.country_code))
                            .catch(() => callback("us"));
                    },
                    utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.2.1/js/utils.js",
                    autoPlaceholder: "polite",
                    nationalMode: false, // Important: shows full number in input
                    formatOnDisplay: true,
                    separateDialCode: false
                });

                input._iti = iti;

                // Format number with country code before submit
                input.form?.addEventListener("submit", function (e) {
                    const phoneVal = input.value.trim();
                    if (phoneVal && !iti.isValidNumber()) {
                        e.preventDefault();
                        return;
                    }

                    // Replace input with full E.164 format
                    input.value = iti.getNumber(); // example: +9779812345678
                });
            });
        });
    </script>
@endpush
