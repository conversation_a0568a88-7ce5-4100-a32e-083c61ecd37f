<script src="{{ asset('assets/js/common_scripts.js') }}"></script>
<script src="{{ asset('assets/js/main.js') }}"></script>
<script src="{{ asset('assets/js/validate.js') }}"></script>
<script src="{{ asset('assets/js/custom.js') }}"></script>
<!-- DATEPICKER  -->
<script>
    $(function() {
        'use strict';
        $('input[name="dates"]').daterangepicker({
            autoUpdateInput: false,
            minDate: new Date(),
            locale: {
                cancelLabel: 'Clear'
            }
        });
        $('input[name="dates"]').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format(
                'YYYY-MM-DD'));
        });
        $('input[name="dates"]').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    });
</script>

<!-- INPUT QUANTITY  -->
{{-- <script src="{{ asset('assets/js/input_qty.js') }}"></script> --}}
{{-- iconify js --}}
<script src="https://code.iconify.design/iconify-icon/1.0.7/iconify-icon.min.js"></script>



{{-- plyr js --}}
<script src="https://cdn.plyr.io/3.7.8/plyr.js"></script>
@stack('js')

<script>
    if (typeof $.validator !== 'undefined' && typeof $.validator.addMethod === 'function') {
        $.validator.addMethod('intlTelNumber', function(value, element) {
            const iti = element._iti;
            return this.optional(element) || (iti && iti.isValidNumber());
        }, 'Please enter a valid phone number.');
    }
</script>

<script>
    function handleChangeCurrency(currency) {
        $.ajax({
            type: 'POST',
            url: '{{ route('change-currency') }}',
            data: {
                _token: '{{ csrf_token() }}',
                currency: currency
            },
            success: function(data) {
                window.location.reload();
            }
        })
    }
</script>


<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>



{{-- home banner js  --}}
<script>
    var swiper = new Swiper(".popularTourSlider", {
        slidesPerView: 2,
        spaceBetween: 10,
        navigation: {
            nextEl: ".swiper-button-nexts",
            prevEl: ".swiper-button-prevs",
        },
        breakpoints: {
            567: {
                slidesPerView: 2,
                spaceBetween: 14,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 14,
            },
            992: {
                slidesPerView: 3,
                spaceBetween: 14,
            },
            1200: {
                slidesPerView: 4,
                spaceBetween: 24,
            },
        },
    });
</script>

<script>
    $(document).ready(function() {


        const $megamenuButtons = $('.megamenu_togglebutton');
        const $megamenuContents = $('.dropdownmenusection');
        const $dropdownButtons = $('.dropdownbutton');
        const $dropdownContents = $('.drop-section');
        const $overlay = $('#megamenuoverlay');
        const $header = $('.header');
        const $closeMegaMenuButtons = $('.close__megamenu--btn');

        // Function to close all menus and remove active-menu class
        function closeAllMenus() {
            $megamenuContents.removeClass('active');
            $dropdownContents.removeClass('active');
            $megamenuButtons.removeClass('active-menu');
            $dropdownButtons.removeClass('active-menu');
            $overlay.removeClass('show');
            $header.removeClass('show'); // Hide overlay
        }

        // Event listener for Megamenu buttons
        $megamenuButtons.on('click', function(event) {
            event.stopPropagation();

            const $button = $(this);
            const $content = $megamenuContents.eq($megamenuButtons.index($button));
            const isMegaMenuOpen = $content.hasClass('active');

            // Close all menus first
            closeAllMenus();

            // Toggle the clicked megamenu if it was not already open
            if (!isMegaMenuOpen) {
                $content.addClass('active');
                $button.addClass('active-menu');
                $overlay.addClass('show');
                $header.addClass('show'); // Show overlay
            }
        });

        // Event listener for Dropdown buttons
        $dropdownButtons.on('click', function(event) {
            event.stopPropagation(); // Prevent bubbling to document click

            const $button = $(this);
            const $content = $dropdownContents.eq($dropdownButtons.index($button));
            const isDropdownOpen = $content.hasClass('active');

            // Close all menus first
            closeAllMenus();

            // Toggle the clicked dropdown if it was not already open
            if (!isDropdownOpen) {
                $content.addClass('active');
                $button.addClass('active-menu');
            }
        });

        // Event listener for the close button inside megamenu
        $closeMegaMenuButtons.on('click', function(event) {
            event.stopPropagation(); // Prevent bubbling to document click

            const $button = $(this);
            const $megamenuContent = $button.closest('.dropdownmenusection');
            const index = $megamenuContents.index($megamenuContent);

            // Close only the associated megamenu content
            $megamenuContent.removeClass('active');

            // Remove active class from the corresponding toggle button
            if (index !== -1) {
                $megamenuButtons.eq(index).removeClass('active-menu');
            }

            // Check if other menus are still open, if not, hide overlay
            if (!$('.dropdownmenusection.active').length) {
                $overlay.removeClass('show');
                $header.removeClass('show');

            }
        });
        $('.tab-content.navmenucontent').on('click', function(event) {
            event.stopPropagation();
        });

        // Close all menus when clicking outside of them
        $(document).on('click', function(event) {
            if (!$(event.target).closest(
                    '.megamenu_togglebutton, .dropdownbutton, .dropdownmenusection, .drop-section')
                .length) {
                closeAllMenus();
            }
        });
    });;


    document.querySelectorAll('.tab_menu-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            event.stopPropagation();
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('.nav-link'); // All tab buttons
        const tabPanes = document.querySelectorAll('.tab-pane'); // All content panes

        // Function to remove 'active' class from all tabs and panes
        function clearActiveStates() {
            navLinks.forEach(link => link.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active', 'show'));
        }

        // Add click event listener to each tab link
        navLinks.forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault(); // Prevent default link behavior

                // Clear active states
                clearActiveStates();

                // Add 'active' to the clicked tab
                link.classList.add('active');


                const targetId = link.getAttribute('href'); // Get href attribute value
                const targetPane = document.querySelector(targetId);
                if (targetPane) {
                    targetPane.classList.add('active', 'show');
                }
            });
        });
    });

    // header sm js

    $(document).ready(function() {
        // Function to open a submenu when clicking on a menu item with the 'has-menu' class.
        $('ul.menu-list').on('click', '.has-menu', function(e) {
            e.preventDefault();

            const $submenu = $(this).next('ul'); // Find the submenu right after the clicked item.
            const $parentMenu = $(this).closest('ul'); // Get the parent menu.

            // Hide all other sibling submenus at the same level.
            $parentMenu.find('ul').not($submenu).removeClass('active').slideUp();

            // Show the clicked submenu with a smooth animation.
            $submenu.addClass('active').slideDown();

            // Show the back button in the submenu.
            $submenu.closest('li').find('.back-btn').first().show();
        });

        // Back button functionality to go back to the parent menu.
        $('ul').on('click', '.back-btn', function(e) {
            e.preventDefault();

            const $currentSubmenu = $(this).closest('ul'); // Get the current submenu.

            // Hide the current submenu.
            $currentSubmenu.removeClass('active').slideUp();

            // Hide the back button.
            $(this).hide();

            // Optionally show the parent menu.
            const $parentMenu = $currentSubmenu.closest('li').parent('ul');
            $parentMenu.addClass('active').slideDown();
        });

        // Open offcanvas
        $('.menu-bar').on('click', function(e) {
            e.preventDefault();
            $('#menuoffcanvasExample').addClass('active');
            $('body').addClass('body-fixed'); // Prevent body scroll
        });
        // Close offcanvas
        $('.btn-closes').on('click', function() {
            resetMenu();
            $('.offcanvas').removeClass('active');
            $('body').removeClass('body-fixed'); // Allow body scroll
        });
        // Close all open submenus when clicking outside the off-canvas.
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.offcanvas, .menu-bar').length) {
                resetMenu(); // Reset the menu to its initial state.
                $('.offcanvas').removeClass('active');
                $('body').removeClass('body-fixed');
            }
        });

        // Close the off-canvas when the close button is clicked.
        $('.btn-closes').on('click', function() {
            resetMenu(); // Reset the menu state.
            $('.offcanvas').removeClass('active'); // Close the off-canvas.
        });

        // Reset all menus to the initial state.
        function resetMenu() {
            // Hide all submenus and remove the 'active' class.
            $('ul ul').removeClass('active').hide();

            // Hide all back buttons.
            $('.back-btn').hide();
        }
    });


    //itinerary overview showmore and less

    $(document).ready(function() {
        const $description = $(".tour-overview__description");
        const $contents = $description.find(".description__contents");
        const $toggleBtnWrapper = $description.find(".tour-overview__toggleBtnwrapper");
        const $toggleBtn = $description.find(".tour-overview__toggle--btn");


        const checkHeight = () => {
            if ($contents.prop("scrollHeight") <= 700) {
                $toggleBtnWrapper.hide();
                $contents.css({
                    height: "auto"
                });
            } else {
                $toggleBtnWrapper.show();
                $contents.css({
                    height: "700px",
                    overflow: "hidden"
                });
            }
        };

        $toggleBtn.on("click", function() {
            if ($description.hasClass("expanded")) {
                $description.removeClass("expanded");
                $contents.animate({
                    height: "260px"
                }, 300, function() {
                    $contents.css({
                        overflow: "hidden"
                    });
                });
                $toggleBtn.html(`Read more
          <span class="icon-wrapper">
            <iconify-icon icon="uil:angle-down"></iconify-icon>
          </span>`);
            } else {
                $description.addClass("expanded");
                $contents.animate({
                    height: $contents.prop("scrollHeight")
                }, 300, function() {
                    $contents.css({
                        overflow: "visible"
                    });
                });
                $toggleBtn.html(`Read less
          <span class="icon-wrapper">
            <iconify-icon icon="uil:angle-up"></iconify-icon>
          </span>`);
            }
        });


        checkHeight();
    });



    $(document).ready(function() {
        const $itineraryAccordion = $('#itineraryaccordion');
        const $toggleBtn = $('#itinerary-accordion-toggle');
        const $accordions = $itineraryAccordion.find('.collapse');
        const $accordionButtons = $itineraryAccordion.find('.accordion-button');


        let isExpanded = false;

        $toggleBtn.on('click', function() {
            if (isExpanded) {
                // If all are expanded, collapse them
                $accordions.removeClass('show').attr('aria-expanded', 'false');
                $accordionButtons.addClass('collapsed').attr('aria-expanded', 'false');
                $toggleBtn.text('Expand all');
                isExpanded = false;
            } else {
                // If not all are expanded, expand them
                $accordions.addClass('show').attr('aria-expanded', 'true');
                $accordionButtons.removeClass('collapsed').attr('aria-expanded', 'true');
                $toggleBtn.text('Close all');
                isExpanded = true;
            }
        });
    });



    var swiper = new Swiper(".activities__card--grid", {
        slidesPerView: 2,
        spaceBetween: 12,

        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
        breakpoints: {
            567: {
                slidesPerView: 2,
                spaceBetween: 12,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 12,
            },
            992: {
                slidesPerView: 3,
                spaceBetween: 12,
            },
            1200: {
                slidesPerView: 3,
                spaceBetween: 12,
            },
        },

    });

    // Initialize Plyr
    const player = new Plyr('#tripvideosection');

    // Add event listeners to show/hide controls
    player.on('play', () => {
        player.elements.container.classList.add('plyr--controls-visible');
    });

    player.on('pause', () => {
        player.elements.container.classList.remove('plyr--controls-visible');
    });

    player.on('ended', () => {
        player.elements.container.classList.remove('plyr--controls-visible');
    });




    $(document).ready(function() {
        const $itineraryAccordion = $('#faqAccordion');
        const $toggleBtn = $('#faqaccordion-toggle');
        const $accordions = $itineraryAccordion.find('.collapse');
        const $accordionButtons = $itineraryAccordion.find('.btn-link');


        let isExpanded = false;

        $toggleBtn.on('click', function() {
            if (isExpanded) {
                // If all are expanded, collapse them
                $accordions.removeClass('show').attr('aria-expanded', 'false');
                $accordionButtons.addClass('collapsed').attr('aria-expanded', 'false');
                $toggleBtn.text('Expand all');
                isExpanded = false;
            } else {
                // If not all are expanded, expand them
                $accordions.addClass('show').attr('aria-expanded', 'true');
                $accordionButtons.removeClass('collapsed').attr('aria-expanded', 'true');
                $toggleBtn.text('Close all');
                isExpanded = true;
            }
        });
    });

    var swiper = new Swiper(".similartripslider", {
        slidesPerView: 2,
        spaceBetween: 14,
        breakpoints: {
            567: {
                slidesPerView: 2,
                spaceBetween: 16,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 16,
            },
            992: {
                slidesPerView: 3,
                spaceBetween: 14,
            },
            1200: {
                slidesPerView: 4,
                spaceBetween: 20,
            },
        },
    });
</script>

<script>
    class ModalSearch {
        constructor() {
            this.modal = document.getElementById('searchModal');
            this.toggleBtn = document.getElementById('searchToggleBtnPage');
            this.closeBtn = document.getElementById('searchModalClose');
            this.overlay = this.modal?.querySelector('.search-modal-overlay');
            this.input = document.getElementById('modalSearchInput');
            this.results = document.getElementById('modalSearchResults');
            this.spinner = document.getElementById('modalSearchSpinner');
            this.popularSection = document.getElementById('popularSection');
            this.resultsSection = document.getElementById('resultsSection');
            
            this.debounceTimer = null;
            this.currentIndex = -1;
            this.searchResults = [];
            
            this.init();
        }
        
        init() {
            if (!this.modal) return;
            
            // Toggle button click
            this.toggleBtn?.addEventListener('click', (e) => {
                e.preventDefault();
                this.openModal();
            });
            
            // Close button click
            this.closeBtn?.addEventListener('click', () => this.closeModal());
            
            // Overlay click
            this.overlay?.addEventListener('click', () => this.closeModal());
            
            // Input events
            this.input?.addEventListener('input', (e) => this.handleInput(e));
            this.input?.addEventListener('keydown', (e) => this.handleKeydown(e));
            
            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                    e.preventDefault();
                    this.openModal();
                }
                if (e.key === 'Escape' && this.modal.classList.contains('active')) {
                    this.closeModal();
                }
            });
        }
        
        openModal() {
            this.modal.style.display = 'flex';
            setTimeout(() => {
                this.modal.classList.add('active');
                this.input?.focus();
            }, 10);
            document.body.style.overflow = 'hidden';
        }
        
        closeModal() {
            this.modal.classList.remove('active');
            setTimeout(() => {
                this.modal.style.display = 'none';
                this.input.value = '';
                this.showPopularCategories();
            }, 300);
            document.body.style.overflow = '';
        }
        
        handleInput(e) {
            const query = e.target.value.trim();
            
            clearTimeout(this.debounceTimer);
            
            if (query.length < 2) {
                this.showPopularCategories();
                return;
            }
            
            this.debounceTimer = setTimeout(() => {
                this.search(query);
            }, 300);
        }
        
        handleKeydown(e) {
            const items = this.results.querySelectorAll('.modal-search-result-item');
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.navigateResults(1, items);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.navigateResults(-1, items);
                    break;
                case 'Enter':
                    e.preventDefault();
                    this.selectResult(items);
                    break;
            }
        }
        
        async search(query) {
            this.showSearchResults();
            this.showSpinner();
            
            try {
                const response = await fetch(`{{ route('search') }}?query=${encodeURIComponent(query)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (!response.ok) throw new Error('Search failed');
                
                const data = await response.json();
                this.searchResults = data.tours || [];
                this.displayResults();
                
            } catch (error) {
                console.error('Search error:', error);
                this.displayError();
            } finally {
                this.hideSpinner();
            }
        }
        
        displayResults() {
            this.currentIndex = -1;
            
            if (this.searchResults.length === 0) {
                this.results.innerHTML = '<div class="search-no-results">No tours found for your search</div>';
            } else {
                this.results.innerHTML = this.searchResults.map((tour, index) => {
                    const imageUrl = tour.image ? `{{ asset('storage') }}/${tour.image}` : '{{ asset('assets/images/placeholder-tour.jpg') }}';
                    const categoryName = tour.category?.category_title || 'Tour';
                    const description = tour.description ? this.truncateText(tour.description, 120) : '';
                    
                    return `
                        <a href="{{ route('tour.detail', '') }}/${tour.slug}" class="modal-search-result-item" data-index="${index}">
                            <img src="${imageUrl}" alt="${this.escapeHtml(tour.name)}" class="modal-search-result-image" loading="lazy">
                            <div class="modal-search-result-content">
                                <div class="modal-search-result-header">
                                    <span class="modal-search-result-category">${this.escapeHtml(categoryName)}</span>
                                    <div class="modal-search-result-title">${this.escapeHtml(tour.name)}</div>
                                </div>
                                ${description ? `<div class="modal-search-result-description">${this.escapeHtml(description)}</div>` : ''}
                            </div>
                        </a>
                    `;
                }).join('');
            }
        }
        
        displayError() {
            this.results.innerHTML = '<div class="search-no-results">Search error occurred. Please try again.</div>';
        }
        
        navigateResults(direction, items) {
            if (items.length === 0) return;
            
            if (this.currentIndex >= 0) {
                items[this.currentIndex].classList.remove('highlighted');
            }
            
            this.currentIndex += direction;
            if (this.currentIndex < 0) this.currentIndex = items.length - 1;
            if (this.currentIndex >= items.length) this.currentIndex = 0;
            
            items[this.currentIndex].classList.add('highlighted');
            items[this.currentIndex].scrollIntoView({ block: 'nearest' });
        }
        
        selectResult(items) {
            if (this.currentIndex >= 0 && items[this.currentIndex]) {
                window.location.href = items[this.currentIndex].href;
            }
        }
        
        showPopularCategories() {
            this.popularSection.style.display = 'block';
            this.resultsSection.style.display = 'none';
        }
        
        showSearchResults() {
            this.popularSection.style.display = 'none';
            this.resultsSection.style.display = 'block';
        }
        
        showSpinner() {
            this.spinner.style.display = 'flex';
        }
        
        hideSpinner() {
            this.spinner.style.display = 'none';
        }
        
        truncateText(text, maxLength) {
            if (!text) return '';
            const stripped = text.replace(/<[^>]*>/g, '');
            if (stripped.length <= maxLength) return stripped;
            return stripped.substr(0, maxLength).trim() + '...';
        }
        
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    }
    
    // Initialize modal search
    new ModalSearch();
    
    // Keep existing search for other pages
    new GlobalSearch({
        toggleBtnId: 'searchToggleBtn',
        closeBtnId: 'searchCloseBtn',
        inputContainerId: 'searchInputContainer',
        inputId: 'globalSearchInput',
        dropdownId: 'searchDropdown',
        resultsId: 'searchResults',
        spinnerId: 'searchSpinner'
    });
</script>
