@extends('frontend.layouts.master')
@push('title', $data['meta']['meta_title'])

@push('seo')
<meta name="description" content="{{ $data['meta']['meta_desc'] }}" />
<meta name="keywords" content="{{ $data['meta']['meta_keyword'] }}" />
<meta name="og:title" content="{{ $data['meta']['meta_title'] }}" />
<meta name="og:description" content="{{ $data['meta']['meta_desc'] }}" />
<meta name="og:url" content="{{ route('tours') }}" />
<meta name="og:type" content="website" />
<meta name="og:site_name" content="{{ config('app.name') }}" />
<link rel="canonical" href="{{ route('tours', $data['category']?->slug) }}" />
@endpush

@push('css')
<script src="{{ asset('assets/js/modernizr.js') }}"></script>
@endpush
@section('content')
<main>

    <section class="hero_in tours jj"
        style="background: url('{{ get_image_full_url($data['category']->image) }}') center center no-repeat; background-size: cover;">
        <div class="wrapper">
            <div class="container">
                <h1 class="fadeInUp">
                    <span></span>{{ $data['category']->category_title ?? 'TOURS LIST : BIRDING TOURS' }}
                </h1>
            </div>
        </div>
    </section>
    <!--/hero_in-->

    <div class="filters_listing  sticky_horizontals">
        <div class="container">
            <ul class="clearfix">
                <li>
                    {{-- <div class="switch-field"> --}}
                    {{-- <input type="radio" id="all" name="listing_filter" value="all"> --}}
                    {{-- <label for="all">All</label> --}}
                    {{-- <input type="radio" id="popular" name="listing_filter" value="popular"> --}}
                    {{-- <label for="popular">Popular</label> --}}
                    {{-- <input type="radio" id="latest" name="listing_filter" value="latest"> --}}
                    {{-- <label for="latest">Latest</label> --}}
                    {{-- </div> --}}
                </li>
                {{-- <li> --}}
                {{-- <div class="layout_view"> --}}
                {{-- <a href="tours-grid-isotope.html"><i class="icon-th"></i></a> --}}
                {{-- <a href="#0" class="active"><i class="icon-th-list"></i></a> --}}
                {{-- </div> --}}
                {{-- </li> --}}
                {{-- <li> --}}
                {{-- <a class="btn_map" data-toggle="collapse" href="#collapseMap" aria-expanded="false" aria-controls="collapseMap" data-text-swap="Hide map" data-text-original="View on map">View on map</a> --}}
                {{-- </li> --}}
            </ul>
        </div>
        <!-- /container -->
    </div>
    <!-- /filters -->

    <div class="collapse" id="collapseMap">
        <div id="map" class="map"></div>
    </div>
    <!-- End Map -->

    <div class="container section-paddingContainer">
        <div class="descriptionFullContainer">
            <div class="description--wrapper">
                 {!! $data['category']->description !!}
            </div>
            <div class="readmore-btnwrapper d-flex align-items-center justify-content-center">
                <button class="toggle--btn">Read more <span class="icon-wrapper">
                    <i class="icon-angle-down"></i>
                </span>
                </button>
            </div>

        </div>

        @include('frontend.pages.partials.call-to-action')
        <div class="packageTour_GridContainer">
        @forelse($data['tours'] as $tour)
            <div class="popularTour_card">
                <div class="img-container">
                    <div class="tour-category">
                        {{ $tour->category->category_title }}
                    </div>
                    <a href="{{ route('tour.detail', $tour->slug) }}" class="img-wrapper">
                        {{-- <img src="{{ get_image_full_url($tour->image) }}" alt="{{ $tour->alt_text ?? $tour->title }}"> --}}
                        {!! render_image($tour->image, [
                            'alt' => $tour->alt_text ?? $tour->title,
                            'loading' => 'lazy',
                            'width' => '264',
                            'height' => '180',
                        ]) !!}
                    </a>
                </div>
                <div class="tour-content">
                    <a href="{{ route('tour.detail', $tour->slug) }}" class="title-wrapper">
                        {{ $tour->name }}
                    </a>
                    <div class="tour-duration">
                                <span class="icon-wrapper">
                                    <iconify-icon icon="mdi:clock-outline"></iconify-icon>
                                </span>
                        {{ $tour->days }} {{ Str::plural('Day', $tour->days) }} & {{ $tour->night }} {{ Str::plural('Night', $tour->night) }}
                    </div>
                    <div class="tour-price">
                        Starts from <span class="bold-text">{{ currency_type(usd_currency()  ? $tour->usd_adult_price : $tour->adult_price) }}</span>
                    </div>
                </div>


            </div>
        @empty
        </div>


        <h4 class="text-center">No Result Found !!!</h4>
        @endforelse



        {{-- <p class="text-center add_top_60"><a href="#0" class="btn_1 rounded">Load more</a></p> --}}

    </div>

    <!-- /container -->
    <div class="bg_color_1 info_containerSection">
        <div class="container section-paddingContainer">
            <div class="row">
                <div class="col-md-4">
                    <a href="{{ route('contact-us') }}" class="boxed_list">
                        <i class="pe-7s-help2"></i>
                        <h4>Need Help? Contact us</h4>
                        {{-- <p>Cum appareat maiestatis interpretaris et, et sit.</p> --}}
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="#0" class="boxed_list">
                        <i class="pe-7s-wallet"></i>
                        <h4>Payments and Refunds</h4>
                        {{-- <p>Qui ea nemore eruditi, magna prima possit eu mei.</p> --}}
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="#0" class="boxed_list">
                        <i class="pe-7s-note2"></i>
                        <h4>Cancel Policy </h4>
                        {{-- <p>Hinc vituperata sed ut, pro laudem nonumes ex.</p> --}}
                    </a>
                </div>
            </div>
            <!-- /row -->
        </div>
        <!-- /container -->
    </div>
    <!-- /bg_color_1 -->
</main>
@endsection
