@extends('frontend.layouts.master')

@push('title', $data['meta']['meta_title'])

@push('seo')
    <meta name="description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="keywords" content="{{ $data['meta']['meta_keyword'] }}" />

    <!-- Open Graph Tags -->
    <meta property="og:title" content="{{ $data['meta']['meta_title'] }}" />
    <meta property="og:description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta property="og:url" content="{{ route('home') }}" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="{{ config('app.name') }}" />
    @php
        $banners = json_decode($data['banners'], true);
        $preloadImages = [];
        $mainImage = '';

        if (count($banners) > 0 && isset($banners[0]['images'][0]['image'])) {
            $mainImage = get_image_full_url($banners[0]['images'][0]['image']);
        }

        foreach ($banners as $banner) {
            foreach ($banner['images'] as $image) {
                $preloadImages[] = get_image_full_url($image['image']);
            }
        }
    @endphp
    <meta property="og:image" content="{{ $mainImage }}" />
    <meta property="og:locale" content="{{ app()->getLocale() }}" />

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{ $data['meta']['meta_title'] }}" />
    <meta name="twitter:description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="twitter:image" content="{{ $mainImage }}" />

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ route('home') }}" />

    <!-- DNS Prefetch and Preconnect -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://www.youtube.com" crossorigin>
    <link rel="dns-prefetch" href="https://www.youtube.com">

    <!-- Image Preloading -->
    @foreach ($preloadImages as $img)
        <link rel="preload" as="image" href="{{ $img }}">
    @endforeach

    <!-- JSON-LD Schema Markup -->
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "Organization",
                    "name": "{{ config('app.name') }}",
                    "url": "{{ route('home') }}",
                    "logo": "{{ get_image_full_url(get_site_config_value('logo')) }}",
                    "sameAs": [
                        "{{ get_site_config_value('facebook_link') }}",
                        "{{ get_site_config_value('twitter_link') }}",
                        "{{ get_site_config_value('instagram_link') }}",
                        "{{ get_site_config_value('youtube_link') }}"
                    ]
                },
                {
                    "@type": "WebSite",
                    "name": "{{ config('app.name') }}",
                    "url": "{{ route('home') }}",
                    "potentialAction": {
                        "@type": "SearchAction",
                        "target": "{{ route('tours') }}?q={search_term_string}",
                        "query-input": "required name=search_term_string"
                    }
                }
            ]
        }
    </script>
@endpush

@push('css')
    <link href="{{ asset('assets/js/modernizr_slider.js') }}" rel="preload" as="script">
    <style>
        /* Critical CSS for above-the-fold content */
        .bannerSwiper {
            visibility: visible;
            opacity: 1;
        }

        .podcast-wrapper {
            margin-top: 30px;
        }

        .podcast-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
        }

        .podcast-info h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }

        .podcast-info p {
            font-size: 1rem;
            color: #666;
        }

        @media (max-width: 767px) {
            .podcast-info h3 {
                font-size: 1.2rem;
            }

            .podcast-info p {
                font-size: 0.9rem;
            }
        }

        .podcast-section {
            background-color: white !important;
            padding: 1px 0;
            margin-bottom: 45px;
        }
    </style>
@endpush

@section('content')
    <main>
        @include('frontend.pages.partials.banner')

        <div class="container container-custom">
            @include('frontend.pages.partials.partner_and_video')

            @include('frontend.pages.partials.popular_tour')
        </div>

        @include('frontend.pages.partials.podcast')

        @include('frontend.pages.partials.cta.adventure')

        @include('frontend.pages.partials.tour_category')

        @include('frontend.pages.partials.plan_your_trip')

        @include('frontend.pages.partials.cta.middle')

        @include('frontend.pages.partials.wtg')

        @include('frontend.pages.partials.call-to-action')


        @include('frontend.pages.partials.blog')

        @include('frontend.pages.partials.cta.footer')
    </main>
@endSection

@push('js')
    <script type="application/ld+json">
        {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "{{ config('app.name') }}",
        "url": "{{ route('home') }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ route('tours') }}?search={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        }
        }
    </script>

    <script type="application/ld+json">
        {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "{{ route('home') }}"
        }]
        }
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script defer src="{{ asset('assets/js/jquery.flexslider.js') }}"></script>
    
    <script>
        // Initialize Plyr
        const players = new Plyr('#player');

        // Add event listeners to show/hide controls
        players.on('play', () => {
            player.elements.container.classList.add('plyr--controls-visible');
        });

        players.on('pause', () => {
            player.elements.container.classList.remove('plyr--controls-visible');
        });

        players.on('ended', () => {
            player.elements.container.classList.remove('plyr--controls-visible');
        });
    </script>

    <script>
        $(window).on('load', function() {
            'use strict';
            $('#carousel_slider').flexslider({
                animation: "slide",
                controlNav: false,
                animationLoop: false,
                slideshow: false,
                itemWidth: 280,
                itemMargin: 25,
                asNavFor: '#slider'
            });
            $('#slider').flexslider({
                animation: "fade",
                controlNav: false,
                animationLoop: false,
                slideshow: false,
                sync: "#carousel_slider",
                start: function(slider) {
                    $('body').removeClass('loading');
                }
            });
        });


        // show more and show less in index page
        document.addEventListener("DOMContentLoaded", function() {
            const toggleButton = document.querySelector(".sm-toggle_btn");
            const textContent = document.querySelector(".sm-text_content");

            // Set initial state
            const maxHeight = 100; // Limit of visible text
            textContent.style.maxHeight = `${maxHeight}px`;

            toggleButton.addEventListener("click", function() {
                if (textContent.style.maxHeight === `${maxHeight}px`) {
                    // Show the full content with smooth transition
                    textContent.style.maxHeight = `${textContent.scrollHeight}px`;
                    toggleButton.innerHTML =
                        'Read less <span class="toggle-icocn_wraper"><iconify-icon icon="uil:angle-up"></iconify-icon></span>';
                } else {
                    // Collapse the content with smooth transition
                    textContent.style.maxHeight = `${maxHeight}px`;
                    toggleButton.innerHTML =
                        'Read more <span class="toggle-icocn_wraper"><iconify-icon icon="uil:angle-down"></iconify-icon></span>';
                }
            });
        });
    </script>

    <script>
        // partners carousel
        $('#partners-carousel_wraper').owlCarousel({
            loop: true,
            margin: 10,
            dot: true,
            nav: false,
            responsiveClass: true,
            responsive: {
                0: {
                    items: 3,
                    nav: false
                },

                650: {
                    items: 4,
                    nav: false
                },
                1020: {
                    items: 5,
                    nav: false,
                    dot: true,
                }
            }
        })

        // gateway carousel
        $('.Gateway-carousel_wraper').owlCarousel({
            items: 1, // Number of items per slide
            loop: true,
            margin: 10,
            nav: false,
            dot: true,
            autoplay: true, // Enable autoplay
            autoplayTimeout: 3000, // Set autoplay interval
            autoplayHoverPause: true, // Pause on hover
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
    
            }
        })
    
        // destination carousel
        $('.destination_sliderContainer').owlCarousel({
            items: 1, // Number of items per slide
            loop: true,
            margin: 10,
            nav: false,
            dot: true,
        })
    </script>
@endpush
