@extends('frontend.layouts.master')

@push('title', $data['meta']['meta_title'])

@push('seo')
    <meta name="description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="keywords" content="{{ $data['meta']['meta_keyword'] }}" />
    <meta name="og:title" content="{{ $data['meta']['meta_title'] }}" />
    <meta name="og:description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="og:url" content="{{ route('home') }}" />
    <meta name="og:type" content="website" />
    <meta name="og:site_name" content="{{ config('app.name') }}" />
    <link rel="canonical" href="{{ route('blogs') }}" />
@endpush

@push('css')
    <!-- SPECIFIC CSS -->
    <link href="{{ asset('assets/css/blog.css') }}" rel="stylesheet">
@endpush
@section('content')
    <main>
        <section class="hero_in general"
            style="background: url('{{ get_image_full_url(get_site_config_value('blog_banner')) }}') center center no-repeat; background-size: cover;">
            <div class="wrapper">
                <div class="container">
                    <h1 class="fadeInUp"><span></span>{{ get_site_config_value('blog_page_title') ?? 'Blogs' }}</h1>
                </div>
            </div>
        </section>
        <!--/hero_in-->

        <div class="container margin_60_35">
            <div class="row">
                <div class="col-lg-9">
                    @forelse($data['blogs'] as $row)
                        <article class="blog wow fadeIn">
                            <div class="row no-gutters">
                                <div class="col-lg-7">
                                    <figure>
                                        <a href="{{ route('blog.detail', $row->slug) }}">
                                            {{-- <img class="blog_image"
                                                src="{{ get_image_full_url($row->image) }}" alt="{{ $row->blog_title }}"> --}}
                                            {!! render_image($row->image, [
                                                'alt' => $row->blog_title,
                                                'loading' => 'lazy',
                                                'width' => '564px',
                                                'height' => '376px',
                                            ]) !!}
                                            <div class="preview"><span>Read more</span></div>
                                        </a>
                                    </figure>
                                </div>
                                <div class="col-lg-5">
                                    <div class="post_info">
                                        <small>{{ format_date($row->created_at) }}</small>
                                        <h3><a href="{{ route('blog.detail', $row->slug) }}">{{ $row->blog_title }}</a>
                                        </h3>
                                        <p>{{ $row->short_desc }}</p>
                                        <ul>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @empty
                    @endforelse



                    {!! $data['blogs']->links() !!}
                    <!-- /pagination -->
                </div>
                <!-- /col -->

                <aside class="col-lg-3">
                    <!-- /widget -->
                    @include('frontend.pages.blog_sidebar')
                </aside>
                <!-- /aside -->
            </div>
            <!-- /row -->
        </div>
        <!-- /container -->
    </main>
@endsection
