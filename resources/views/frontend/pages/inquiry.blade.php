@extends('frontend.layouts.master')

@push('title', $data['meta']['meta_title'])

@push('seo')
    <meta name="description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="keywords" content="{{ $data['meta']['meta_keyword'] }}" />
    <meta name="og:title" content="{{ $data['meta']['meta_title'] }}" />
    <meta name="og:description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="og:url" content="{{ route('inquiry') }}" />
    <meta name="og:type" content="website" />
    <meta name="og:site_name" content="{{ config('app.name') }}" />
    <link rel="canonical" href="{{ route('inquiry') }}" />
@endpush

@push('css')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .inquiry-form-container {
            max-width: 850px;
            margin: 0 auto;
            padding: 50px;
            background-color: #f9f9f9;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8e8e8;
        }

        .inquiry-title {
            text-align: center;
            margin-bottom: 20px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 2.5rem;
        }

        .inquiry-subtitle {
            text-align: center;
            margin-bottom: 50px;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 10px;
            color: #34495e;
            font-size: 0.95rem;
        }

        .required-field::after {
            content: "*";
            color: #e74c3c;
            margin-left: 4px;
        }

        .form-control {
            height: 52px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px 20px;
            background-color: #fff;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 8px rgba(52, 152, 219, 0.2);
            background-color: #fff;
        }

        textarea.form-control {
            min-height: 160px;
            resize: vertical;
        }

        .has-error {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 6px;
            display: block;
        }

        .submit-btn-container {
            text-align: center;
            margin-top: 40px;
        }

        .btn_1.rounded.submit-btn {
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            min-width: 200px;
            border-radius: 50px;
            background-color: var(--primary);
            border: none;
            color: #fff;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .btn_1.rounded.submit-btn:hover {
            background-color: #957600;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.5);
        }

        .section-divider {
            text-align: center;
            margin: 50px 0 40px;
            position: relative;
        }

        .section-divider span {
            background: #f9f9f9;
            padding: 0 20px;
            position: relative;
            z-index: 2;
            color: #34495e;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .section-divider:before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 1px;
            background: #ddd;
            z-index: 1;
        }

        /* jQuery Validation Styles */
        label.error {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 6px;
            display: block;
            font-weight: 500;
        }

        input.error, textarea.error, select.error {
            border-color: #e74c3c !important;
            background-color: #fffafa;
        }

        .form-group.has-success .form-control {
            border-color: #2ecc71;
        }

        .form-group.has-error .form-control {
            border-color: #e74c3c;
        }

        .validation-loading {
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }

        .contact-info-section {
            padding: 80px 0;
            background-color: #f5f7fa;
        }

        .contact-info-title {
            text-align: center;
            margin-bottom: 50px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 2.5rem;
        }

        .contact-info-box {
            text-align: center;
            padding: 40px 30px;
            background: #fff;
            border-radius: 10px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 25px rgba(0,0,0,0.05);
            border: 1px solid #e8e8e8;
        }

        .contact-info-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .contact-info-box i {
            font-size: 48px;
            color: #3498db;
            margin-bottom: 25px;
            display: block;
        }

        .contact-info-box h5 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 10px;
        }

        .contact-info-box p {
            color: #7f8c8d;
            font-size: 1rem;
            line-height: 1.6;
        }

        .contact-info-box a {
            color: #7f8c8d;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info-box a:hover {
            color: #3498db;
        }

        .map-container {
            margin-top: 40px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8e8e8;
        }

        .map-container iframe {
            width: 100%;
            height: 450px;
            border: none;
        }

        /* Custom Select2 Styles */
        .select2-container .select2-selection--single {
            height: 52px !important;
            border-radius: 8px !important;
            border: 1px solid #ddd !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 50px !important;
            padding-left: 20px !important;
            color: #495057;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 50px !important;
            right: 8px !important;
        }

        .select2-dropdown {
            border: 1px solid #ddd !important;
            border-radius: 8px !important;
        }


        /* Custom Success Alert */
        .custom-alert-success {
            padding: 20px;
            background-color: #e8f5e9; /* A lighter, softer green */
            border-left: 5px solid #4CAF50; /* A solid green accent line */
            color: #2e7d32; /* Darker green text for readability */
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            animation: fadeIn 0.5s ease-in-out;
        }

        .custom-alert-success .icon {
            font-size: 28px;
            margin-right: 20px;
            line-height: 1;
        }

        .custom-alert-success .message {
            flex-grow: 1;
            font-weight: 500;
            font-size: 1.05rem;
        }

        .custom-alert-success .close-btn {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            font-size: 28px;
            font-weight: 300;
            color: #2e7d32;
            opacity: 0.6;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0 5px;
            line-height: 1;
            transition: opacity 0.2s ease;
        }

        .custom-alert-success .close-btn:hover {
            opacity: 1;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
@endpush

@section('content')
    <main>
        <section class="hero_in contacts" style="background: url('{{ get_image_full_url(get_site_config_value('contact_banner')) }}') center center no-repeat; background-size: cover;">
            <div class="wrapper">
                <div class="container">
                    <h1 class="fadeInUp"><span></span>Customize Your Journey</h1>
                </div>
            </div>
        </section>
        <!--/hero_in-->

        <div class="bg_color_1">
            <div class="container margin_80_55">
                <div class="inquiry-form-container">
                    @if (session('success'))
                        <div class="custom-alert-success" role="alert">
                            <div class="icon">
                                {{-- Using an Elegant Icon, assuming it's available in your project --}}
                                <i class="icon_check_alt2"></i>
                            </div>
                            <div class="icon" style="font-size: 20px;">
                                {{ session('success') }}
                            </div>
                            <button type="button" class="close-btn" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    <h2 class="inquiry-title">Plan Your Perfect Journey</h2>
                    <p class="inquiry-subtitle">Fill out the form below with your travel preferences and requirements, and our experts will craft the perfect itinerary for you.</p>

                    <form method="Post" action="{{ route('inquiry.post') }}" id="inquiryform" autocomplete="off">
                        @csrf

                        @if ($errors->any())
                            <div class="alert alert-danger" style="margin-bottom: 20px;">
                                <strong>Whoops! Something went wrong.</strong>
                                <p>Please check the form below for errors.</p>
                            </div>
                        @endif

                        <!-- Personal Information -->
                        <div class="section-divider">
                            <span>Personal Information</span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required-field">First Name</label>
                                    <input class="form-control" type="text" id="first_name" name="first_name" value="{{ old('first_name') }}">
                                    @error('first_name')
                                    <label class="has-error" for="first_name">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required-field">Last Name</label>
                                    <input class="form-control" type="text" id="last_name" name="last_name" value="{{ old('last_name') }}">
                                    @error('last_name')
                                    <label class="has-error" for="last_name">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required-field">Email</label>
                                    <input class="form-control" type="email" id="email" name="email" value="{{ old('email') }}">
                                    @error('email')
                                    <label class="has-error" for="email">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required-field">Phone Number</label>
                                    <input class="form-control" type="text" id="phone_no" name="phone_no" value="{{ old('phone_no') }}" data-intl-tel-input>
                                    @error('phone_no')
                                    <label class="has-error" for="phone_no">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Country of Residence</label>
                                    <select class="form-control select2" name="country" required>
                                        <option value="" disabled selected>Select Country of Residence *</option>
                                        @foreach($data['countries'] as $code => $name)
                                            <option value="{{ $name }}" {{ old('country') == $name ? 'selected' : '' }}>{{ $name }}</option>
                                        @endforeach
                                    </select>
                                    @error('country')
                                    <label class="has-error" for="country">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Preferred Language</label>
                                    <select class="form-control" id="preferred_language" name="preferred_language">
                                        <option value="" selected disabled>Select preferred language</option>
                                        <option value="english" {{ old('preferred_language') == 'english' ? 'selected' : '' }}>English</option>
                                        <option value="spanish" {{ old('preferred_language') == 'spanish' ? 'selected' : '' }}>Spanish</option>
                                        <option value="french" {{ old('preferred_language') == 'french' ? 'selected' : '' }}>French</option>
                                        <option value="german" {{ old('preferred_language') == 'german' ? 'selected' : '' }}>German</option>
                                        <option value="italian" {{ old('preferred_language') == 'italian' ? 'selected' : '' }}>Italian</option>
                                        <option value="portuguese" {{ old('preferred_language') == 'portuguese' ? 'selected' : '' }}>Portuguese</option>
                                        <option value="other" {{ old('preferred_language') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('preferred_language')
                                    <label class="has-error" for="preferred_language">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Travel Information -->
                        <div class="section-divider">
                            <span>Travel Details</span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Preferred Travel Date</label>
                                    <input class="form-control" type="date" id="travel_date" name="travel_date" value="{{ old('travel_date') }}">
                                    @error('travel_date')
                                    <label class="has-error" for="travel_date">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Trip Duration (in days)</label>
                                    <input class="form-control" type="number" id="duration" name="duration" min="1" value="{{ old('duration') }}">
                                    @error('duration')
                                    <label class="has-error" for="duration">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required-field">Number of Adults</label>
                                    <input class="form-control" type="number" id="adults" name="adults" min="1" value="{{ old('adults', 1) }}">
                                    @error('adults')
                                    <label class="has-error" for="adults">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Number of Children (if any)</label>
                                    <input class="form-control" type="number" id="children" name="children" min="0" value="{{ old('children', 0) }}">
                                    @error('children')
                                    <label class="has-error" for="children">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Preferred Tour Type</label>
                                    <select class="form-control" id="tour_type" name="tour_type">
                                        <option value="" selected disabled>Select tour type</option>
                                        @foreach($data['categories'] as $cat)
                                            <option value="{{ $cat->category_title }}" {{ old('tour_type') == $cat->category_title ? 'selected' : '' }}>{{ $cat->category_title }}</option>
                                        @endforeach
                                    </select>
                                    @error('tour_type')
                                    <label class="has-error" for="tour_type">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Budget Range (USD)</label>
                                    <select class="form-control" id="budget" name="budget">
                                        <option value="" selected disabled>Select budget range</option>
                                        <option value="budget" {{ old('budget') == 'budget' ? 'selected' : '' }}>Economy ($800-$1500 per person)</option>
                                        <option value="standard" {{ old('budget') == 'standard' ? 'selected' : '' }}>Standard ($1500-$2500 per person)</option>
                                        <option value="luxury" {{ old('budget') == 'luxury' ? 'selected' : '' }}>Luxury ($2500+ per person)</option>
                                    </select>
                                    @error('budget')
                                    <label class="has-error" for="budget">{{ $message }}</label>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="section-divider">
                            <span>Additional Information</span>
                        </div>

                        <div class="form-group">
                            <label class="required-field">Your Message & Special Requirements</label>
                            <textarea class="form-control" id="query" name="query" placeholder="Please include any specific requirements, interests, or questions you may have about your trip...">{{ old('query') }}</textarea>
                            @error('query')
                            <label class="has-error" for="query">{{ $message }}</label>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label>How did you hear about us?</label>
                            <select class="form-control" id="how_did_you_hear" name="source">
                                <option value="" selected disabled>Select an option</option>
                                <option value="google" {{ old('source') == 'google' ? 'selected' : '' }}>Google/Search Engine</option>
                                <option value="social_media" {{ old('source') == 'social_media' ? 'selected' : '' }}>Social Media (Facebook, Instagram, etc.)</option>
                                <option value="friend" {{ old('source') == 'friend' ? 'selected' : '' }}>Friend/Family Recommendation</option>
                                <option value="blog" {{ old('source') == 'blog' ? 'selected' : '' }}>Blog or Travel Website</option>
                                <option value="advertisement" {{ old('source') == 'advertisement' ? 'selected' : '' }}>Online Advertisement</option>
                                <option value="other" {{ old('source') == 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('source')
                            <label class="has-error" for="source">{{ $message }}</label>
                            @enderror
                        </div>

                        @error('recaptcha_token')
                            <p class="text-danger">{{ $message }}</p>
                        @enderror

                        <div class="submit-btn-container">
                            <button type="submit" class="btn_1 rounded submit-btn" id="submit-inquiry">
                                Submit Inquiry
                            </button>
                        </div>
                    </form>
                </div>
                <!-- /inquiry-form-container -->
            </div>
            <!-- /container -->
        </div>
        <!-- /bg_color_1 -->

        <div class="contact-info-section">
            <div class="container">
                <h2 class="contact-info-title">Get In Touch</h2>
                <div class="row">
                    <div class="col-lg-4">
                        <div class="contact-info-box">
                            <i class="icon_pin_alt"></i>
                            <h5>Our Location</h5>
                            <p>{{ get_site_config_value('location') }}</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="contact-info-box">
                            <i class="icon_mobile"></i>
                            <h5>Call Us</h5>
                            <p><a href="tel:{{ get_site_config_value('phone') }}">{{ get_site_config_value('phone') }}</a></p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="contact-info-box">
                            <i class="icon_mail_alt"></i>
                            <h5>Email Us</h5>
                            <p><a href="mailto:{{ get_site_config_value('email') }}">{{ get_site_config_value('email') }}</a></p>
                        </div>
                    </div>
                </div>

                <div class="map-container">
                    <iframe src="{{ get_site_config_value('contact_page_map') }}" allowfullscreen="" loading="lazy"></iframe>
                </div>
            </div>
        </div>
    </main>
@endsection

@include('common.int-tel-input')

@push('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Add jQuery validation plugin -->
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>

    <!-- Add reCAPTCHA script -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    <script src="{{ asset('assets/js/recaptcha-validation.js') }}"></script>
    <script>
        $(document).ready(function () {
            // Initialize reCAPTCHA
            recaptchaHandler.init('{{ config('services.recaptcha.site_key') }}');

            // Logic to close the custom alert
            $('.custom-alert-success .close-btn').on('click', function() {
                $(this).closest('.custom-alert-success').fadeOut(300);
            });

            // Initialize Select2
            $('.select2').select2({
                placeholder: "Select Country of Residence *",
                allowClear: true
            });

            {!! toaster() !!}

            // Initialize jQuery Validation
            $("#inquiryform").validate({
                // Validation rules
                rules: {
                    first_name: {
                        required: true,
                        minlength: 2,
                        maxlength: 50
                    },
                    last_name: {
                        required: true,
                        minlength: 2,
                        maxlength: 50
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone_no: {
                        required: true,
                        intlTelNumber: true
                    },
                    country: {
                        maxlength: 50
                    },
                    travel_date: {
                        date: true
                    },
                    duration: {
                        min: 1,
                        max: 365,
                        digits: true
                    },
                    adults: {
                        required: true,
                        min: 1,
                        max: 100,
                        digits: true
                    },
                    children: {
                        min: 0,
                        max: 100,
                        digits: true
                    },
                    query: {
                        required: true,
                        minlength: 10,
                        maxlength: 2000
                    },
                },

                // Custom error messages
                messages: {
                    first_name: {
                        required: "Please enter your first name",
                        minlength: "First name must be at least 2 characters",
                        maxlength: "First name must be less than 50 characters"
                    },
                    last_name: {
                        required: "Please enter your last name",
                        minlength: "Last name must be at least 2 characters",
                        maxlength: "Last name must be less than 50 characters"
                    },
                    email: {
                        required: "Please enter your email address",
                        email: "Please enter a valid email address"
                    },
                    phone_no: {
                        required: "Please enter your phone number",
                        minlength: "Phone number must be at least 7 digits",
                        maxlength: "Phone number must be less than 15 digits",
                        digits: "Please enter only numbers"
                    },
                    duration: {
                        min: "Trip duration must be at least 1 day",
                        max: "Trip duration must be less than 365 days",
                        digits: "Please enter only numbers"
                    },
                    adults: {
                        required: "Please enter number of adults",
                        min: "At least 1 adult is required",
                        max: "Maximum 100 adults allowed",
                        digits: "Please enter only numbers"
                    },
                    children: {
                        min: "Minimum 0 children",
                        max: "Maximum 100 children allowed",
                        digits: "Please enter only numbers"
                    },
                    query: {
                        required: "Please specify your travel requirements",
                        minlength: "Please provide at least 10 characters",
                        maxlength: "Message is too long (maximum 2000 characters)"
                    },
                },

                // Highlight function
                highlight: function(element, errorClass, validClass) {
                    $(element).addClass(errorClass).removeClass(validClass);
                    $(element).closest('.form-group').addClass('has-error').removeClass('has-success');
                },

                // Unhighlight function
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass(errorClass).addClass(validClass);
                    $(element).closest('.form-group').removeClass('has-error').addClass('has-success');
                },

                // Error placement
                errorPlacement: function(error, element) {
                    if (element.attr('name') === 'phone_no') {
                        element.closest(".form-group").append(error);
                    } else {
                        error.insertAfter(element);
                    }
                },

                // Submit handler
                submitHandler: function(form) {
                    // Show loading indicator or perform additional actions before form submission
                    $('#submit-inquiry').val('Submitting...');
                    $('#submit-inquiry').prop('disabled', true);

                    recaptchaHandler.getToken()
                        .then(function(token) {
                            recaptchaHandler.injectToken(form, token);
                            form.submit();
                        })
                        .catch(function(err) {
                            alert("reCAPTCHA failed: " + err);
                        });
                },

                // Invalid form
                invalidHandler: function(event, validator) {
                    const errors = validator.numberOfInvalids();
                    if (errors) {
                        // Scroll to the first error
                        const firstErrorElement = $(validator.errorList[0].element);
                        $('html, body').animate({
                            scrollTop: firstErrorElement.offset().top - 100
                        }, 500);
                    }
                }
            });

            // Additional custom validations
            $.validator.addMethod("futureDate", function(value, element) {
                if (!value) return true; // Allow empty
                const today = new Date();
                const selectedDate = new Date(value);
                return selectedDate >= today;
            }, "Please select a future date");

            // Apply future date validation to travel_date
            $("#travel_date").rules("add", {
                futureDate: true
            });
        });
    </script>
@endpush
