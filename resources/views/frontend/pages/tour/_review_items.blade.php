@foreach ($reviews as $review)
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div class="d-flex align-items-center">
                    <div class="mr-3">
                        <img src="{{ get_image_url('users', $review->user->image) }}"
                            alt="{{ $review->user->full_name }}" class="rounded-circle" width="50" height="50">
                    </div>
                    <div>
                        <h6 class="mb-1 font-weight-bold">{{ $review->user->full_name ?? 'Anonymous' }}
                        </h6>
                        <div class="star-rating mb-1">
                            <div>
                                @for ($i = 1; $i <= 5; $i++)
                                    @if ($i <= $review->rating)
                                        <iconify-icon icon="mingcute:star-fill" class="text-warning"></iconify-icon>
                                    @else
                                        <iconify-icon icon="mingcute:star-line" class="text-warning"></iconify-icon>
                                    @endif
                                @endfor
                            </div>
                            <span class="ml-2 text-muted small">{{ $review->created_at->format('d M Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <p class="mb-0">{{ $review->comment }}</p>
            </div>
        </div>
    </div>
@endforeach
