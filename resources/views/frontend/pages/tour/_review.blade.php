<section class="tour__faqsection itinerarydetailsection" id="tour_reviewTab">
    <div class="faq-titlewrapper d-flex justify-content-between align-items-center">
        <h2 class="tour_faq--title mb-0">
            Reviews & Ratings
        </h2>
        @auth
            @if (!$row->hasUserReviewed())
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addReviewModal">
                    <i class="fa fa-plus"></i> Write a Review
                </button>
            @endif
        @endauth
    </div>

    <!-- Add Review Modal -->
    @auth
        @if (!$row->hasUserReviewed())
            <div class="modal fade" id="addReviewModal" tabindex="-1" role="dialog" aria-labelledby="addReviewModalLabel"
                aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addReviewModalLabel">Write a Review</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form action="{{ route('customer.reviews.store', $row->id) }}" method="POST">
                            @csrf
                            <div class="modal-body">
                                <input type="hidden" name="tour_id" value="{{ $row->id }}">

                                <div class="form-group mb-4">
                                    <label for="rating">Rating</label>
                                    <select class="form-control @error('rating') is-invalid @enderror" id="rating"
                                        name="rating" required>
                                        @for ($i = 5; $i >= 1; $i--)
                                            <option value="{{ $i }}" {{ old('rating') == $i ? 'selected' : '' }}>
                                                {{ $i }} -
                                                {{ $i === 1 ? 'Poor' : ($i === 2 ? 'Fair' : ($i === 3 ? 'Good' : ($i === 4 ? 'Very Good' : 'Excellent'))) }}
                                            </option>
                                        @endfor
                                    </select>
                                    @error('rating')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-group">
                                    <label for="comment">Your Review</label>
                                    <textarea class="form-control @error('comment') is-invalid @enderror" id="comment" name="comment" rows="4"
                                        required>{{ old('comment') }}</textarea>
                                    @error('comment')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                <button type="submit" class="btn btn-primary">Submit Review</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endif
    @endauth

    @if (count($row?->publishedReviews))
        <!-- Overall Rating Summary -->
        <div class="review-summary mb-5">
            <div class="row">
                <div class="col-md-4 text-center">
                    <h1 class="display-4 text-primary mb-0">{{ number_format($row->published_reviews_avg_rating, 1) }}
                    </h1>
                    <div class="star-rating mb-2">
                        @for ($i = 1; $i <= 5; $i++)
                            <i
                                class="fa fa-star{{ $i <= $row->published_reviews_avg_rating ? ' text-warning' : '-o' }}"></i>
                        @endfor
                    </div>
                    <p class="text-muted">Based on {{ $row->published_reviews_count }} reviews</p>
                </div>
                <div class="col-md-8">
                    @for ($i = 5; $i >= 1; $i--)
                        @php
                            $percentage = 0;
                            if ($row->published_reviews_count > 0) {
                                $percentage =
                                    ($row->publishedReviews->where('rating', $i)->count() /
                                        $row->published_reviews_count) *
                                    100;
                            }
                        @endphp
                        <div class="rating-row d-flex align-items-center mb-2">
                            <span class="mr-2">{{ $i }} <i class="fa fa-star text-warning"></i></span>
                            <div class="progress flex-grow-1" style="height: 8px;">
                                <div class="progress-bar bg-warning" role="progressbar"
                                    style="width: {{ $percentage }}%" aria-valuenow="{{ $percentage }}"
                                    aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span class="ml-2 text-muted">{{ round($percentage) }}%</span>
                        </div>
                    @endfor
                </div>
            </div>
        </div>

        <div class="reviews-list" id="reviewsList">
            @include('frontend.pages.tour._review_items', ['reviews' => $reviews])
        </div>

        <!-- Load More Button -->
        @if ($reviews->hasMorePages())
            <div class="mt-3 d-flex justify-content-center" id="loadMoreContainer">
                <button type="button" class="btn btn-primary" id="loadMoreBtn" data-tour-id="{{ $row->id }}">
                    <span class="btn-text">Load More</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                </button>
            </div>
        @endif
    @else
        <div class="alert alert-info">
            No reviews yet. Be the first to review this tour!
        </div>
    @endif
</section>

@push('js')
    <script>
        $(document).ready(function() {
            const $loadMoreContainer = $('#loadMoreContainer');
            const $button = $('#loadMoreBtn');
            const $spinner = $button.find('.spinner-border');
            const $btnText = $button.find('.btn-text');
            const $reviewsList = $('#reviewsList');

            if (!$button.length || !$loadMoreContainer.length || !$reviewsList.length) return;

            let currentPage = 1;
            const tourId = $button.data('tour-id');
            const loadMoreUrl = "{{ route('tour.load-more-reviews', ['tour_id' => '__ID__']) }}";

            $button.on('click', function() {
                currentPage++;

                $btnText.text('Loading...');
                $spinner.removeClass('d-none');
                $button.prop('disabled', true);

                let fetchUrl = loadMoreUrl.replace('__ID__', tourId) + '?page=' + currentPage;

                $.ajax({
                    url: fetchUrl,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if (data.success) {
                            // Only append if there's actual content
                            if (data.html && data.html.trim() !== '') {
                                $reviewsList.append(data.html);
                            }

                        if (!data.hasMore) {
                                $loadMoreContainer.remove();
                            }
                        } else {
                            console.error('Unexpected response format');
                            alert('Something went wrong while loading more reviews.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load More Error:', error);

                        $loadMoreContainer.show();
                    },
                    complete: function() {
                        $btnText.text('Load More');
                        $spinner.addClass('d-none');
                        $button.prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush
