<div class="sticky-wrapper">
    <div class="tripdetail__side--card">
        <div class="tourprice__badge">
            Most popular
        </div>
        <div class="country-select">
        </div>
        <div class="trip__price">
            <div class="trip-price__pax">
                Price Per person
            </div>
            <div class="trip-price__amount">{{ price_with_currency($row) }}
            </div>
        </div>
        <div class="review-wrapper">
            <div class="review-icons">
                <span class="icon-wrapper">
                    <iconify-icon icon="ic:round-star"></iconify-icon>
                </span>
                <span class="icon-wrapper">
                    <iconify-icon icon="ic:round-star"></iconify-icon>
                </span>
                <span class="icon-wrapper">
                    <iconify-icon icon="ic:round-star"></iconify-icon>
                </span>
                <span class="icon-wrapper">
                    <iconify-icon icon="ic:round-star"></iconify-icon>
                </span>
                <span class="icon-wrapper">
                    <iconify-icon icon="ic:round-star"></iconify-icon>
                </span>
            </div>
        </div>
        @php
            $dynamicContent = json_decode(get_site_config_value('tour_detail') ?? null);
        @endphp

        @if ($dynamicContent?->promise_content)
            <ul class="trip_price--info">
                @foreach ($dynamicContent->promise_content as $key => $item)
                    @if ($item)
                        <li>
                            <span class="icon-wrapper">
                                @if ($key === 0)
                                    <iconify-icon icon="basil:award-outline"></iconify-icon>
                                @elseif($key === 1)
                                    <iconify-icon icon="mingcute:location-2-line"></iconify-icon>
                                @elseif($key === 2)
                                    <iconify-icon icon="ic:sharp-whatsapp"></iconify-icon>
                                @endif

                            </span>
                            {{ $item }}
                        </li>
                    @endif
                @endforeach
            </ul>
        @endif

        <div class="btn-wrapper">
            <button data-target="#choosedateoffcanvas" class="choose--btn btn--lg btn--secondary ">
                Choose Your Date
            </button>
            <button class="askquestion-btn btn--lg btn--primary " data-target="#askquestionoffcanvas">
                Ask a Question
            </button>
        </div>

        @if (get_site_config_value('whatsapp_number'))
            <div class="instant-contact">
                <div class="icon-wrapper">
                    <iconify-icon icon="logos:whatsapp-icon"></iconify-icon>
                </div>
                <div class="content-wrapper">
                    <span class="text-content">Get Instant Response:</span>
                    <br>
                    <a href="https://wa.me/{{ get_site_config_value('whatsapp_number') }}" target="_blank"
                        class="contactlink">{{ get_site_config_value('whatsapp_number') }} (WhatsApp)</a>
                </div>
            </div>
        @endif
    </div>

    <div class="tripdetail__side--card mt-3 cta-card">
        <div class="cta-content">
            <h4 class="cta-title">Ready to Explore Bhutan?</h4>
            <p class="cta-text">
                Start your journey today and discover the magic of Bhutan with our expert guides and carefully crafted
                tours.
            </p>
        </div>
        <div class="btn-wrapper">
            <a href="{{ route('inquiry') }}" class="cta-btn btn--lg btn--primary">Inquire Now</a>
        </div>
    </div>
</div>

<!-- Offcanvas - Choose Date -->
<div id="choosedateoffcanvas" class="tripitinerary-offcanvas offcanvas">
    <div class="datepaneloffcanvas">
        <div class="btn-wrapper">
            <button class=" close-offcanvas" data-target="#choosedateoffcanvas">
                <iconify-icon icon="gridicons:cross"></iconify-icon>
            </button>
        </div>
        <div class="heading-wrapper">
            <h3 class="title-wrapper">
                Book This Trip
            </h3>
        </div>

        <form method="POST" action="{{ route('tour.booking') }}" id="booking-form">
            @csrf
            <div class="box_details booking">
                <input type="hidden" name="tour_id" value="{{ $row->id }}">
                <div class="form-group" id="input_date">
                    <label for="" class="booking-trip__form-label">
                        When?
                        <span class="booking-trip__small-text">Pick a Date (Private Date)</span>
                    </label>
                    <input class="form-control" type="text" required name="dates_calendar" placeholder="When.."
                        readonly>
                    <input type="hidden" name="dates" id="dates_hidden">
                    <i class="icon_calendar"></i>
                </div>
                <div class="form-group">
                    <label for="traveller_count" class="booking-trip__form-label">
                        Travellers?
                        <span class="booking-trip__small-text">Number of pax</span>
                    </label>
                    <div class="panel-dropdown">
                        <a href="#">Guests <span class="qtyTotal">1</span></a>
                        <div class="panel-dropdown-content right">
                            <div class="qtyButtons">
                                <label>Adults</label>
                                <input type="text" name="adults" class="qtyInput" value="1">
                            </div>
                            <div class="qtyButtons">
                                <label>Children</label>
                                <input type="text" name="children" class="qtyInput" value="0">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="total-cost-display"
                    style="margin-top: 20px; margin-bottom: 20px; font-size: 18px; font-weight: 600; display: flex; justify-content: space-between;">
                    <span>Total Cost:</span>
                    <span class="total-price-amount">{{ price_with_currency($row) }}</span>
                </div>
                <button type="submit" class=" add_top_30 btn_1 full-width purchase planedates-booknow__btn">
                    Book now
                </button>
            </div>
        </form>

        @if ($dynamicContent?->book_this_trip)
            <ul class="booking-trip__features">
                @foreach ($dynamicContent->book_this_trip as $trip)
                    @if ($trip)
                        <li class="booking-trip__listitems">
                            <span class="booking-trip__icon">
                                <iconify-icon icon="charm:circle-tick"></iconify-icon>
                            </span>
                            {{ $trip }}
                        </li>
                    @endif
                @endforeach
            </ul>
        @endif
    </div>
</div>

<!-- Offcanvas - Ask Question -->
<div id="askquestionoffcanvas" class="tripitinerary-offcanvas offcanvas">
    <div class="inquiryFormWrapper">
        <div class="btn-wrapper">
            <button class=" close-offcanvas" data-target="#askquestionoffcanvas">
                <iconify-icon icon="gridicons:cross"></iconify-icon>
            </button>
        </div>
        <div class="heading-wrapper">
            <h3 class="title-wrapper">
                Ask a Question
            </h3>
            <p class="sub-title">
                {{ $dynamicContent->ask_question->description ?? '' }}
            </p>
        </div>
        <form action="{{ route('ask-question') }}" method="post" id="askQuestionForm">
            @csrf
            <div class="form-wrapper">
                <input type="hidden" name="tour_id" value="{{ $row->id }}">
                <div class="form-group">
                    <label for="name">Name<span class="error">*</span></label>
                    <input type="text" class="form-control" name="name" id="name"
                        placeholder="Enter your name" value="{{ old('name') }}">
                    @error('name')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>
                <div class="form-group">
                    <label>Email<span class="error">*</span></label>
                    <input type="email" class="form-control" name="email" id="inquiry_email"
                        placeholder="Enter your email" value="{{ old('email') }}">
                    @error('email')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number<span class="error">*</span></label>
                    <input type="tel" class="form-control" name="phone" id="phone"
                        placeholder="Enter your phone number" value="{{ old('phone') }}" data-intl-tel-input>
                    @error('phone')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="message">Message<span class="error">*</span></label>
                    <textarea class="form-control" name="message" id="message" rows="6" placeholder="Enter your message">{{ old('message') }}</textarea>
                    @error('message')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>

                @error('recaptcha_token')
                    <p class="text-danger">{{ $message }}</p>
                @enderror

                <div class="text-center">
                    <button type="submit" value="Submit" class="btn_1 full-width">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!--  Overlay -->
<div id="offcanvasOverlay" class="offcanvas-overlay itineraryoverlay"></div>

@push('css')
    <link href="{{ asset('dist/css/select2.min.css') }}" rel="stylesheet">
@endpush

@include('common.int-tel-input')

@push('js')
    <script src="{{ asset('dist/js/select2.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('assets/js/input_qty.js') }}"></script>

    <!-- Add reCAPTCHA script -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    <script src="{{ asset('assets/js/recaptcha-validation.js') }}"></script>

    <script>
        $('#currency-select').select2();

        $(document).ready(function() {
            const $overlay = $("#offcanvasOverlay");
            const $header = $(".header.main-header");

            @if (session('success'))
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: "{{ session('success') }}",
                    confirmButtonText: 'OK',
                });
            @endif

            @if (session('error'))
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: "{{ session('error') }}",
                    confirmButtonText: 'OK',
                });
            @endif

            // Open specific offcanvas and show overlay
            $(".choose--btn, .askquestion-btn").on("click", function() {
                const target = $(this).data("target");
                $("body").addClass("offcanvas-open");
                $(target).addClass("show");
                $overlay.addClass("show");
                $header.addClass("overlay-hidden");
            });

            // Close specific offcanvas and hide overlay
            $(".close-offcanvas").on("click", function() {
                const target = $(this).data("target");
                $("body").removeClass("offcanvas-open");
                $(target).removeClass("show");
                $overlay.removeClass("show");
                $header.removeClass("overlay-hidden");
            });

            // Hide overlay and close offcanvas when clicking the overlay
            $overlay.on("click", function() {
                $(".tripitinerary-offcanvas.show").removeClass("show");
                $overlay.removeClass("show");
                $("body").removeClass("offcanvas-open");
                $header.removeClass("overlay-hidden");
            });
        });

        // Initialize reCAPTCHA
        recaptchaHandler.init('{{ config('services.recaptcha.site_key') }}');

        $(document).ready(function() {
            $("#askQuestionForm").validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 3
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone: {
                        required: true,
                        intlTelNumber: true
                    },
                    message: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    name: {
                        required: "Please enter your name",
                        minlength: "Name must be at least 3 characters long"
                    },
                    email: {
                        required: "Please enter your email",
                        email: "Please enter a valid email address"
                    },
                    phone: {
                        required: "Please enter your phone number",
                        digits: "Phone number must contain only digits",
                        minlength: "Phone number must be at least 10 digits long",
                        maxlength: "Phone number cannot exceed 15 digits"
                    },
                    message: {
                        required: "Please enter your message",
                        minlength: "Message must be at least 10 characters long"
                    }
                },
                errorElement: "div",
                errorPlacement: function(error, element) {
                    error.addClass("invalid-feedback");
                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.siblings("label"));
                    } else if (element.attr('name') === 'phone') {
                        element.closest(".form-group").append(error);
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function(element) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function(form) {
                    recaptchaHandler.getToken()
                        .then(function(token) {
                            recaptchaHandler.injectToken(form, token);
                            form.submit();
                        })
                        .catch(function(err) {
                            alert("reCAPTCHA failed: " + err);
                        });
                },
            });
        });
    </script>
@endpush
