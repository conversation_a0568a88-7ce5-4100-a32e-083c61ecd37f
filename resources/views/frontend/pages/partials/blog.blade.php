<div class="bg_color_1 section-paddingContainer">
    <div class="container container-custom ">
        <div class="main_title_2">
            <span><em></em></span>
            <h2 id="blog-section">Latest Travel Blogs</h2>
            <p>Explore the Enchanting Beauty of Bhutan: Top Must-Visit Destinations!</p>
        </div>
        <div class="row">
            @foreach($data['blogs'] as $blog)
                <script type="application/ld+json">
                {
                  "@context": "https://schema.org",
                  "@type": "BlogPosting",
                  "headline": "{{ $blog->blog_title }}",
                  "image": "{{ get_resize_image($blog->image, 400, 267) }}",
                  "datePublished": "{{ $blog->created_at->toIso8601String() }}",
                  "dateModified": "{{ $blog->updated_at->toIso8601String() }}",
                  "author": {
                    "@type": "Organization",
                    "name": "{{ config('app.name') }}"
                  },
                  "publisher": {
                    "@type": "Organization",
                    "name": "{{ config('app.name') }}",
                    "logo": {
                      "@type": "ImageObject",
                      "url": "{{ asset('/images/stamp-logo.png') }}"
                    }
                  },
                  "description": "{{ str_limit($blog->short_desc, 150) }}",
                  "url": "{{ route('blog.detail', $blog->slug) }}"
                }
                </script>
                <div class="col-md-6 col-6">
                    <a class="box_news" href="{{ route('blog.detail', $blog->slug) }}">
                        <figure>
                            <img loading="lazy" src="{{ get_resize_image($blog->image, 400, 267) }}"
                                alt="{{ $blog->alt_text ?? 'Blog article: ' . $blog->blog_title }}" width="400"
                                height="267">
                            <figcaption>
                                <strong>{{ format_date($blog->created_at, 'j') }}</strong>{{ format_date($blog->created_at, 'M') }}
                            </figcaption>
                        </figure>
                         {{-- {!! render_image($blog->image, [
                            'alt' => $blog->alt_text ?? $blog->blog_title,
                            'loading' => 'lazy',
                            'width' => '400px',
                            'height' => '267px',
                        ]) !!} --}}
                        <ul>
                            <li>{{ format_date($blog->created_at) }}</li>
                        </ul>
                        <h4 class="blog_title">{{ $blog->blog_title }}</h4>
                        <p class="blog-description">{{ str_limit($blog->short_desc, 170) }}</p>
                    </a>
                </div>
            @endforeach
            <!-- /box_news -->
        </div>
        <!-- /row -->
        <p class="btn_home_align"><a href="{{ route('blogs') }}" class="btn_1 rounded">View all Blogs</a></p>
    </div>
    <!-- /container -->
</div>
