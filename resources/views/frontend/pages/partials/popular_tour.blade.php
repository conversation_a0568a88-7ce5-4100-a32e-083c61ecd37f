<section class="add_bottom_45 popular_tours--section">
    <div class="main_title_3">
        <span><em></em></span>
        <h2 id="popular-tours">{{ get_site_config_value('home_popular_tours_section_title') }} </h2>
        <p>{{ get_site_config_value('home_popular_tours_section_subtitle') }}</p>
    </div>
    <div class="swiper popularTourSlider">
        <div class="swiper-wrapper">
            @foreach ($data['popularTours'] as $popular)
                <div class="swiper-slide">
                    <div class="popularTour_card">
                        <div class="img-container">
                            <div class="tour-category">
                                {{ $popular->tour->category->category_title }}
                            </div>
                            <a href="{{ route('tour.detail', $popular->tour->slug) }}" class="img-wrapper">
                                {{-- <img loading="lazy"
                                    alt="{{ $popular?->tour?->alt_text ?? 'Tour to ' . $popular?->tour?->name . ' - ' . $popular->tour->days . ' days & ' . $popular->tour->night . ' nights' }}"
                                    src="{{ get_image_full_url($popular?->tour?->image) }}" width="400"
                                    height="267"> --}}
                                {!! render_image($popular->tour->image, [
                                    'alt' => $popular?->tour?->alt_text ?? 'Tour to ' . $popular?->tour?->name . ' - ' . $popular->tour->days . ' days & ' . $popular->tour->night . ' nights',
                                    'loading' => 'lazy',
                                    'width' => '400px',
                                    'height' => '267px',
                                    'class' => 'img-fluid',
                                ]) !!}
                            </a>
                        </div>
                        <div class="tour-content">
                            <a href="{{ route('tour.detail', $popular->tour->slug) }}" class="title-wrapper">
                                {{ $popular->tour->name }}
                            </a>
                            <div class="tour-duration">
                                <span class="icon-wrapper">
                                    <iconify-icon icon="mdi:clock-outline"></iconify-icon>
                                </span>
                                {{ $popular->tour->days }} {{ Str::plural('Day', $popular->tour->days) }} &
                                {{ $popular->tour->night }} {{ Str::plural('Night', $popular->tour->night) }}
                            </div>
                            <div class="tour-price">
                                Starts from <span
                                    class="bold-text">{{ currency_type(usd_currency() ? $popular->tour->usd_adult_price : $popular->tour->adult_price) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    <div class="navigation-btns--wrapper">
        <a href="{{ route('popular-tours') }}" class="view_all--btn"> View all tours <span class="icon"><iconify-icon
                    icon="fe:arrow-right"></iconify-icon></span></a>
        <div class="navigationBtns">
            <div class="swiper-button-prevs">
                <iconify-icon icon="fe:arrow-left"></iconify-icon>
            </div>
            <div class="swiper-button-nexts">
                <iconify-icon icon="fe:arrow-right"></iconify-icon>
            </div>
        </div>
    </div>
</section>
