<div class="container container-custom margin_30_95">
    <div class="categories-section">
        <div class="header-section">
            <span class="categories-line">
                <em></em>
            </span>
            <h3 class="title">Ways to Go</h3>
            <p class="description">Discover Hidden Gems: Unforgettable Tours for Every Adventurer!</p>
        </div>
        <div class="destination_GridContainer sm-destination_GridContainer">

            @foreach($data['tourCategory'] as $category)
            <a href="{{ route('tours', [$category->slug]) }}" class="destination_GridItem">
                <h5 class="destination_title">{{ $category->category_title }}</h5>
                {{-- <img loading="lazy" src="{{ get_image_full_url($category->image) }}" alt="{{  $category->alt_text ?? $category->category_title }}"> --}}
                {!! render_image($category->image, [
                    'alt' => $category->alt_text ?? $category->category_title,
                    'loading' => 'lazy',
                    'width' => '363px',
                    'height' => '167px',
                    'class' => 'img-fluid',
                ]) !!}
            </a>
            @endforeach
        </div>
        {{-- <div class="categories-slider__wraper">
            <div class="owl-carousel destination_sliderContainer">
                <div>
                    <div class="categories-slider">
                        @foreach($data['tourCategory'] as $category)
                        <a href="{{ route('tours', [$category->slug]) }}" class="categories-slider_item">
                            <h5 class="categories-slider_title">{{ $category->category_title }}</h5>
                            <img loading="lazy" class="categories-slider_image" src="{{ get_image_full_url($category->image) }}" alt="slider image">
                        </a>

                        @if($loop->iteration % 4 == 0)
                        </div>
                        </div>
                        <div>
                        <div class="categories-slider">
                        @endif
                        @endforeach
                    </div>
                </div>
            </div>
        </div> --}}
    </div>
</div>