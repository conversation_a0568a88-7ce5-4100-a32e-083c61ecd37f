<div class="bannerSwiper">
    <div class="bannerCard">
        <div class="container container-custom">
            <div class="row justify-content-between align-items-center">
                <div class="col-md-6 col-lg-6">
                    <div class="bannerCard__leftcontent">
                        <div class="navigation-btns--wrapper">
                            <div class="button-prev">
                                <iconify-icon icon="flowbite:angle-left-outline"></iconify-icon>
                            </div>
                            <div id="banner-type">
                            </div>
                            <div class="button-next">
                                <iconify-icon icon="flowbite:angle-right-outline"></iconify-icon>
                            </div>
                        </div>
                        <div class="title-wrapper">
                            <h1 class="bannerCard__leftcontent--title">
                            </h1>
                            <div class="bannerCard__leftcontent--contentwrapper">
                                <div class="subtitle">
                                </div>
                                <div class="arrow-icon">
                                    <img src="{{ asset('assets\img\arrowicon.svg') }}" alt="Direction arrow icon"
                                        width="24" height="24">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-6">
                    <div class="rightBannerMainContainer">
                        <div class="bannerCard__rightcontent">
                        </div>
                        <div class="dotimage">
                            <img src="{{ asset('assets\img\dot.svg') }}" alt="Decorative dot pattern" width="60"
                                height="60">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('js')
    <script>
        var banners = JSON.parse('{!! $data['banners'] !!}');
        var current = 0;

        function setBanner(i) {
            let banner = banners[i]

            $('#banner-type').html(banner.type);
            $('.bannerCard__leftcontent--title').html(
                `${banner.title_plain} ${banner.title_colored ? `<mark class="highlight-text">${banner.title_colored}</mark>` : ''}`
                );
            $('.subtitle').html(banner.sub_title);
            $('.bannerCard__rightcontent').html(`
        <div class="imageGridContainer theme${banner.layout}">
            ${banner.images.map(function(image) {
                return image.link ? `<a href="${image.link}" class="imageGridItem">
                        <img loading="lazy" src="{{ get_image_full_url('') }}${ image.image }" alt="">
                    </a>` : `<span class="imageGridItem">
                        <img loading="lazy" src="{{ get_image_full_url('') }}${ image.image }" alt="">
                    </span>`;
            })}
        </div>
        `)
        }

        $(function() {
            setBanner(current);

            $('.button-prev').click(function() {
                if (current > 0) {
                    current--;
                } else {
                    current = banners.length - 1;
                }

                setBanner(current);
            });

            $('.button-next').click(function() {
                if (current < banners.length - 1) {
                    current++;
                } else {
                    current = 0;
                }

                setBanner(current);
            });
        })
    </script>
@endpush
