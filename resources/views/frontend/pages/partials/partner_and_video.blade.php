<section class="add_bottom_45 health-info_wraper">

    @if ($data['partners']->isNotEmpty())
        <div class="carousel_wraper">
            <div class="content-box">
                <h2 class="title">Our Partners</h2>
            </div>
            <div class="owl-carousel owl-theme scroll-row" id="partners-carousel_wraper">
                @foreach ($data['partners'] as $partner)
                    <div class="item">
                        @if ($partner->link)
                            <a href="{{ $partner->link }}" class="logo-container" target="_blank">
                                {{-- <img loading="lazy" src="{{ get_image_full_url($partner->image) }}"
                                    alt="{{ $partner->alt_text ?? $partner->name }}" title="{{ $partner->name }}"
                                    class="img-fluid"> --}}
                                {!! render_image($partner->image, [
                                    'alt' => $partner->alt_text ?? $partner->name,
                                    'loading' => 'lazy',
                                    'width' => '100px',
                                    'height' => '100px',
                                    'class' => 'img-fluid',
                                ]) !!}
                            </a>
                        @else
                            <span class="logo-container">
                                {!! render_image($partner->image, [
                                    'alt' => $partner->alt_text ?? $partner->name,
                                    'loading' => 'lazy',
                                    'width' => '100px',
                                    'height' => '100px',
                                    'class' => 'img-fluid',
                                ]) !!}
                            </span>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="row row-container">
        <div class="col-lg-6 d-flex flex-1">
            <div class="video-wraper">
                <div class="plyr__video-embed " id="player">
                    <iframe src="https://www.youtube.com/embed/{{ $data['video']['video_code'] }}"
                        title="{{ $data['video']['video_title'] ?? 'Travel experience video' }}" loading="lazy"
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>
            </div>
        </div>
        <div class="col-lg-6 d-flex flex-1 ">
            <div class="info-box">
                <h2 class="title">{{ $data['video']['video_title'] }}</h2>
                <p class="desc-content">{{ $data['video']['video_desc'] }}</p>
            </div>
            <div class="sm-info_box">
                <h2 class="sm-info_title mb-16">{{ $data['video']['video_title'] }}</h2>

                <div class="sm-description__wrapper">
                    <p class="sm-text_content">
                        {!! $data['video']['video_desc'] !!}
                    </p>
                </div>
                <button class="sm-toggle_btn">
                    Read more
                    <span class="toggle-icocn_wraper"> <iconify-icon icon="uil:angle-down"></iconify-icon> </span>
                </button>
            </div>
        </div>
    </div>
</section>
