@if (get_site_config_value('plan_your_trips'))
    @php
        $row = get_site_config_value('plan_your_trips');
        $row = json_decode($row);
    @endphp
    <div class=" plan_trip--section">
        <div class="container container-custom margin_80_55">
            <div class="main_title_2 add_bottom_60 sm-margin_bottom">
                <h2>{{ $row->section_title }}</h2>
                <p>{{ $row->section_subtitle }}</p>
            </div>
            <div class="row adventure_feat gateway-adventure-container">
                <div class="col-md-4">
                    <div class="trip-card">
                        <div class="img-wrapper">
                            <img loading="lazy" src="{{ asset($row->first_section_image) }}" alt="{{ $row->first_title }}">
                        </div>
                        <h3 class="title">{{ $row->first_title }}</h3>
                        <p class="trip-info">{{ $row->first_section_desc }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="trip-card">
                        <div class="img-wrapper">
                            <img loading="lazy" src="{{ asset($row->second_section_image) }}"
                                alt="{{ $row->second_title }}">
                        </div>
                        <h3 class="title">{{ $row->second_title }}</h3>
                        <p class="trip-info">{{ $row->second_section_desc }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="trip-card">
                        <div class="img-wrapper">
                            <img loading="lazy" src="{{ asset($row->third_section_image) }}"
                                alt="{{ $row->third_title }}">
                        </div>
                        <h3 class="title">{{ $row->third_title }}</h3>
                        <p class="trip-info">{{ $row->third_section_desc }}</p>
                    </div>
                </div>
            </div>

            <div class="gateway-carousel_container">
                <div class="owl-carousel Gateway-carousel_wraper">
                    <div>
                        <div class="gatewayslider-card">
                            <div class="image-wraper">
                                <img loading="lazy" src="{{ asset($row->first_section_image) }}" alt="{{ $row->first_title }}">
                                 {{-- {!! render_image($row->first_section_image, [
                                    'alt' => $row->first_title,
                                    'loading' => 'lazy',
                                    'width' => '363px',
                                    'height' => '167px',
                                ]) !!} --}}
                            </div>
                            <h3 class="slider-title">{{ $row->first_title }}</h3>
                            <p class="slider-text">
                                {{ $row->first_section_desc }}
                            </p>
                        </div>
                    </div>
                    <div>
                        <div class="gatewayslider-card">
                            <div class="image-wraper">
                                <img loading="lazy" src="{{ asset($row->second_section_image) }}"
                                    alt="{{ $row->second_title }}">
                            </div>
                            <h3 class="slider-title">{{ $row->second_title }}</h3>
                            <p class="slider-text">
                                {{ $row->second_section_desc }}
                            </p>
                        </div>
                    </div>
                    <div>
                        <div class="gatewayslider-card">
                            <div class="image-wraper">
                                <img loading="lazy" src="{{ asset($row->third_section_image) }}"
                                    alt="{{ $row->third_title }}">
                            </div>
                            <h3 class="slider-title">{{ $row->third_title }}</h3>
                            <p class="slider-text">
                                {{ $row->third_section_desc }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
