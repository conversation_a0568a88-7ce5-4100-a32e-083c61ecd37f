@extends('frontend.layouts.master')

@push('title', $data['meta']['meta_title'])

@push('seo')
    <meta name="description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="keywords" content="{{ $data['meta']['meta_keyword'] }}" />
    <meta name="og:title" content="{{ $data['meta']['meta_title'] }}" />
    <meta name="og:description" content="{{ $data['meta']['meta_desc'] }}" />
    <meta name="og:url" content="{{ route('contact-us') }}" />
    <meta name="og:type" content="website" />
    <meta name="og:site_name" content="{{ config('app.name') }}" />
    <link rel="canonical" href="{{ route('contact-us') }}" />
@endpush


@section('content')
    <main>
        <section class="hero_in contacts"
            style="background: url('{{ get_image_full_url(get_site_config_value('contact_banner')) }}') center center no-repeat; background-size: cover;">
            <div class="wrapper">
                <div class="container">
                    <h1 class="fadeInUp"><span></span>{{ get_site_config_value('contact_page_title') ?? 'Contact Us' }}</h1>
                </div>
            </div>
        </section>
        <!--/hero_in-->

        <div class="contact_info">
            <div class="container">
                <ul class="clearfix">
                    <li>
                        <i class="pe-7s-map-marker"></i>
                        <h4>Address</h4>
                        <span>{{ get_site_config_value('location') }}</span>
                    </li>
                    <li>
                        <i class="pe-7s-mail-open-file"></i>
                        <h4>Email address</h4>
                        <span>{{ get_site_config_value('email') }}</span>

                    </li>
                    <li>
                        <i class="pe-7s-phone"></i>
                        <h4>Contacts info</h4>
                        <span>{{ get_site_config_value('phone') }}</span>
                    </li>
                </ul>
            </div>
        </div>
        <!--/contact_info-->

        <div class="bg_color_1">
            <div class="container margin_80_55">
                <div class="row justify-content-between">
                    <div class="col-lg-5">
                        <div class="map_contact">
                            @if (get_site_config_value('contact_page_map'))
                                <iframe src="{{ get_site_config_value('contact_page_map') }}"
                                    style="border:0; width: 100%; height: 400px" allowfullscreen="" loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade"></iframe>
                            @endif
                        </div>
                        <!-- /map -->
                    </div>


                    <div class="col-lg-6">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        <h4>{{ get_site_config_value('contact_page_title') ?? 'Contact Us' }}</h4>
                        <p>We are just One Message Away</p>
                        <div id="message-contact"></div>
                        <form method="Post" action="{{ route('contact-us.post') }}" id="contactform" autocomplete="off">
                            @csrf
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Full Name <span class="text-danger">*</span></label>
                                        <input class="form-control" type="text" id="full_name" name="full_name"
                                            value="{{ old('full_name') }}" required>
                                        @error('full_name')
                                            <label class="has-error" for="name">{{ $message }}</label>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- /row -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Email <span class="text-danger">*</span></label>
                                        <input class="form-control" type="email" id="email" name="email"
                                            value="{{ old('email') }}" required>
                                        @error('email')
                                            <label class="has-error" for="email">{{ $message }}</label>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Phone No. <span class="text-danger">*</span></label>
                                        <input class="form-control" type="text" id="phone_no" name="phone_no"
                                            value="{{ old('phone_no') }}" required data-intl-tel-input>
                                        @error('phone_no')
                                            <label class="has-error" for="phone_no">{{ $message }}</label>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- /row -->

                            <div class="form-group">
                                <label>Message <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="query" required name="query" rows="5">{{ old('query') }}</textarea>
                                @error('query')
                                    <label class="has-error" for="query">{{ $message }}</label>
                                @enderror
                            </div>

                            @error('recaptcha_token')
                                <p class="text-danger">{{ $message }}</p>
                            @enderror

                            <p class="add_top_30">
                                <button type="submit" class="btn_1 rounded" id="submit-contact"> Submit </button>
                            </p>
                        </form>
                    </div>
                </div>
                <!-- /row -->
            </div>
            <!-- /container -->
        </div>
        <!-- /bg_color_1 -->
    </main>
@endsection

@include('common.int-tel-input')

@push('js')
    <script src="{{ asset('assets/js/jquery.validate.min.js') }}"></script>

    <!-- Add reCAPTCHA script -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    <script src="{{ asset('assets/js/recaptcha-validation.js') }}"></script>
    <script>
        $(document).ready(function() {
            {!! toaster() !!}

            // Initialize reCAPTCHA
            recaptchaHandler.init('{{ config('services.recaptcha.site_key') }}');
            $('#contactform').validate({
                rules: {
                    full_name: {
                        required: true,
                    },
                    email: {
                        required: true,
                        email: true,
                    },
                    phone_no: {
                        required: true,
                        intlTelNumber: true,
                    },
                    query: {
                        required: true,
                    },
                },
                messages: {
                    full_name: {
                        required: "Please enter your full name",
                    },
                    email: {
                        required: "Please enter your email address",
                        email: "Please enter a valid email address",
                    },
                    phone_no: {
                        required: "Please enter your phone number",
                    },
                    query: {
                        required: "Please enter your query",
                    },
                },
                errorPlacement: function(error, element) {
                    error.addClass("has-error");
                    element.closest(".form-group").append(error);
                },
                submitHandler: function(form) {
                    recaptchaHandler.getToken()
                        .then(function(token) {
                            recaptchaHandler.injectToken(form, token);
                            form.submit();
                        })
                        .catch(function(err) {
                            alert("reCAPTCHA failed: " + err);
                        });
                },
            });
        });
    </script>
@endpush
