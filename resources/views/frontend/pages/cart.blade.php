@extends('frontend.layouts.master')

@push('title', 'Your Cart')

@section('content')
    <main>
        <div class="hero_in cart_section">
            <div class="wrapper">
                <div class="container">
                    <div class="bs-wizard clearfix">
                        <div class="bs-wizard-step active">
                            <div class="text-center bs-wizard-stepnum">Your cart</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="#0" class="bs-wizard-dot"></a>
                        </div>

                        <div class="bs-wizard-step disabled">
                            <div class="text-center bs-wizard-stepnum">Payment</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="#0" class="bs-wizard-dot"></a>
                        </div>

                        <div class="bs-wizard-step disabled">
                            <div class="text-center bs-wizard-stepnum">Finish!</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="#0" class="bs-wizard-dot"></a>
                        </div>
                    </div>
                    <!-- End bs-wizard -->
                </div>
            </div>
        </div>
        <!--/hero_in-->

        <div class="bg_color_1">
            <div class="container margin_60_35">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="box_cart">
                            <table class="table table-striped cart-list">
                                <thead>
                                    <tr>
                                        <th>
                                            Item
                                        </th>
                                        <th>
                                            Quantity
                                        </th>
                                        <th>
                                            Price
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="thumb_cart">
                                                <img src="{{ get_image_full_url($data['tour']->image) }}" alt="Image">
                                            </div>
                                            <span class="item_cart">{{ $data['tour']->name }}</span>
                                        </td>
                                        <td>
                                            @if ($data['carts']['adults'])
                                                Adult : {{ $data['carts']['adults'] }}
                                            @endif
                                            <br>
                                            @if ($data['carts']['children'])
                                                Child : {{ $data['carts']['children'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($data['carts']['adults'])
                                                <strong>{{ currency_type($data['carts']['adult_price']) }}</strong>
                                            @endif
                                            <br>
                                            @if ($data['carts']['children'])
                                                <strong>{{ currency_type($data['carts']['children_price']) }}</strong>
                                            @endif
                                        </td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /col -->

                    <aside class="col-lg-4" id="sidebar">
                        <div class="box_detail">
                            <div id="total_cart">
                                Total <span class="float-right">{{ currency_type($data['carts']['total_price']) }}</span>
                            </div>
                            <ul class="cart_details">
                                <li>Date <span>{{ $data['carts']['start_date'] }} - {{ $data['carts']['end_date'] }}</span>
                                </li>
                                <li>Adults <span>{{ $data['carts']['adults'] }}</span></li>
                                <li>Children <span>{{ $data['carts']['children'] }}</span></li>
                            </ul>
                            <a href="{{ route('checkout') }}" class="btn_1 full-width purchase checkout-cart__btn">Checkout</a>
                            <div class="text-center"><small>No money charged in this step</small></div>
                        </div>
                    </aside>
                </div>
                <!-- /row -->
            </div>
            <!-- /container -->
        </div>
    </main>
@endsection
