@extends('frontend.layouts.master')

@push('title', 'Payment')

@section('content')
    <main>
        <div class="hero_in cart_section">
            <div class="wrapper">
                <div class="container">
                    <div class="bs-wizard clearfix">
                        <div class="bs-wizard-step">
                            <div class="text-center bs-wizard-stepnum">Your cart</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="{{ route('carts') }}" class="bs-wizard-dot"></a>
                        </div>

                        <div class="bs-wizard-step active">
                            <div class="text-center bs-wizard-stepnum">Payment</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="#0" class="bs-wizard-dot"></a>
                        </div>

                        <div class="bs-wizard-step disabled">
                            <div class="text-center bs-wizard-stepnum">Finish!</div>
                            <div class="progress">
                                <div class="progress-bar"></div>
                            </div>
                            <a href="#0" class="bs-wizard-dot"></a>
                        </div>
                    </div>
                    <!-- End bs-wizard -->
                </div>
            </div>
        </div>
        <div class="bg_color_1">
            <div class="container margin_60_35">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="box_cart">
                            <div class="message">
                                <!--End step -->
                                @if (session('success'))
                                    <div class="alert alert-success">{{ session('success') }}</div>
                                @endif

                                @if (session('error'))
                                    <div class="alert alert-danger">{{ session('error') }}</div>
                                @endif

                                <div class="form_title">
                                    <h3><strong>1</strong>Payment Information</h3>
                                </div>
                                <div class="step">
                                    @if (usd_currency())
                                        <form action="{{ route('stripe.checkout') }}" method="POST" id="payment-form">
                                            @csrf
                                            <button type="submit" class="btn_1 purchase checkout-cart__btn">
                                                Pay via Stripe
                                            </button>
                                        </form>
                                    @else
                                        <form action="{{ route('razorpay.payment') }} " method="POST" id="payment-form">
                                            @csrf
                                            <script src="https://checkout.razorpay.com/v1/checkout.js" data-key="{{ config('razor.razor_key_id') }}"
                                                data-amount="{{ $data['total_price'] }}" data-order_id="{{ $order['id'] }}"
                                                data-buttontext="Pay {{ $data['total_price'] * 100 }} INR" data-name="{{ config('app.name') }}" data-currency="INR"
                                                data-description="{{ 'Payment for Orrog Booking' }}" data-image="{{ asset('assets/img/razorpay.png') }}"
                                                data-prefill.name="{{ auth()->user()->name }}" data-prefill.email="{{ auth()->user()->email }}"
                                                data-theme.color="#ff7529"></script>
                                        </form>
                                    @endif
                                </div>
                                <hr>

                                <div id="policy">
                                    <h5>Cancellation policy</h5>
                                    {!! get_site_config_value('cancellation_policy') !!}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /row -->
                </div>
                <!-- /container -->
            </div>
        </div>
    </main>
@endsection
