@extends('frontend.layouts.master')

@push('title', $row->meta_title ?? $row->name)
@push('css')
    <!-- Toastr CSS -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" as="style"
        onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    </noscript>

    <!-- Flatpickr CSS -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" as="style"
        onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    </noscript>

    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />


    <style>
        .gears_and_equipment__section {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .gears_and_equipment--title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .gears_and_equipment_description p {
            margin-bottom: 1rem;
            line-height: 1.6;
            color: #666;
        }

        .gears_category_item {
            margin-bottom: 2rem;
        }

        .gears_category_header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .gears_category_header .icon-wrapper {
            background-color: #f1f6fb;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .gears_category_header .icon-wrapper img {
            max-width: 35px;
            max-height: 35px;
        }

        .gears_category_name {
            font-size: 22px;
            font-weight: 600;
        }

        .gears_list {
            list-style: none;
            padding-left: 0;
            margin-left: 10px;
        }

        .gear_item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 16px;
            color: #333;
        }

        .gear_icon {
            color: #0d5cab;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .gear_name p {
            margin-bottom: 0;
        }


        @media screen and (max-width: 768px) {
            footer {
                padding-bottom: 100px;
            }

            #toTop {
                right: 25px;
                bottom: 130px;

                #toTop {
                    right: 25px;
                    bottom: 130px;
                }
            }
    </style>
@endpush

@push('seo')
    <meta name="description" content="{{ $row->meta_desc }}" />
    <meta name="keywords" content="{{ $row->meta_keyword }}" />
    <meta name="og:title" content="{{ $row->meta_title }}" />
    <meta name="og:description" content="{{ $row->meta_desc }}" />
    @if ($row->image)
        <meta name="og:image" content="{{ get_image_full_url($row->image) }}" />
    @endif
    <meta name="og:url" content="{{ route('tour.detail', $row->slug) }}" />
    <meta name="og:type" content="website" />
    <meta name="og:site_name" content="{{ config('app.name') }}" />
    <link rel="canonical" href="{{ route('tour.detail', $row->slug) }}" />

    @if ($row->gallery && $row->gallery->isNotEmpty())
        <script>
            (function() {
                const isMobile = window.matchMedia("(max-width: 768px)").matches;
                const imagesToPreload = isMobile ? 3 : 5;
                const gallery = {!! json_encode(
                    $row->gallery->where('source', 'image')->take(5)->pluck('media')->map(function ($media) {
                            return get_webp_image_url(get_image_full_url($media));
                        }),
                ) !!};

                for (let i = 0; i < Math.min(gallery.length, imagesToPreload); i++) {
                    const link = document.createElement('link');
                    link.rel = 'preload';
                    link.as = 'image';
                    link.href = gallery[i];
                    link.fetchPriority = 'high';
                    document.head.appendChild(link);
                }
            })();
        </script>
    @endif

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "Product",
                    "name": "{{ $row->name }}",
                    "image": "{{ get_image_full_url($row->image) }}",
                    "description": "{{ $row->meta_desc ?: $row->name }}",
                    "sku": "{{ $row->slug }}",
                    "offers": {
                        "@type": "Offer",
                        "url": "{{ route('tour.detail', $row->slug) }}",
                        "priceCurrency": "{{ session()->get('currency') }}",
                        "price": "{{ price_without_currency($row) }}",
                        "priceValidUntil": "{{ date('Y-12-31') }}",
                        "availability": "https://schema.org/InStock",
                        "hasMerchantReturnPolicy": {
                            "@type": "MerchantReturnPolicy",
                            "applicableCountry": "US",
                            "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                            "merchantReturnDays": 14,
                            "returnMethod": "https://schema.org/ReturnByMail",
                            "returnFees": "https://schema.org/FreeReturn"
                        },
                        "shippingDetails": {
                            "@type": "ShippingDetails",
                            "shippingRate": {
                                "@type": "MonetaryAmount",
                                "value": "0",
                                "currency": "{{ session()->get('currency') }}"
                            }
                        }
                    },
                    "aggregateRating": {
                        "@type": "AggregateRating",
                        "ratingValue": "{{ $row->published_reviews_avg_rating > 0 ? number_format($row->published_reviews_avg_rating, 1) : 5 }}",
                        "reviewCount": {{ $row->published_reviews_count > 0 ? $row->published_reviews_count : 1 }}
                    },
                    "review": [
                        @forelse ($reviews->take(5) as $review)
                            {
                                "@type": "Review",
                                "reviewRating": {
                                    "@type": "Rating",
                                    "ratingValue": "{{ $review->rating }}",
                                    "bestRating": "5"
                                },
                                "author": {
                                    "@type": "Person",
                                    "name": "{{ $review->user?->full_name ?? 'Anonymous' }}"
                                }
                            }@if (!$loop->last),@endif
                        @empty
                            {
                                "@type": "Review",
                                "reviewRating": {
                                    "@type": "Rating",
                                    "ratingValue": "5",
                                    "bestRating": "5"
                                },
                                "author": {
                                    "@type": "Person",
                                    "name": "Satisfied Customer"
                                }
                            }
                        @endforelse
                    ]
                },
                {
                    "@type": "BreadcrumbList",
                    "itemListElement": [
                        {
                            "@type": "ListItem",
                            "position": 1,
                            "name": "Home",
                            "item": "{{ route('home') }}"
                        },
                        {
                            "@type": "ListItem",
                            "position": 2,
                            "name": "{{ $row->category->category_title }}",
                            "item": "{{ route('tours', $row->category->slug) }}"
                        },
                        {
                            "@type": "ListItem",
                            "position": 3,
                            "name": "{{ $row->name }}"
                        }
                    ]
                }
            ]
        }
    </script>
@endpush

@section('content')
    <section class="itinerary_banner--section">

        @if (count($row->gallery))
            <div class="banner__gallery">
                <button class="gallery__btn" data-toggle="modal" data-target="#galleryModal">
                    <span class="icon-wrapper">
                        <iconify-icon icon="ri:image-add-line"></iconify-icon>
                    </span>
                    <span class="textcontent">
                        See all photos
                    </span>

                </button>
                <!-- Modal -->
                <div class="modal fade gallerymodalwrapper" id="galleryModal" tabindex="-1" role="dialog"
                    aria-labelledby="galleryModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">

                            <div class="modal-header">

                                <button data-dismiss="modal" aria-label="Close" class="backIcon-wrapper">
                                    <iconify-icon icon="mdi:arrow-left" class="icon"></iconify-icon>
                                </button>
                                <div class="titlewrapper">
                                    Gallery
                                </div>


                            </div>

                            <div class="modal-body">
                                <div class="container">
                                    <div class="galleryimage__gridcontainer">
                                        @foreach ($row->gallery->take(5) as $gallery)
                                            @switch($gallery->source)
                                                @case('image')
                                                    <a href="{{ get_image_full_url($gallery->media) }}" class="banner__img"
                                                        data-fancybox="gallery" data-caption="{{ $gallery->caption }}">
                                                        {!! render_image($gallery->media, [
                                                            'sizes' => '(max-width: 768px) 100vw, 50vw',
                                                            'alt' => $gallery->alt_text,
                                                            'loading' => 'lazy',
                                                        ]) !!}
                                                    </a>
                                                @break

                                                @case('youtube')
                                                    <a href="{{ $gallery->media }}" data-fancybox="gallery"
                                                        class="banner__img videothumbnail">
                                                        <img alt=""
                                                            src="https://img.youtube.com/vi/{{ get_youtube_video_id($gallery->media) }}/0.jpg"
                                                            loading="lazy">
                                                        <div class="play-icon-wrapper">
                                                            <div class="play-icon">
                                                                <iconify-icon class="icon-wrapper"
                                                                    icon="line-md:play-filled"></iconify-icon>
                                                            </div>
                                                        </div>
                                                    </a>
                                                @endswitch
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="banner__grid">
                        @foreach ($row->gallery->take(5) as $gallery)
                            @switch($gallery->source)
                                @case('image')
                                    <a href="{{ get_image_full_url($gallery->media) }}" class="banner__img" data-fancybox="gallery"
                                        data-caption="{{ $gallery->caption }}">
                                        {!! render_image($gallery->media, [
                                            'sizes' => '(max-width: 768px) 100vw, 50vw',
                                            'alt' => $gallery->alt_text,
                                            'loading' => 'lazy',
                                        ]) !!}
                                    </a>
                                @break

                                @case('youtube')
                                    <a href="{{ $gallery->media }}" data-fancybox="gallery" class="banner__img videothumbnail">
                                        <img alt="{{ $gallery->alt_text }}"
                                            src="https://img.youtube.com/vi/{{ get_youtube_video_id($gallery->media) }}/0.jpg"
                                            loading="lazy">
                                        <div class="play-icon-wrapper">
                                            <div class="play-icon">
                                                <iconify-icon class="icon-wrapper" icon="line-md:play-filled"></iconify-icon>
                                            </div>
                                        </div>
                                    </a>
                                @endswitch
                            @endforeach
                        </div>
                    </div>
                @endif


            </section>
            <section class="breadcrumb__wrapper">
                <div class="container">
                    <ul class="breadcrumbs">
                        <li class="breadcrumb-items">
                            <a href="{{ route('home') }}" class="breadcrumbs-link">Home</a>
                        </li>
                        <li class="breadcrumb-items">
                            <a class="breadcrumbs-link"
                                href="{{ route('tours', $row?->category->slug) }}">{{ $row?->category->category_title }}</a>
                        </li>
                        <li class="active breadcrumb-items">
                            {{ $row->name }}
                        </li>
                    </ul>
                </div>
            </section>

            <section class="tour-detail--section">
                <div class="container ">
                    <div class="tourdetails__wrapper row">
                        <div class="col-lg-8 tourdetails__wrapper--main">

                            <section class="tourdetails__overview itinerarydetailsection" id="overviewTab">
                                <h1 class="tourdetails__title">{{ $row->name }} -
                                    {{ $row->days }} Days</h1>
                                <div class="tourdetails__desc">
                                    <p> {!! $row->short_desc !!}</p>
                                </div>

                                @if (count($row->attributes))
                                    <div class="tour-facts">
                                        <div class="tour-facts__grid">
                                            @foreach ($row->attributes as $attr)
                                                <div class="tour-facts__item">
                                                    <div class="icon-wrapper">
                                                        {{-- <iconify-icon icon="fluent:globe-location-20-regular"></iconify-icon> --}}
                                                        {!! render_image($attr->image, ['sizes' => '40px', 'alt' => $attr->name, 'width' => '40', 'loading' => 'lazy']) !!}
                                                    </div>
                                                    <div class="tour-facts__contents">
                                                        <div class="tour-facts__title">
                                                            {{ $attr->name }}
                                                        </div>
                                                        <div class="tour-facts__text">
                                                            {{ $attr->pivot->value }}
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                                @if ($row->highlights)
                                    <div class="tour-highlights">
                                        <h2 class="tour-highlights--title">
                                            {{ $row->highlight_title }}
                                        </h2>
                                        <div class="highlights__list--wrapper">
                                            {!! $row->highlights !!}
                                        </div>
                                    </div>
                                @endif

                                <div class="tour-overview__contents">
                                    <h3 class="tour-overview__title">
                                        Trip Overview
                                        </h2>
                                        <div class="tour-overview__description">
                                            <div class="description__contents">
                                                {!! $row->description !!}
                                            </div>
                                            <div class="tour-overview__toggleBtnwrapper">
                                                <button class="tour-overview__toggle--btn">Read more
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="uil:angle-down"></iconify-icon>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                </div>
                            </section>
                            @if (isset($row->short_itinerary) && count(json_decode($row->short_itinerary, true)))
                                <section class="short__itinerary section">
                                    <h2 class="heading">Short Itinerary</h2>
                                    <div class="short-itenerary__items">
                                        @php $key = 1; @endphp
                                        @foreach (json_decode($row->short_itinerary, true) as $short_itinerary)
                                            <div class="short-itenerary__item">
                                                <span class="short-itenerary__item-day">Day {{ $key < 10 ? '0' . $key : $key }}
                                                    :</span>
                                                <div class="short-itenerary__item-title">
                                                    <p></p>
                                                    <p>{{ $short_itinerary['title'] }}</p>
                                                    <p></p>
                                                </div>
                                            </div>
                                            @php $key++; @endphp
                                        @endforeach
                                    </div>
                                </section>
                            @endif
                            <section class="tour_costdetail__section itinerarydetailsection" id="costdetailTab">
                                @if ($row->inclusion)
                                    <div class="included-price__wrapper">
                                        <h3 class="included-price--title">
                                            Price Includes
                                            </h2>

                                            <div class="costdetail--list included-list">
                                                {!! $row->inclusion !!}
                                            </div>
                                    </div>
                                @endif
                                @if ($row->exclusion)
                                    <div class="excluded-price__wrapper">
                                        <h3 class="excluded-price--title">
                                            Price Excludes
                                            </h2>

                                            <div class="costdetail--list excluded-list">
                                                {!! $row->exclusion !!}
                                            </div>
                                    </div>
                                @endif

                            </section>

                            @if (isset($row->gearCategories) && count($row->gearCategories))
                                <section class="gears_and_equipment__section itinerarydetailsection" id="gearsTab">
                                    <div class="gears_titlewrapper">
                                        <h3 class="gears_and_equipment--title">
                                            Things to pack for {{ $row->name }}
                                            </h2>
                                    </div>
                                    <div class="gears_and_equipment_description">
                                        @if ($row->gears_description)
                                            {!! $row->gears_description !!}
                                        @endif
                                    </div>

                                    <div class="gears_category_list">
                                        @foreach ($row->gearCategories as $category)
                                            <div class="gears_category_item">
                                                <div class="gears_category_header">
                                                    <div class="icon-wrapper">
                                                        @if ($category->image)
                                                            {!! render_image($category->image, [
                                                                'sizes' => '40px',
                                                                'alt' => $category->name,
                                                                'width' => '40',
                                                                'height' => '40',
                                                                'loading' => 'lazy',
                                                            ]) !!}
                                                        @endif
                                                    </div>
                                                    <h3 class="gears_category_name">{{ $category->name }}</h3>
                                                </div>
                                                @if (count($category->gears))
                                                    <ul class="gears_list">
                                                        @foreach ($category->gears as $gear)
                                                            <li class="gear_item">
                                                                <span class="gear_icon">
                                                                    <iconify-icon
                                                                        icon="material-symbols:arrow-right-alt-rounded"></iconify-icon>
                                                                </span>
                                                                <span class="gear_name">{!! $gear->name !!}</span>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                </section>
                            @endif

                            @if (count($row->itinerary))
                                <section class="tour_itinerary__section itinerarydetailsection" id="itineraryTab">
                                    <div class="itinerary_titlewrapper ">
                                        <h3 class="tour_itinerary--title">
                                            {{ $row->name }} Itinerary
                                            </h2>
                                            <button class="accordion_toggle--btn" id="itinerary-accordion-toggle">
                                                Expand all
                                            </button>
                                    </div>

                                    <div class="accordion" id="itineraryaccordion">
                                        @foreach ($row->itinerary as $itinerary)
                                            <div class="card">
                                                <div class="card-header" id="headingOne">
                                                    <h2 class="mb-0">
                                                        <button class="btn btn-link collapsed accordion-button" type="button"
                                                            data-toggle="collapse" data-target="#collapse{{ $itinerary->id }}"
                                                            aria-expanded="false" aria-controls="collapseOne">
                                                            {{ $itinerary->day }}
                                                        </button>
                                                    </h2>
                                                </div>

                                                <div id="collapse{{ $itinerary->id }}" class="collapse"
                                                    aria-labelledby="headingOne" data-parent="#itineraryaccordion">
                                                    <div class="itinerary__accordion-contents">

                                                        @if ($itinerary->attributes->count())
                                                            <div class="itinerary__accordion-stats">
                                                                @forelse($itinerary->attributes as $attr)
                                                                    <div class="stats-item">
                                                                        <div class="icon-wrapper">
                                                                            {!! render_image($attr->image, ['sizes' => '18px', 'alt' => $attr->name, 'width' => '18', 'loading' => 'lazy']) !!}
                                                                        </div>
                                                                        <div class="stats-itemcontents">
                                                                            <span class="itemtitle">
                                                                                {{ $attr->name }}:
                                                                            </span>
                                                                            <span class="itemcontent">
                                                                                {{ $attr->pivot->value }}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                @empty
                                                                @endforelse
                                                            </div>
                                                        @endif
                                                        <div class="itinerary__accordion-maincontainer">
                                                            {!! $itinerary->detail !!}
                                                            <div class="itinerary__accordion-gallerywrapper">
                                                                <h3 class="titlewrapper">
                                                                    Gallery
                                                                </h3>
                                                                <div class="gallery-wrap">
                                                                    @foreach ($itinerary->itineraryMedia as $gallery)
                                                                        @switch($gallery->source)
                                                                            @case('image')
                                                                                <a href="{{ get_image_full_url($gallery->media) }}"
                                                                                    class="img-wrapper" data-fancybox="gallery"
                                                                                    data-caption="{{ $gallery->image_caption }}">
                                                                                    {!! render_image($gallery->media, [
                                                                                        'sizes' => '(max-width: 768px) 100vw, 33vw',
                                                                                        'alt' => $gallery->alt_text,
                                                                                        'loading' => 'lazy',
                                                                                    ]) !!}
                                                                                </a>
                                                                            @break
                                                                        @endswitch
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @if (count($itinerary->itineraryActivities))
                                                            <div class="itinerary_activities--wrapper">
                                                                <h3 class="titlewrapper">
                                                                    Activities
                                                                </h3>
                                                                <div class="activities_slider--containerBox">
                                                                    <div class="swiper activities__card--grid">


                                                                        <div class="swiper-wrapper">
                                                                            @foreach ($itinerary->itineraryActivities as $activity)
                                                                                <div class="swiper-slide">
                                                                                    <div class="activities__cardwrapper">
                                                                                        <div class="activities_img-wrapper">
                                                                                            {!! render_image($activity->image, [
                                                                                                'sizes' => '(max-width: 768px) 100vw, 33vw',
                                                                                                'alt' => $activity->title,
                                                                                                'loading' => 'lazy',
                                                                                            ]) !!}
                                                                                        </div>
                                                                                        <div class="activitites_content">
                                                                                            <h6 class="title">
                                                                                                {{ $activity->title }}
                                                                                            </h6>
                                                                                            <div class="activities-desc--sm">
                                                                                                {!! $activity->description !!}
                                                                                            </div>

                                                                                            <button type="button"
                                                                                                class="itinerary_activities--btn"
                                                                                                data-toggle="modal"
                                                                                                data-id="{{ $activity->id }}"
                                                                                                data-name="{{ $activity->title }}"
                                                                                                data-description="{{ $activity->description }}"
                                                                                                data-image="{{ get_image_full_url($activity->image) }}"
                                                                                                data-attributes="{{ $activity->attributes }}"
                                                                                                data-target="#itinerayActivity">
                                                                                                See more
                                                                                            </button>

                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            @endforeach
                                                                        </div>
                                                                        <div class="swiper-pagination"></div>
                                                                    </div>
                                                                    <!-- Modal -->

                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="modal fade  itineraryactivities__modal" id="itinerayActivity" tabindex="-1"
                                        role="dialog" aria-labelledby="itinerayActivityLabel" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="btn-closes" data-dismiss="modal"
                                                        aria-label="Close">
                                                        Close
                                                        <iconify-icon icon="material-symbols:close"
                                                            class="icon-wrapper"></iconify-icon>
                                                    </button>

                                                </div>
                                                <div class="modal-body">
                                                    <h2 class="maintitle text-uppercase  text-center">
                                                        Included activity
                                                    </h2>
                                                    <div class="image-wrapper">
                                                        <img src="" alt="{{ $row->name }}"
                                                            class="image-itineray-show">
                                                    </div>
                                                    <div class="activity_tripcontent">
                                                        <h4 class="trip-title">

                                                        </h4>
                                                        <div class="activity_listwrapper">
                                                            <div class="activity_flexwrapper">
                                                                <div class="activity_listitem">
                                                                    <div class="icon-wrapper">
                                                                        <iconify-icon icon="mingcute:time-line"></iconify-icon>
                                                                    </div>
                                                                    <div class="text-content">
                                                                        2-4 hrs trek
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <div class="activity_shortinfo">
                                                                {{--                                                    Dress modestly, cover shoulders/knees, wear --}}
                                                                {{--                                                    easy-off shoes, and cover tattoos. --}}
                                                            </div>
                                                            <div class="activies_description">

                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>


                                </section>
                            @endif
                            @if (!empty($row->know_before) && count($row->know_before))
                                <section class="tour_equipmentInfo__section itinerarydetailsection" id="tour_equipmentInfo">
                                    <h2 class="tour_itinerary--title">
                                        Know Before You Travel
                                    </h2>
                                    <div class="accordion  tour_equipmentInfo--accordion" id="accordionGroup">
                                        @forelse($row->know_before as $kb)
                                            <div class="card">
                                                <div class="card-header" id="{{ $loop->index . '_' . Str::slug($kb['title']) }}">
                                                    <h2 class="mb-0">
                                                        <button class="btn btn-link collapsed" type="button"
                                                            data-toggle="collapse" data-target="#panel{{ $loop->index }}"
                                                            aria-expanded="true" aria-controls="panel{{ $loop->index }}">

                                                            {{ $kb['title'] }}

                                                        </button>
                                                    </h2>
                                                </div>
                                                <div id="panel{{ $loop->index }}" class="collapse"
                                                    aria-labelledby="{{ $loop->index . '_' . Str::slug($kb['title']) }}"
                                                    data-parent="#accordionGroup">
                                                    <div class="accordionContents">
                                                        <ul>
                                                            @forelse($kb['items'] as $item)
                                                                <li>
                                                                    <strong>{{ $item['title'] }}</strong>: {!! $item['content'] !!}
                                                                </li>
                                                            @empty
                                                            @endforelse
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        @empty
                                        @endforelse

                                    </div>

                                </section>
                            @endif
                            @if ($row->map_image)
                                <section class="tour_map__section itinerarydetailsection" id="tour_mapTab">
                                    <div class="title-wrapper">
                                        <h2 class="title">
                                            {{ $row->map_title ?? $row->name . ' Map' }}
                                        </h2>
                                        <a href="{{ get_image_full_url($row->map_image) }}" download
                                            class="downloadmap-pdf">Download <span class="icon-wrapper"><iconify-icon
                                                    icon="material-symbols:download"></iconify-icon></span></a>
                                    </div>

                                    <div class="tourmap__imgwrapper">
                                        {!! render_image($row->map_image, [
                                            'sizes' => '(max-width: 768px) 100vw, 50vw',
                                            'alt' => $row->map_title ?? $row->name,
                                            'loading' => 'lazy',
                                        ]) !!}
                                    </div>

                                </section>
                            @endif

                            @if ($row->video_code)
                                <section class="tour_video__section itinerarydetailsection" id="tour_videoTab">
                                    <h2 class="title">
                                        {{ $row->video_title ?? $row->name . ' Video' }}
                                    </h2>
                                    <div class="video-wrapper">
                                        <div class="plyr__video-embed" id="tripvideosection">
                                            <iframe loading="lazy" src="https://www.youtube.com/embed/{{ $row->video_code }}"
                                                title="{{ $row->video_title }}" frameborder="0"
                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                                referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                                        </div>
                                    </div>
                                </section>
                            @endif

                            @if (count($row?->faqs))
                                <section class="tour__faqsection itinerarydetailsection" id="tour_faqTab">
                                    <div class="faq-titlewrapper">
                                        <h2 class="tour_faq--title">
                                            FAQs for {{ $row->name }}
                                        </h2>
                                        <button class="faqaccordion_toggle--btn" id="faqaccordion-toggle">
                                            Expand all
                                        </button>
                                    </div>
                                    <div class="faqaccordion__mainwrapper">
                                        <div class="accordion" id="faqAccordion">
                                            @php
                                                $faqs = $row->faqs()->get()->groupBy('group_name');
                                            @endphp
                                            @forelse($faqs as $group=> $faq)
                                                <div class="accordion-wrap">
                                                    <div class="tripfaq__accordionheader">
                                                        {{ $group }}
                                                    </div>
                                                    <div class="accordion-wrapper">
                                                        @forelse($faq as $item)
                                                            <div class="card">
                                                                <div class="card-header"
                                                                    id="faqHeading{{ str_slug($group) . '_' . $loop->index }}">
                                                                    <h2 class="mb-0">
                                                                        <button class="btn btn-link collapsed" type="button"
                                                                            data-toggle="collapse"
                                                                            data-target="#faqCollapse{{ str_slug($group) . '_' . $loop->index }}"
                                                                            aria-expanded="true"
                                                                            aria-controls="faqCollapse{{ str_slug($group) . '_' . $loop->index }}">
                                                                            {{ $item->question }}
                                                                        </button>
                                                                    </h2>
                                                                </div>

                                                                <div id="faqCollapse{{ str_slug($group) . '_' . $loop->index }}"
                                                                    class="collapse"
                                                                    aria-labelledby="faqHeading{{ str_slug($group) . '_' . $loop->index }}"
                                                                    data-parent="#faqAccordion">
                                                                    <div class="card-body">
                                                                        {!! $item->answer !!}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @empty
                                                        @endforelse
                                                    </div>
                                                </div>
                                            @empty
                                            @endforelse
                                        </div>
                                    </div>

                                </section>
                            @endif

                            @include('frontend.pages.tour._review')

                        </div>
                        <div class="tourdetails__wrapper--side col-lg-4">
                            @include('frontend.pages.partials.tour-detail-side')
                        </div>

                    </div>

                    @include('frontend.pages.partials.call-to-action')

                    @if (count($similarTours))
                        <div class="similaTripSliderWrapper">
                            <h2 class="similaTrip--title">
                                Similar Trip
                            </h2>
                            <div class="swiper similartripslider">
                                <div class="swiper-wrapper">
                                    @forelse($similarTours as $tour)
                                        <div class="swiper-slide">
                                            <div class="relatedtripCard">
                                                <div class="imageoverlay">
                                                    <a href="{{ route('tour.detail', $tour->slug) }}" class="img-wrapper">
                                                        {!! render_image($tour->image, [
                                                            'sizes' => '(max-width: 768px) 100vw, 33vw',
                                                            'alt' => $tour->name,
                                                            'loading' => 'lazy',
                                                        ]) !!}
                                                    </a>
                                                    <div class="trip-date"> {{ $tour->days }} days</div>
                                                </div>
                                                <div class="related--trips__content">
                                                    <a href="{{ route('tour.detail', $tour->slug) }}"
                                                        class="triptitle">{{ $tour->name }}</a>
                                                    <div class="trip-price"> <span>{{ price_with_currency($tour) }}</span>/ person
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                    @endforelse

                                </div>

                            </div>
                        </div>
                    @endif
                    <div class="itineraryTabNavigation" id="tourTabWrapper">
                        <div class="container">
                            <div class="itinerary_nav">
                                <nav class="navigation">
                                    <ul>
                                        <li>
                                            <a href="#overviewTab" class="nav-link ">
                                                <span class="icon-wrapper">
                                                    <iconify-icon icon="iconoir:eye"></iconify-icon>
                                                </span>
                                                Overview</a>
                                        </li>
                                        @if ($row->inclusion || $row->exclusion)
                                            <li>
                                                <a href="#costdetailTab" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="material-symbols:box-outline"></iconify-icon>
                                                    </span>
                                                    Cost details
                                                </a>
                                            </li>
                                        @endif
                                        @if (count($row->itinerary))
                                            <li>
                                                <a href="#itineraryTab" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="game-icons:wavy-itinerary"></iconify-icon>
                                                    </span>
                                                    Itinerary
                                                </a>
                                            </li>
                                        @endif
                                        @if (!empty($row->know_before) && count($row->know_before))
                                            <li>
                                                <a href="#tour_equipmentInfo" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="material-symbols:lab-research"></iconify-icon>
                                                    </span>
                                                    Know Before
                                                </a>
                                            </li>
                                        @endif
                                        @if ($row->map_image)
                                            <li>
                                                <a href="#tour_mapTab" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="hugeicons:maps-global-02"></iconify-icon>
                                                    </span>
                                                    Map
                                                </a>
                                            </li>
                                        @endif
                                        @if ($row->video_code)
                                            <li>
                                                <a href="#tour_videoTab" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="mingcute:video-line"></iconify-icon>
                                                    </span>
                                                    Video
                                                </a>
                                            </li>
                                        @endif
                                        @if (count($row?->faqs))
                                            <li>
                                                <a href="#tour_faqTab" class="nav-link">
                                                    <span class="icon-wrapper">
                                                        <iconify-icon icon="mingcute:question-line"></iconify-icon>
                                                    </span>FAQs
                                                </a>
                                            </li>
                                        @endif

                                        <li>
                                            <a href="#tour_reviewTab" class="nav-link">
                                                <span class="icon-wrapper">
                                                    <iconify-icon icon="mingcute:star-line"></iconify-icon>
                                                </span>Reviews
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>

                            <div class="itinerary_nav--sm">
                                <div class="itinerary-nav-headerwrapper">
                                    <span class="titlewrap" id="navTitleWrap">Overview</span>
                                    <span class="icon-wrapper">
                                        <iconify-icon icon="iconamoon:arrow-down-2" id="toggle-icon"></iconify-icon>
                                    </span>
                                </div>
                                <div class="itinerary_nav_dropdownmenu" id="tabdropdown-menu">
                                    <ul>
                                        <li>
                                            <a href="#overviewTab" class="nav-link ">
                                                Overview</a>
                                        </li>
                                        @if ($row->inclusion || $row->exclusion)
                                            <li>
                                                <a href="#costdetailTab" class="nav-link"> Cost details</a>
                                            </li>
                                        @endif
                                        @if (isset($row->gearCategories) && count($row->gearCategories))
                                            <li>
                                                <a href="#gearsTab" class="nav-link"> Gears and Equipment</a>
                                            </li>
                                        @endif
                                        @if (count($row->itinerary))
                                            <li>
                                                <a href="#itineraryTab" class="nav-link"> Itinerary</a>
                                            </li>
                                        @endif
                                        @if (!empty($row->know_before) && count($row->know_before))
                                            <li>
                                                <a href="#tour_equipmentInfo" class="nav-link"> Know Before</a>
                                            </li>
                                        @endif
                                        @if ($row->map_image)
                                            <li>
                                                <a href="#tour_mapTab" class="nav-link"> Map</a>
                                            </li>
                                        @endif
                                        @if ($row->video_code)
                                            <li>
                                                <a href="#tour_videoTab" class="nav-link"> Video</a>
                                            </li>
                                        @endif
                                        @if (count($row?->faqs))
                                            <li>
                                                <a href="#tour_faqTab" class="nav-link">
                                                    FAQs
                                                </a>
                                            </li>
                                        @endif

                                        <li>
                                            <a href="#tour_reviewTab" class="nav-link">
                                                Reviews
                                            </a>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>




                </div>

            </section>
            <div class="booking__btn--stickywrapper">
                <div class="trip-price"> <span>{{ price_with_currency($row) }}</span>/ person</div>
                <div class="btn-wrapper">
                    <button data-target="#choosedateoffcanvas" class="choose--btn btn--lg btn--secondary ">Choose Your
                        Date</button>
                    <button class="askquestion-btn btn--lg btn--primary " data-target="#askquestionoffcanvas">Ask a
                        Question</button>

                </div>


            </div>
        @endsection

        @push('js')
            <!-- Toastr JS -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js" defer></script>
            <!-- Fancybox JS -->
            <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
            <script src="https://www.google.com/recaptcha/api.js" async defer></script>
            <!-- Flatpickr JS -->
            <script src="https://cdn.jsdelivr.net/npm/flatpickr" defer></script>

            <script>
                $(document).ready(function() {
                    // --- Price Calculation and Date Picker ---
                    const dateInput = document.querySelector('input[name="dates_calendar"]');
                    if (dateInput) {
                        const tourDays = parseInt('{{ $row->days }}') || 1;
                        let isSettingRange = false;
                        const adultsInput = document.querySelector('input[name="adults"]');
                        const childrenInput = document.querySelector('input[name="children"]');
                        const priceDisplay = document.querySelector('.total-price-amount');
                        const adultPrice = parseFloat('{{ price_without_currency($row) }}') || 0;
                        const childPrice = parseFloat('{{ price_without_currency($row, true) }}') || 0;
                        const currencySymbol = "{{ $row->currency->symbol ?? '$' }}";

                        function updateTotalPrice() {
                            const adults = parseInt(adultsInput.value) || 0;
                            const children = parseInt(childrenInput.value) || 0;
                            const totalPrice = (adults * adultPrice) + (children * childPrice);
                            if (totalPrice > 0) {
                                priceDisplay.textContent = currencySymbol + ' ' + totalPrice.toLocaleString();
                            } else {
                                priceDisplay.textContent = currencySymbol + ' ' + adultPrice.toLocaleString();
                            }
                        }

                        const qtyContainers = document.querySelectorAll('.qtyButtons');
                        qtyContainers.forEach(container => {
                            container.addEventListener('click', function() {
                                setTimeout(updateTotalPrice, 50);
                            });
                        });

                        if (adultsInput) adultsInput.addEventListener('input', updateTotalPrice);
                        if (childrenInput) childrenInput.addEventListener('input', updateTotalPrice);

                        const bookingForm = document.getElementById('booking-form');
                        if (bookingForm) {
                            bookingForm.addEventListener('submit', function(event) {
                                if (!dateInput.value) {
                                    event.preventDefault();
                                    toastr.error('Please select a date before booking.');
                                }
                            });
                        }

                        flatpickr(dateInput, {
                            mode: 'range',
                            minDate: 'today',
                            dateFormat: 'Y-m-d',
                            altInput: true,
                            altFormat: 'Y-m-d',
                            onValueUpdate: function(selectedDates, dateStr, instance) {
                                if (selectedDates.length === 2) {
                                    const startDate = instance.formatDate(selectedDates[0], 'Y-m-d');
                                    const endDate = instance.formatDate(selectedDates[1], 'Y-m-d');
                                    document.getElementById('dates_hidden').value = startDate + 'to' + endDate;
                                }
                            },
                            showMonths: 2,
                            onChange: function(selectedDates, dateStr, instance) {
                                if (selectedDates.length === 1 && !isSettingRange) {
                                    isSettingRange = true;
                                    const startDate = selectedDates[0];
                                    const endDate = new Date(startDate.getTime());
                                    endDate.setDate(startDate.getDate() + tourDays - 1);
                                    instance.setDate([startDate, endDate], true);
                                    setTimeout(() => {
                                        isSettingRange = false;
                                    }, 100);
                                }
                            }
                        });
                    }

                    // --- Countdown Timer ---
                    const offerCountdown = document.getElementById("offerCountdown");
                    if (offerCountdown) {
                        const countdownDate = new Date();
                        countdownDate.setDate(countdownDate.getDate() + 7);
                        const countdownTimer = setInterval(function() {
                            const now = new Date().getTime();
                            const distance = countdownDate - now;
                            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                            document.getElementById("days").innerHTML = days < 10 ? "0" + days : days;
                            document.getElementById("hours").innerHTML = hours < 10 ? "0" + hours : hours;
                            document.getElementById("minutes").innerHTML = minutes < 10 ? "0" + minutes : minutes;
                            document.getElementById("seconds").innerHTML = seconds < 10 ? "0" + seconds : seconds;

                            if (distance < 0) {
                                clearInterval(countdownTimer);
                                offerCountdown.innerHTML = "EXPIRED";
                            }
                        }, 1000);
                    }

                    // --- Fancybox ---
                    $(window).on('load', function() {
                        Fancybox.bind('[data-fancybox="gallery"]', {
                            Video: {
                                autoplay: true,
                                ratio: 16 / 9
                            },
                        });
                    });

                    // --- Smooth Scrolling ---
                    $(".navigation ul li .nav-link, .itinerary_nav_dropdownmenu ul li .nav-link").on("click", function(e) {
                        e.preventDefault();
                        const targetSection = $(this).attr("href");
                        $("html, body").animate({
                            scrollTop: $(targetSection).offset().top - 70
                        }, 300);
                        if ($(this).closest('.itinerary_nav_dropdownmenu').length) {
                            $("#tabdropdown-menu").removeClass('show-dropdown');
                            $('#toggle-icon').attr('icon', 'iconamoon:arrow-down-2');
                        }
                    });

                    // --- Sticky Navigation and Active Section Highlighting ---
                    const tourTabs = document.getElementById("tourTabWrapper");
                    const tourDetailWrapper = document.querySelector(".tourdetails__wrapper--main");
                    const navLinks = document.querySelectorAll(".navigation a");
                    const sections = document.querySelectorAll("section[id]");
                    const dropdownheader = document.querySelector('.itinerary-nav-headerwrapper');
                    const dropdownMenu = document.getElementById('tabdropdown-menu');
                    const icon = document.getElementById('toggle-icon');
                    const navLinksSm = document.querySelectorAll(".itinerary_nav_dropdownmenu a");
                    const headerHeight = 80;

                    if (tourDetailWrapper) {
                        window.addEventListener("scroll", () => {
                            const scrollY = window.scrollY + headerHeight;
                            const tourDetailOffsetTop = tourDetailWrapper.offsetTop - 20;
                            const tourDetailOffsetBottom = tourDetailWrapper.offsetTop + tourDetailWrapper
                                .offsetHeight - 20;

                            if (scrollY >= tourDetailOffsetTop && scrollY < tourDetailOffsetBottom) {
                                tourTabs.classList.add("sticky-background");
                            } else {
                                tourTabs.classList.remove("sticky-background");
                            }

                            let activeSet = false;
                            sections.forEach((section) => {
                                const rect = section.getBoundingClientRect();
                                const sectionTop = scrollY + rect.top - headerHeight;
                                const sectionBottom = sectionTop + rect.height;
                                const sectionId = section.getAttribute("id");
                                const correspondingLink = document.querySelector(
                                    `.navigation a[href="#${sectionId}"]`);
                                const currentLinkSm = document.querySelector(
                                    `.itinerary_nav_dropdownmenu a[href="#${sectionId}"]`);

                                if (!activeSet && scrollY >= sectionTop && scrollY < sectionBottom) {
                                    navLinks.forEach((link) => link.classList.remove("active"));
                                    navLinksSm.forEach((link) => link.classList.remove("active"));
                                    if (correspondingLink) correspondingLink.classList.add("active");
                                    if (currentLinkSm) {
                                        currentLinkSm.classList.add("active");
                                        $('#navTitleWrap').text(currentLinkSm.innerText);
                                    }
                                    activeSet = true;
                                }
                            });

                            if (!activeSet) {
                                navLinks.forEach((link) => link.classList.remove("active"));
                                navLinksSm.forEach((link) => link.classList.remove("active"));
                            }
                        });
                    }

                    if (dropdownheader) {
                        dropdownheader.addEventListener('click', () => {
                            dropdownMenu.classList.toggle('show-dropdown');
                            icon.icon = dropdownMenu.classList.contains('show-dropdown') ? 'iconamoon:arrow-up-2' :
                                'iconamoon:arrow-down-2';
                        });
                    }

                    // --- Itinerary Modal ---
                    $('.itinerary_activities--btn').on('click', function() {
                        var name = $(this).data('name');
                        var description = $(this).data('description');
                        var image = $(this).data('image');
                        $('.trip-title').html(name);
                        $('.activies_description').html(description);
                        $('.image-itineray-show').attr('src', image).attr('alt', name);
                        $('#itineraryactivities__modal').show();
                    });

                    // --- Toastr Notifications ---
                    @if (session('success'))
                        toastr.success("{{ session('success') }}");
                    @endif

                    @if (session('error'))
                        toastr.error("{{ session('error') }}");
                    @endif
                });
            </script>
        @endpush
