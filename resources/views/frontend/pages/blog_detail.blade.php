@extends('frontend.layouts.master')

@push('css')
    <link href="{{ asset('assets/css/blog.css') }}" rel="stylesheet">

    <style>
        .hero_in.general .wrapper {
            background-color: transparent !important;
        }

        .hero_in .wrapper h1 {
            background-color: rgb(0 0 0 / 7%);
            text-shadow: 6px 6px 10px rgb(0 0 0 / 65%);
        }
    </style>
@endpush

@push('title', $row->meta_title ?? $row->blog_title)

@push('seo')
    <meta name="description" content="{{ $row->meta_desc }}" />
    <meta name="keywords" content="{{ $row->meta_keyword }}" />
    <meta name="og:title" content="{{ $row->meta_title }}" />
    <meta name="og:description" content="{{ $row->meta_desc }}" />
    @if ($row->image)
        <meta name="og:image" content="{{ get_image_full_url($row->image) }}" />
    @endif
    <meta name="og:url" content="{{ route('blog.detail', $row->slug) }}" />
    <meta name="og:type" content="article" />
    <meta name="og:site_name" content="{{ config('app.name') }}" />
    <link rel="canonical" href="{{ route('blog.detail', $row->slug) }}" />

@endpush

@section('content')
    <main>
        <section class="hero_in general"
            style="background: url('{{ get_image_full_url($row->image) }}') center center no-repeat; background-size: cover;">
            <div class="wrapper">
                <div class="container">
                    <h1 class="fadeInUp"><span></span>{{ $row->blog_title }}</h1>
                </div>
            </div>
        </section>
        <!--/hero_in-->

        <div class="container margin_60_35 blog_detail">
            <div class="row">
                <div class="col-lg-9">
                    <div class="bloglist singlepost">
                        <p>
                            <img alt="Blog Image" class="img-fluid" src="img/blog-single.jpg">
                        </p>
                        <h1>{{ $row->blog_title }}</h1>
                        <div class="postmeta">
                            <ul>
                                @if ($row->category?->name)
                                    <li>
                                        <a href="#"><i class="icon_folder-alt"></i> {{ $row->category?->name }}</a>
                                    </li>z`
                                @endif
                                <li>
                                    <a href="#"><i class="icon_clock_alt"></i>
                                        {{ format_date($row->created_at) }}</a>
                                </li>
                            </ul>
                        </div>
                        <!-- /post meta -->
                        <div class="post-content">
                            <div class="dropcaps">
                                {!! $row->blog_desc !!}
                            </div>

                        </div>
                        <!-- /post -->
                    </div>
                    <!-- /single-post -->
                </div>
                <!-- /col -->

                <aside class="col-lg-3"> <!-- /widget -->
                    @include('frontend.pages.blog_sidebar')
                    <!-- /widget -->
                </aside>
                <!-- /aside -->
            </div>
            <!-- /row -->
        </div>
        <!-- /container -->
    </main>
@endsection
