@extends('frontend.layouts.master')
@push('css')
    <link href="{{ asset('assets/css/blog.css') }}" rel="stylesheet">
@endpush

@push('title', 'Update Profile')

@section('content')
    <main>

        <div class="container container-custom dashboard-section margin_60_35">
            <div class="row">
                @include('frontend.customer.user-dashboard', ['user' => $user])

                <div class="col-lg-8">
                    @include('common.alert')
                    <div class="contact_wraper">
                        <div class="contact-info">
                            <h4 class="title">Update Profile</h4>
                            <p class="desc">Change your profile information</p>
                            <div class="contact_form">
                                {{ Form::model($user, ['route' => 'customer.update', 'method' => 'patch']) }}
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input_group">
                                            <label for="full_name" class="form-label">Full Name <span
                                                    class="text-red-800">*</span></label>

                                            {{ Form::text('full_name', null, ['class' => 'form-control form-control--sm br-4' . ($errors->has('full_name') ? ' is-invalid' : ''), 'id' => 'full_name', 'placeholder' => 'Your full name...', 'required']) }}
                                            @error('full_name')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="email" class="form-label">Email <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::email('email', null, ['class' => 'form-control form-control--sm br-4' . ($errors->has('email') ? ' is-invalid' : ''), 'id' => 'email', 'placeholder' => 'Your email...', 'required']) }}
                                            @error('email')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="input_group">
                                            <label for="phone_number" class="form-label">Phone Number <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::text('phone_number', null, ['class' => 'form-control form-control--sm br-4' . ($errors->has('phone_number') ? ' is-invalid' : ''), 'id' => 'phone_number', 'required', 'data-intl-tel-input' => '']) }}
                                            @error('phone_number')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="input_group">
                                            <label for="Address" class="form-label">Address <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::textarea('address', null, ['class' => 'form-control form-control--sm br-4' . ($errors->has('address') ? ' is-invalid' : ''), 'id' => 'address', 'placeholder' => 'Your address...', 'required']) }}
                                            @error('address')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <button class="update-btn" type="submit">
                                    Update
                                </button>
                                {{ Form::close() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>
@endsection

@include('common.int-tel-input')
