@extends('frontend.layouts.master')

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="h3 mb-3">{{ __('Reset Password') }}</h2>
                            <p class="text-muted">
                                {{ __('Enter your email address and we\'ll send you a link to reset your password.') }}
                            </p>
                        </div>

                        @if (session('status'))
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('status') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('password.email') }}" class="mt-4">
                            @csrf

                            <div class="mb-4">
                                <label for="email" class="form-label">{{ __('Email Address') }}</label>
                                <div class="input-group">
                                    <input id="email" type="email"
                                        class="form-control form-control-lg @error('email') is-invalid @enderror"
                                        name="email" value="{{ old('email') }}" placeholder="<EMAIL>" required
                                        autocomplete="email" autofocus>
                                </div>
                                @error('email')
                                    <div class="invalid-feedback d-block">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    {{ __('Send Reset Link') }}
                                </button>
                            </div>

                            <div class="text-center mt-4">
                                <a href="{{ route('login') }}" class="text-decoration-none">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    {{ __('Back to Login') }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            body {
                background-color: #f8f9fa;
            }

            .card {
                border-radius: 12px;
                border: none;
            }

            .form-control {
                border-radius: 8px;
                padding: 12px 15px;
            }

            .form-control:focus {
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
                border-color: #86b7fe;
            }

            .btn-primary {
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 500;
            }

            .input-group-text {
                background-color: #f8f9fa;
                border-right: none;
            }

            .form-control {
                border-left: none;
            }

            .form-control:focus {
                border-color: #ced4da;
            }
        </style>
    @endpush
@endsection
