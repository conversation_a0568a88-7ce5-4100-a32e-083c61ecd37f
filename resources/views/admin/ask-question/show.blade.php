@extends('admin.layouts.master')

@push('css')
    <style>
        .ibox-content-custom {
            padding: 25px 20px 15px 20px;
        }
    </style>
@endpush


@section('title', 'Ask Quastion Details')

@section('content')

    @include('admin.common.breadcrumbs', [
        'title' => 'Show',
        'panel' => 'Ask-Question',
    ])

    <div class="wrapper wrapper-content animated fadeInRight">
        @include('admin.common.success_error_message')

        <div class="row">
            <div class="col-lg-7">
                <div class="ibox ">
                    <div class="ibox-title">
                        <h3>Question Details</h3>
                    </div>
                    <div class="ibox-content ibox-content-custom">
                        <table class="table table-striped table-bordered table-hover">
                            <tbody>
                                <tr>
                                    <th style="width:35%;">Tour</th>
                                    <td style="width:65%">
                                        <a
                                            href="{{ route('admin.tour.show', $data['row']?->tour_id) }}">{{ $data['row']?->tour?->name }}</a>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Full Name</th>
                                    <td style="width:65%">{{ $data['row']->name }}</td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Status</th>
                                    <td style="width:65%">
                                        @switch($data['row']->status)
                                            @case('pending')
                                                <span class="label label-warning">Pending</span>
                                            @break

                                            @case('completed')
                                                <span class="label label-success">Completed</span>
                                            @break

                                            @case('cancel')
                                                <span class="label label-danger">Cancel</span>
                                            @break

                                            @default
                                                <span class="label label-danger">{{ ucwords($data['row']->status) }}</span>
                                        @endswitch

                                        @if ($data['row']->status == 'pending')
                                            <div class="btn-group pull-right">
                                                <button data-toggle="dropdown"
                                                    class="btn btn-sm btn-default dropdown-toggle">
                                                    Action
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a href="javascript:void(0)" id="completeInquiry"
                                                            class="dropdown-item inquiry-status" data-type="completed"
                                                            title="Complete">
                                                            <i class="fa fa-check-circle"></i> Complete
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0)" id="cancelInquiry"
                                                            class="dropdown-item inquiry-status" data-type="cancel"
                                                            title="Cancel"> <i class="fa fa-times"></i> Cancel</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        @endif

                                    </td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Email</th>
                                    <td style="width:65%">
                                        {{ $data['row']->email }}
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Phone</th>
                                    <td style="width:65%">
                                        {{ $data['row']->phone }}
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Message</th>
                                    <td style="width:65%">
                                        {{ $data['row']->message }}
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width:35%;">Inquiry Date</th>
                                    <td style="width:65%">{{ format_date_time($data['row']->created_at) }}</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js')
    <script>
        $(document).ready(function() {
            $('.inquiry-status').on('click', function(e) {
                var $this = $(this);
                var status = $this.attr('data-type');
                swal({
                    title: status === 'completed' ? 'Do you want to complete this inquiry ?' :
                        'Do you want to cancel this inquiry ?',
                    text: status === 'completed' ? 'This inquiry will be completed!' :
                        "You won't be able to revert this!",
                    type: 'error',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: status === 'completed' ? 'Yes, complete it!' :
                        'Yes, cancel it!',
                    html: false,
                    closeOnConfirm: false
                }, function(isConfirm) {
                    if (isConfirm) {
                        swal({
                            title: '',
                        })
                        let data = {
                            _token: '{{ csrf_token() }}',
                            status: status
                        }
                        let route = '{{ route('admin.ask-question.update', $data['row']->id) }}'
                        $.post(route, data)
                            .done(function(response) {
                                toastr.info('Item is Updated')
                                window.location.reload()
                            })
                            .fail(function(response) {
                                toastr.error(response.responseJSON.message)
                            })
                            .always(function() {
                                swal.close()
                            })
                    }
                })
            });
            var message = '{{ session()->get('message') }}';
            if (message) {
                toastr.info(message)
            }
            // toastr.info('Item is Updated')
        })
    </script>
@endpush
