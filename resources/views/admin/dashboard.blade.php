@extends('admin.layouts.master')

@push('css')
    <link rel="stylesheet" href="{{ asset('dist/css/select2.min.css') }}">
    <link href="{{ asset('dist/css/switchery.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
    <style>
        .h4-custom-label {
            padding-right: -12px;
            text-align: right;
            margin-right: 0px !important;
        }

        .setting-cog {
            position: absolute;
            top: -6px;
            right: 30px;
        }

        .date-setting {
            width: 60vh;
        }

        .select2-container--open {
            z-index: 9999999
        }
    </style>
    <link rel="stylesheet" href="{{ asset('dist/css/statistics.css') }}">
@endpush

@section('title', 'Dashboard')

@section('content')
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-6">
            <h2>Dashboard</h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ route('admin.dashboard.index') }}">Dashboard</a>
                </li>
            </ol>
        </div>
    </div>

    <div class="wrapper wrapper-content animated fadeInRight dashboard-manager">
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-lg-3 col-md-6">
                <div class="ibox ">
                    <div class="ibox-title">
                        <span class="label label-success float-right">{{ now()->format('Y-m-d') }}</span>
                        <h5>Total Users</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">{{ $total_users }}</h1>
                        <small>Total registered users</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox ">
                    <div class="ibox-title">
                        <span class="label label-info float-right">{{ now()->subDays(30)->format('Y-m-d') }} - {{ now()->format('Y-m-d') }}</span>
                        <h5>Active Users</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">{{ $active_users }}</h1>
                        <small>Active users in last 30 days</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox ">
                    <div class="ibox-title">
                        <span class="label label-primary float-right">{{ now()->subDays(30)->format('Y-m-d') }} - {{ now()->format('Y-m-d') }}</span>
                        <h5>New Registrations</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">{{ $recent_users }}</h1>
                        <small>New users in last 30 days</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox ">
                    <div class="ibox-title">
                        <span class="label label-danger float-right">{{ now()->format('Y-m-d H:i:s') }}</span>
                        <h5>System Status</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">Online</h1>
                        <small>Current system status</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>User Registration Statistics</h5>
                        <div class="ibox-tools">
                            <form action="{{ route('admin.dashboard.index') }}" method="GET"
                                class="d-flex align-items-center">
                                <select name="year" class="form-control" onchange="this.form.submit()">
                                    @foreach ($years as $year)
                                        <option value="{{ $year }}"
                                            {{ $selected_year == $year ? 'selected' : '' }}>
                                            {{ $year }}
                                        </option>
                                    @endforeach
                                </select>
                            </form>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="chart-container" style="height: 400px;">
                                    <canvas id="userRegistrationChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Bookings -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Latest Bookings</h5>
                        <div class="ibox-tools">
                            <a href="{{ route('admin.booking.index') }}">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content" style="padding-top: 20px;">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Tour Name</th>
                                    <th>User Name</th>
                                    <th>Booking Date</th>
                                    <th>Booking Status</th>
                                    <th>Booking Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($latest_bookings as $booking)
                                    <tr>
                                        <td>
                                            <a href="{{ route('admin.booking.show', $booking->id) }}">
                                                {{ $booking->id }}
                                            </a>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.tour.show', $booking->tour->id) }}">
                                                {{ $booking->tour->name }}
                                            </a>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.user.show', $booking->user->id) }}">
                                                {{ $booking->user->full_name }}
                                            </a>
                                        </td>
                                        <td>{{ $booking->created_at }}</td>
                                        <td>{{ $booking->order_status }}</td>
                                        <td>{{ $booking->total_price }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Initialize chart
        let userRegistrationChart;

        // Prepare data from PHP
        const registrationData = @json($registration_data);

        // Get selected year from PHP
        const selectedYear = {{ $selected_year }};

        // Initialize chart with selected year data
        initializeChart();

        function initializeChart() {
            if (userRegistrationChart) {
                userRegistrationChart.destroy();
            }

            const ctx = document.getElementById('userRegistrationChart').getContext('2d');

            if (!ctx) {
                return;
            }

            // Prepare labels and data
            const labels = registrationData.map(item => item.month);
            const data = registrationData.map(item => item.registrations);

            userRegistrationChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `Registrations (${selectedYear})`,
                        data: data,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 10
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: `Monthly User Registrations (${selectedYear})`
                        }
                    }
                }
            });
        }

        // Initialize chart when page loads
        document.addEventListener('DOMContentLoaded', initializeChart);
    </script>
@endpush
