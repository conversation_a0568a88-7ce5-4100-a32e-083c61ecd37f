@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/css/list.css') }}" rel="stylesheet">
    <style>
        .select2-container{
            z-index: 2051;
        }
        #exampleModal .modal-dialog{
            overflow-y: initial !important
        }
        #exampleModal .modal-body{
            height: 80vh;
            overflow-y: auto;
        }
        .dropleft:hover .dropdown-menu {
            display: block;
            margin-top: 0;
        }
    </style>
@endpush

@section('title', 'User List')

@section('content')
    @include('admin.common.breadcrumbs', [
        'title'=> 'List',
        'panel'=> 'user',
    ])
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            @summary
            @slot('title') User @endslot
            @forelse($data['status'] as $key => $value)
                @include('admin.common.count',compact('key','value'))
            @empty
            @endforelse
            @endsummary
        </div>

        @component('admin.common.advanced-filter')
            @include('admin.user.partials.advanced-filter-form')
        @endcomponent

        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title-border">
                        <h5>User List</h5>
                    </div>
                    <div class="ibox-content ibox-content-custom">
                        @include('admin.user.partials.table')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assign Roles -->
    <div class="modal fade" id="syncRoles" tabindex="-1" role="dialog" aria-labelledby="syncRoles" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Change User Roles</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="rolesForm">
                        @csrf
                        <div class="form-group">
                            <label for="select_roles">Select User Roles for <span id="user_name"></span></label><br>
                            <select name="roles[]" id="select_roles" multiple="multiple" class="form-control js-states select2_roles" style="width: 100%">
                                @foreach($data['roles'] as $id => $name)
                                    <option value="{{ $id }}"> {{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <input type="hidden" name="user_id" id="user_id">
                    </form>
                    {{-- <button class="brn btn-success btn-sm" id="showHistoryButton" title="Show History" data-toggle="modal" data-target="#exampleModal" data-type="{{ \Foundation\Models\User::class }}">History</button> --}}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary save-roles-btn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Role Update History</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="code_history_div"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('dist/js/bulk-action-manager.min.js') }}"></script>

    <script>
        DataTableOptions = {
            defaultPagination: {{ \Foundation\Lib\Meta::get('default_system_pagination', 25) }},
            export: {
                columns: [1, 2, 3, 4,],
                title: 'Application : User Data Print'
            },
            ajax: {
                url: '{{ route('admin.user.index') }}',
                dataType: 'json',
                type: 'GET',
                data: function (data) {
                    data._token = '{{ csrf_token() }}'
                    data.filter = {
                        name: $('input[name=name]').val(),
                        role: $('select[name=role]').val(),
                        creation: {
                            start: $('input[name=creation_start]').val(),
                            end: $('input[name=creation_end]').val(),
                        },
                        kyc_verification: $('select[name=kyc_verification]').val(),
                        soft_delete: $('select[name=softdelete]').val(),
                        is_blacklisted: $('select[name=is_blacklisted]').val(),
                    }
                }
            },
            columns: [
                {data: 'checkbox', orderable: false},
                {data: 'full_name', orderable: true},
                {data: 'image', orderable: true},
                {data: 'roles', orderable: true},
                {data: 'email', orderable: true},
                {data: 'created_at', orderable: true},
                {data: 'action', orderable: false},
            ],
            filters: function () {
                this.api().columns([1]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([2]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([3]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([4]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
            },
            collectionsButtons: [
                {
                    text: '<i class="fa fa-user-circle-o"></i> Verify the selected users',
                    className: 'buttons-delete',
                    action: function (e, dt, node, config) {
                        BulkActionManager.init(
                            router.get('admin.actions.verify-users'),
                            'select'
                        );
                    }
                },
                {
                    text: '<i class="fa fa-user-circle-o"></i> Unblock the selected users',
                    className: 'buttons-delete',
                    action: function (e, dt, node, config) {
                        BulkActionManager.init(
                            router.get('admin.actions.unblock-users'),
                            'unblock'
                        );
                    }
                },
                {
                    text: '<i class="fa fa-check-circle"></i> Remove from Blacklist',
                    className: 'buttons-delete',
                    action: function () {
                        BulkActionManager.init(
                            router.get('admin.users.remove-blacklist'),
                            'remove from blacklist'
                        );
                    }
                },
            ]
        };
    </script>

    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    <script src="{{ asset('dist/js/list.js') }}"></script>
    @include('admin.common.summary-script', ['table' => 'users'])
    @include('admin.user.partials.index-scripts')
@endpush
