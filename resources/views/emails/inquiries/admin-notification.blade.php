<x-mail::message>
# ✈️ New Travel Inquiry Received

Hey Admin,

You’ve got a new travel inquiry from **{{ $inquiry->full_name }}**. Here's the full scoop:

---

## 👤 Personal Information

**Full Name:** {{ $inquiry->full_name }}<br>
**Email:** <a href="mailto:{{ $inquiry->email }}">{{ $inquiry->email }}</a><br>
**Phone:** <a href="tel:{{ $inquiry->phone_no }}">{{ $inquiry->phone_no }}</a><br>
**Country:** {{ $inquiry->country ?? 'Not specified' }}

---

## 🗺️ Travel Details

**Travel Date:** {{ $inquiry->travel_date ? $inquiry->travel_date->format('F j, Y') : 'Not specified' }}
**Duration:** {{ $inquiry->duration ? $inquiry->duration . ' days' : 'Not specified' }}
**Travelers:** {{ $inquiry->adults }} adults, {{ $inquiry->children }} children
**Tour Type:** {{ $inquiry->tour_type ?? 'Not specified' }}
**Budget Range:** {{ $inquiry->budget ?? 'Not specified' }}

---

## 💬 Message

{{ $inquiry->query }}

---

## 📌 Additional Info

**Source:** {{ $inquiry->source ?? 'Not specified' }}
**Submitted On:** {{ $inquiry->created_at->format('F j, Y, g:i a') }}

---

<x-mail::button :url="route('admin.inquiry.show', $inquiry->id)">
View Inquiry Details
</x-mail::button>

Thanks,
**{{ config('app.name') }}**

<x-slot:footer>
If you’ve got any questions, hit us up at [{{ get_site_config_value('email') }}](mailto:{{ get_site_config_value('email') }}).
</x-slot:footer>
</x-mail::message>
