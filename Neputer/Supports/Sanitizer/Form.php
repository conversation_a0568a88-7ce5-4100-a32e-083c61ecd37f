<?php

namespace Neputer\Supports\Sanitizer;

use Neputer\Supports\Sanitizer\Filters\{
    Trim, Digit, Uppercase, Lowercase, StripTags, Capitalize, EscapeHtml
};

/**
 * Class Form
 * @package Neputer\Supports\Sanitizer
 */
abstract class Form
{

    /**
     * Filter keyword's and their class path
     *
     * @var string[]
     */
    protected $filters = [
        'capitalize'  => Capitalize::class,
        'digit'       => Digit::class,
        'escape_html' => EscapeHtml::class,
        'lowercase'   => Lowercase::class,
        'strip_tags'  => StripTags::class,
        'trim'        => Trim::class,
        'uppercase'   => Uppercase::class,
    ];

    protected function applyFilter(string $keyword)
    {

    }

}
