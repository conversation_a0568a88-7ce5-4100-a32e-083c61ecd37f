<?php

namespace Neputer\Supports\Sanitizer\Filters;

use Neputer\Supports\Sanitizer\Contracts\Sanitizer;

/**
 * Class Capitalize
 *
 * @package Neputer\Supports\Sanitizer\Filters
 */
final class Capitalize implements Sanitizer
{

    /**
     * Capitalize the given string.
     *
     * @param string $value
     * @param array $options
     * @return string
     */
    public function apply($value, $options = [])
    {
        return is_string($value) ? mb_convert_case(mb_strtolower($value, 'UTF-8'),  MB_CASE_TITLE) : $value;
    }

}
