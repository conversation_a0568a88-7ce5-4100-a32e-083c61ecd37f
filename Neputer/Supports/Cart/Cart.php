<?php

namespace Neputer\Supports\Cart;

use Foundation\Lib\ProductCartType;
use Illuminate\Support\Arr;

/**
 * Class Cart
 * @package Neputer\Supports\Cart
 */
final class Cart
{
    public static function add($id, $name = null, $type= null, $price = null, $original_price = null, $quantity = null, $attributes = array(), $cartKey = 'cart')
    {
        if (is_array($id)) {
            if (CartCollection::make($id)->isMultiArr()) {
                foreach ($id as $item) {
                    static::add(
                        $item['id'],
                        $item['name'],
                        $item['type'],
                        $item['price'],
                        $item['original_price'],
                        $item['quantity'],
                        $item['attributes'] ?? []
                    );
                }
            } else {
                static::add(
                    $id['id'],
                    $id['name'],
                    $id['type'],
                    $id['price'],
                    $id['original_price'],
                    $id['quantity'],
                    $id['attributes'] ?? []
                );
            }

        } else {
            app('session')
                ->put($cartKey,
                    static::get(null, 'id', $cartKey)
                        ->reject(function ( $value, $key) use ($id, $type) {
                return ($value->id == $id) && ($value->type == $type);
            }));

            $cartItems = static::get(null, 'id', $cartKey);
            $newCart = $cartItems->push([
                'id'             =>     $id,
                'name'           =>     $name,
                'type'           =>     $type,
                'price'          =>     $price,
                'original_price' =>     $original_price,
                'quantity'       =>     $quantity,
                'attributes'     =>     $attributes,
                'subtotal'       =>     $price * $quantity
            ]);

            app('session')->put( $cartKey , $newCart ?? [] );
        }
    }

    /**
     * Get Cart items with default parameters and specified cart key
     *
     * @param string $cartKey
     * @return mixed|CartCollection
     */
    public static function getDefault($cartKey = 'cart')
    {
        return self::get(null, 'id', $cartKey);
    }

    /**
     * @param string|null $value
     * @param string $key
     * @param string $cartKey
     * @return mixed
     */
    public static function get(string $value = null, string $key = 'id', $cartKey = 'cart') : CartCollection
    {
        $cart = CartCollection::make(app('session')->get($cartKey))
            ->map(function($row) {
                $newProduct = [];

                $productType = Arr::get($row, 'attributes.type');
                $productId   = Arr::get($row, 'id');
                $product = $productType;

                if ($product) {
                    $productPrice = $product->price ?? 0;
                    $productOrgPrice = $product->original_price ?? 0;

                    $newProduct = [
                        'price'          => $productPrice,
                        'original_price' => $productOrgPrice,
                        'subtotal'       => $productPrice * ($row['quantity'] ?? 0),
                        'attributes'     => array_merge(Arr::get($row, 'attributes'), [
                            'discount'   => [],
                        ])
                    ];
                }

                return (object) array_merge((array) $row, $newProduct);
            });
        return $value ? $cart->firstWhere($key, $value) : $cart;
    }

    public static function clear($cartKey = 'cart')
    {
        app('session')->forget($cartKey);
    }

    public static function remove(string $id, $type = ProductCartType::PRODUCT_TYPE, $cartKey = 'cart')
    {
        $cartItems = static::get(null, 'id', $cartKey);

        app('session')
            ->put($cartKey, $cartItems->reject(
                function ($value, $key) use ($type, $id) {
                    return ($value->id == $id) && ($value->type == $type);
                }));
    }

    public static function total($cartKey = 'cart')
    {
        return static::get(null, 'id', $cartKey)->sum('subtotal');
    }

    public static function quantity($cartKey = 'cart')
    {
        return static::get(null, 'id', $cartKey)->count();
    }

    public static function update($data, $cartKey = 'cart')
    {
        $collection = [];
        $cartItems = static::get(null, 'id', $cartKey);

        $newItem = array_merge($data, [ 'subtotal'  => $data['price'] * $data['quantity']]);

        foreach ($cartItems->toArray() as $cart) {
            if  (Arr::get((array) $cart, 'id') === Arr::get($newItem, 'id')) {
                $collection[] = array_merge((array) $cart, $newItem);
            } else {
                $collection[] = (array) $cart;
            }
        }
        app('session')->put( $cartKey , $collection );

    }

    /**
     * Update Cart quantity by item's id
     *
     * @param $slug
     * @param int $type
     * @param $quantity
     * @param string $cartKey
     */
    public static function updateQuantity($slug, $type = ProductCartType::PRODUCT_TYPE, $quantity, $cartKey = 'cart', $productIncrement)
    {
        $cartItems = static::get(null, 'id', $cartKey);
        $item = $cartItems->where('id', $slug)->where('type', $type)->first();
        $item->quantity =  $productIncrement ? ($item->quantity + $quantity) : $quantity;
        $item->subtotal = $item->price * $item->quantity;
        app('session')->put( $cartKey, $cartItems);
        return $item;
    }
}
