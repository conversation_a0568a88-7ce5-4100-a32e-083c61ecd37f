@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
@endpush

@section('title', 'Create a {CLASS_NAME}')

@section('content')
    @include('admin.common.breadcrumbs', [
        'title'=> 'Create',
        'panel'=> '{LOWER_CLASS_NAME}',
    ])

    <div class="wrapper wrapper-content">
        {!! Form::open(['route' => 'admin.{LOWER_CLASS_NAME}.store', 'enctype' => 'multipart/form-data', 'method' => 'post', 'id' => '{LOWER_CLASS_NAME}Form']) !!}
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title ibox-title-border">
                        <h5>General Info</h5>
                    </div>
                    <div class="ibox-content ibox-content ibox-content-custom">
                        @include('admin.{LOWER_CLASS_NAME}.partials.form')
                    </div>
                </div>
            </div>
        </div>
        @includeIf('admin.common.action')
        {!! Form::close() !!}
    </div>
@endsection

@push('js')
    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    @include('admin.{LOWER_CLASS_NAME}.partials.scripts')
@endpush
