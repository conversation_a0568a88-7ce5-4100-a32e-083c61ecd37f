@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
@endpush

@section('title', '{CLASS_NAME} List')

@section('content')
    @include('admin.common.breadcrumbs', [
        'title'=> 'List',
        'panel'=> '{LOWER_CLASS_NAME}',
    ])
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title-border">
                        <h5>{CLASS_NAME} List</h5>
                    </div>
                    <div class="ibox-content ibox-content-custom">
                        @include('admin.{LOWER_CLASS_NAME}.partials.table')

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        var DataTableOptions = {
            defaultPagination: 10,
            export: {
                columns: [ 0, 1, 2, 3 ],
                title: 'Application : {CLASS_NAME} Print'
            },
            ajax: {
                url: '{{ route('admin.{LOWER_CLASS_NAME}.index') }}',
                dataType: 'json',
                type: 'GET',
                data: function (data) {
                    data._token = '{{ csrf_token() }}'
                }
            },
            columns: [
                { data: 'username' },
                { data: 'full_name', orderable: true },
                { data: 'email', orderable: true },
                { data: 'created_at', orderable: true },
                { data: 'action', orderable: false },
            ],
            order: [[ 0, 'desc' ]]
        };
    </script>
    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    <script src="{{ asset('dist/js/list.js') }}"></script>
@endpush
