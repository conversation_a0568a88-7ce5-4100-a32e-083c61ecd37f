@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
@endpush

@section('title', 'Edit a {CLASS_NAME}')

@section('content')

    @include('admin.common.breadcrumbs', [
        'title'=> 'Edit',
        'panel'=> '{LOWER_CLASS_NAME}',
        'id'=> $data['{LOWER_CLASS_NAME}']->id,
    ])

    <div class="wrapper wrapper-content">
        {!! Form::model($data['{LOWER_CLASS_NAME}'],['route' => ['admin.{LOWER_CLASS_NAME}.update',$data['{LOWER_CLASS_NAME}']->id], 'enctype' => 'multipart/form-data', 'method' => 'put', 'id' => '{LOWER_CLASS_NAME}Form']) !!}
        {!! Form::hidden('id', $data['{LOWER_CLASS_NAME}']->id) !!}
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title ibox-title-border">
                        <h5>General Info</h5>
                    </div>
                    <div class="ibox-content ibox-content ibox-content-custom">
                        @includeIf('admin.{LOWER_CLASS_NAME}.partials.form')
                    </div>
                </div>
            </div>

        </div>
        @includeIf('admin.common.action')
        {!! Form::close() !!}
    </div>

@endsection

@push('js')
    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    @include('admin.{LOWER_CLASS_NAME}.partials.scripts')
@endpush
