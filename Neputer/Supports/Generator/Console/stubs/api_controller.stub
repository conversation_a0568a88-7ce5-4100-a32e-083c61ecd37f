<?php

namespace App\Http\Controllers{API_CONTROLLER_NAMESPACE};

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Neputer\Supports\BaseController;
use Foundation\Services\{CLASS_NAME}Service;
use Foundation\Resources\{CLASS_NAME}Resource;
use Foundation\Requests\{CLASS_NAME}\StoreRequest;
use Foundation\Requests\{CLASS_NAME}\UpdateRequest;

/**
 * Class {CLASS_NAME}Controller
 *
 * @package App\Http\Controllers{API_CONTROLLER_NAMESPACE}
 */
class {CLASS_NAME}Controller extends BaseController
{

    /**
     * The {CLASS_NAME}Service instance
     *
     * @var {VAR_CLASS_NAME}Service
     */
    private {VAR_CLASS_NAME}Service;

    public function __construct({CLASS_NAME}Service {VAR_CLASS_NAME}Service)
    {
        $this->{SMALL_CASE_CLASS_NAME}Service = {VAR_CLASS_NAME}Service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     */
    public function index( Request $request )
    {
        return $this->responseOk(
            {CLASS_NAME}Resource::collection($this->{SMALL_CASE_CLASS_NAME}Service->get())
        );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        return $this->responseOk([
            //
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  StoreRequest  $request
     * @return Response
     */
    public function store(StoreRequest $request)
    {
        return $this->responseOk(
            new {CLASS_NAME}Resource($this->{SMALL_CASE_CLASS_NAME}Service->new($request->validated())),
            '{CLASS_NAME} is successfully created !'
        );
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id)
    {
        return $this->responseOk(
            new {CLASS_NAME}Resource($this->{SMALL_CASE_CLASS_NAME}Service->findOrFail($id))
        );
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        return $this->responseOk(
            new {CLASS_NAME}Resource($this->{SMALL_CASE_CLASS_NAME}Service->findOrFail($id))
        );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  UpdateRequest  $request
     * @param  int  $id
     * @return Response
     */
    public function update(UpdateRequest $request, $id)
    {
        return $this->responseOk(
            new {CLASS_NAME}Resource($this->{SMALL_CASE_CLASS_NAME}Service->update($request->validated(), $this->{SMALL_CASE_CLASS_NAME}Service->findOrFail($id))),
            '{CLASS_NAME} is successfully Updated !'
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {
        return $this->responseOk(
            $this->{SMALL_CASE_CLASS_NAME}Service->delete($this->{SMALL_CASE_CLASS_NAME}Service->findOrFail($id)),
            '{CLASS_NAME} is Successfully Deleted !'
        );
    }

}
