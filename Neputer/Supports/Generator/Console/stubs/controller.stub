<?php

namespace App\Http\Controllers{CONTROLLER_NAMESPACE};

use Exception;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Foundation\Models\{CLASS_NAME};
use Neputer\Supports\BaseController;
use Foundation\Requests\{CLASS_NAME}\{
    StoreRequest,
    UpdateRequest
};
use Illuminate\Http\RedirectResponse;
use Illuminate\Contracts\View\Factory;
use Foundation\Services\{CLASS_NAME}Service;

/**
 * Class {CLASS_NAME}Controller
 * @package App\Http\Controllers\Admin
 */
class {CLASS_NAME}Controller extends BaseController
{

    /**
     * The {CLASS_NAME}Service instance
     *
     * @var {VAR_CLASS_NAME}Service
     */
    private {VAR_CLASS_NAME}Service;

    public function __construct({CLASS_NAME}Service {VAR_CLASS_NAME}Service)
    {
        $this->{SMALL_CASE_CLASS_NAME}Service = {VAR_CLASS_NAME}Service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Factory|View
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->{SMALL_CASE_CLASS_NAME}Service->filter($request->input('search.value')))
                ->addColumn('created_at', function ($data) {
                    return $data->created_at . " <code>{$data->created_at->diffForHumans()}</code>";
                })
                ->addColumn('action', function ($data) {
                    $model = '{SMALL_CASE_CLASS_NAME}';
                    return view('admin.common.data-table-action', compact('data', 'model'))->render();
                })
                ->addColumn('status', function ($data) {
                     return view('admin.common.status', compact('data'))->render();
                })
                ->rawColumns([ 'action', 'created_at', ])
                ->make(true);
        }

        return view('admin.{LOWER_CLASS_NAME}.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Factory
     */
    public function create()
    {
        $data = [];
        //
        return view('admin.{LOWER_CLASS_NAME}.create', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  StoreRequest $request
     * @return RedirectResponse
     */
    public function store(StoreRequest $request)
    {
        $this->{SMALL_CASE_CLASS_NAME}Service->new($request->all());
        flash('success', 'Record successfully created.');
        return $this->redirect($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  {CLASS_NAME} {VAR_CLASS_NAME}
     * @return Factory
     */
    public function show({CLASS_NAME} {VAR_CLASS_NAME})
    {
        $data = [];
        $data['{LOWER_CLASS_NAME}'] = {VAR_CLASS_NAME};
        return view('admin.{LOWER_CLASS_NAME}.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  {CLASS_NAME} {VAR_CLASS_NAME}
     * @return Factory
     */
    public function edit({CLASS_NAME} {VAR_CLASS_NAME})
    {
        $data = [];
        $data['{LOWER_CLASS_NAME}']  = {VAR_CLASS_NAME};
        return view('admin.{LOWER_CLASS_NAME}.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  UpdateRequest  $request
     * @param  {CLASS_NAME} {VAR_CLASS_NAME}
     * @return RedirectResponse
     */
    public function update(UpdateRequest $request, {CLASS_NAME} {VAR_CLASS_NAME})
    {
        $this->{SMALL_CASE_CLASS_NAME}Service->update($request->all(), {VAR_CLASS_NAME});
        flash('success', 'Record successfully updated.');
        return $this->redirect($request);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  {CLASS_NAME} {VAR_CLASS_NAME}
     * @return RedirectResponse
     */
    public function destroy({CLASS_NAME} {VAR_CLASS_NAME})
    {
        $this->{SMALL_CASE_CLASS_NAME}Service->delete({VAR_CLASS_NAME});
        flash('success', '{CLASS_NAME} is deleted successfully !');
        return redirect()->back();
    }
}
