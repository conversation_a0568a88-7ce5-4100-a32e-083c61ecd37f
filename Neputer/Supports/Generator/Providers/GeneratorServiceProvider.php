<?php

namespace Neputer\Supports\Generator\Providers;

use Illuminate\Support\ServiceProvider;
use Neputer\Supports\Generator\Console\GenerateCommand;

/**
 * Class GeneratorServiceProvider
 *
 * @package Neputer\Supports\Generator\Providers
 */
class GeneratorServiceProvider extends ServiceProvider
{

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = true;

    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                GenerateCommand::class,
            ]);
        }

        $this->mergeConfigFrom(
            __DIR__.'/../config/generator.php', 'generator'
        );
    }

}
