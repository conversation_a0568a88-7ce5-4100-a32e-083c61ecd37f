<?php

namespace Neputer\Supports\Mixins;

use App\Foundation\Lib\Payment\PaymentType;

trait PaymentRequestForm
{
    protected function paymentRules()
    {
        if ($this->request->get('payment_type_id') != PaymentType::CASH) {
            $rules = [
                'payment_id' => 'required|integer|gt:' . PaymentType::CASH,
                'payment_remarks' => 'sometimes|nullable',
            ];
        } else {
            $rules = [
                'payment_id' => 'sometime|nullable|numeric|min:0|max:0',
                'payment_remarks' => 'sometimes|nullable',
            ];
        }

        return $rules;
    }

}
