<?php

namespace Neputer\Supports\Mixins;

use File;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

/**
 * Trait Image
 * @package Neputer\Supports\Mixins
 */
trait  Image
{

    /**
     * @param $image
     * @param $folder_name
     * @param null $existing_image
     * @return string
     * @throws FileNotFoundException
     */
    public function uploadImage($image, $folder_name, $existing_image = null): string
    {
        $image_name = md5($image . microtime()).'_'.$image->getClientOriginalName();
        $file_path = 'images'.DIRECTORY_SEPARATOR.$folder_name.DIRECTORY_SEPARATOR.$image_name;
        Storage::disk('public')->put($file_path, File::get($image));
        if($existing_image) {
            $this->__deleteFile('images'.DIRECTORY_SEPARATOR.$folder_name.DIRECTORY_SEPARATOR.$existing_image);
        }
        return $image_name;
    }



    /**
     * Creates different size thumbs for uploaded image
     *
     * @param $image
     * @param $imageName
     * @param $folder_name
     * @param null $image_name will have value if request_for is update
     */
    public function uploadImageThumbs($image, $imageName, $folder_name, $image_name = null)
    {
            $image_dimension = $this->image_dimensions;

            foreach ($image_dimension as $image_dimansion) {
                // open and resize an image file
                $img = \Intervention\Image\Facades\Image::make($image)->resize($image_dimansion['width'], $image_dimansion['height']);
                // save file as jpg with medium quality
                Storage::disk('public')->put('images'.DIRECTORY_SEPARATOR . $folder_name . DIRECTORY_SEPARATOR . $image_dimansion['width'] . '_' . $image_dimansion['height'] . '_' . $imageName, $img);
            }
    }

    public function deleteImage($folder_name, $image_name)
    {
        $this->__deleteFile('images'.DIRECTORY_SEPARATOR.$folder_name.DIRECTORY_SEPARATOR.$image_name);
    }

    public function __deleteFile($file)
    {
        if(Storage::disk('public')->has($file)) {
            Storage::disk('public')->delete($file);
            return true;
        }
        return false;
    }

//    public function __deleteFile($file)
//    {
//        if(file_exists(public_path().DIRECTORY_SEPARATOR.$file)) {
//            unlink(public_path().DIRECTORY_SEPARATOR.$file);
//            return true;
//        }
//        return false;
//    }

    /**
     * Get Random Number
     *
     * @return string
     */
    public function __getRandomNumbers()
    {
        return rand(5555, 9876).'_';
    }

    public function imageNameUrl($request,$column_name = 'image', $is_array = false)
    {
        if($is_array) {
            $list = [];
            foreach($request->input($column_name) as $url) {
                if(is_array($url)) {
                    $path = parse_url($url['image'], PHP_URL_PATH);

                    $list[] = [
                        'image' => str_replace('/storage', '', $path),
                        'link' => $url['link'],
                    'alt_text'=>$url['alt_text'] ?? null,
                    'caption'=>$url['caption'] ?? null
                ];} else {
                    $path = parse_url($url, PHP_URL_PATH);
                    $list[] = str_replace('/storage', '', $path);
                }
            }

            $request->merge([$column_name => $list]);
        } else {
            $url = $request->input($column_name);

            $path = parse_url($url, PHP_URL_PATH);

            $relativePath = str_replace('/storage', '', $path);

            $request->merge([$column_name => $relativePath]);

            return $request;
        }
    }
}
