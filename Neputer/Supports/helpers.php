<?php

use Foundation\Lib\Cart as CartLib;
use Foundation\Lib\paymentHistory\PaymentStatus;
use Foundation\Services\PaymentHistoryService;
use Neputer\Supports\Cart\Cart;

if (!function_exists('neputer_version')) :

    function neputer_version ()
    {
        return '1.2.12';
    }

endif;

if (!function_exists('flash')) :

    function flash($type, $message)
    {
        return app(\Neputer\Lib\Flash::class)->notify($type, $message);
    }

endif;

if (!function_exists('is_active')) :

    /**
     * Check if given route is active
     *
     * @return boolean
     */
    function is_active(string $route)
    {
        return request()->route()->getName() === $route;
    }

endif;

if (!function_exists('is_json')) :

    /**
     * @deprecated
     */
    function is_json($string)
    {
        return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }

endif;

if (!function_exists('toaster')) :

    function toaster()
    {
        if (session()->has('notify')) {
            $type = session()->get('notify.type');
            $response = session()->get('notify.response');
            $title = '';
            $options = json_encode(\Neputer\Supports\Utility::flashConfig());
            return "toastr.$type('$response', '$title', $options);";
        }
    }

endif;

if (!function_exists('format_date_time')) :

    function format_date_time($date, $format = 'M j, Y g:i A'): string
    {
        return $date instanceof \Carbon\Carbon ? $date->format($format) : \Carbon\Carbon::parse($date)->format($format);
    }

endif;
if (!function_exists('format_date')) :

    function format_date($date, $format = 'M j, Y'): string
    {
        return $date instanceof \Carbon\Carbon ? $date->format($format) : \Carbon\Carbon::parse($date)->format($format);
    }

endif;

if(!function_exists('generateRandomString')){
    function generateRandomString($length) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

if (! function_exists('theme_path') ) :

    /**
     * Get the path to the themes directory.
     *
     * @param  string  $path
     * @return string
     */
    function theme_path ($path = '') {
        return app()->resourcePath('themes'.($path ? DIRECTORY_SEPARATOR.$path : $path));
    }

endif;

if (! function_exists('module_path') ) :

    /**
     * Get the path to the modules directory.
     *
     * @param  string  $path
     * @return string
     */
    function module_path ($path = '') {
        return app()->basePath('Modules'.($path ? DIRECTORY_SEPARATOR.$path : $path));
    }

endif;

if (! function_exists('active_theme') ) :

    /**
     * Get the active theme
     *
     * @param  string  $path
     * @return string
     */
    function active_theme () {
        return \Neputer\Supports\Theme\Theme::active();
    }

endif;

if (! function_exists('theme_asset') ) :

    /**
     * Get the active theme asset path
     *
     * @param  string  $path
     * @return string
     */
    function theme_asset($path = '/') {
        return \Neputer\Supports\Theme\Theme::asset($path);
    }

endif;

if (! function_exists('_x') ) :

    /**
     * Translate the given message.
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return \Illuminate\Contracts\Translation\Translator|string|array|null
     */
    function _x($key = null, $replace = [], $locale = null) {
        return trans(\Neputer\Supports\Theme\Theme::active(). '::'. $key, $replace, $locale);
    }

endif;

if (! function_exists('write_log') ) :

    function write_log ($message, $channel = 'payment-status') {
        $logger = logger()->channel($channel)->getLogger();
        $logger->info($message);
    }

endif;

if (! function_exists('config_value_by_key') ) :

    function config_value_by_key ($key) {
      $row = \Foundation\Models\Setting::where('key', $key)->first();
      return $row ? $row->value : null;
    }

endif;
if (! function_exists('currency_type') ) :

    function currency_type ($amount) {
        $currency = session()->get('currency');
        if ($currency == 'USD') {
            $current_currency = '$ ';
        }else{
            $current_currency = '₹ ';
        }
        return $current_currency. number_format($amount, 2);
    }

endif;



if (! function_exists('usd_currency') ) :
    function usd_currency () {
        $currency = session()->get('currency');
        return $currency === 'USD';
    }

endif;



if (! function_exists('price_with_currency') ) :
    function price_with_currency ($tour, $isForChild = false) { 
        if($isForChild){
            return currency_type(usd_currency()  ? $tour->usd_child_price : $tour->child_price);
        }   
        return currency_type(usd_currency()  ? $tour->usd_adult_price : $tour->adult_price);
    }
endif;

if (! function_exists('price_without_currency') ) :
    function price_without_currency ($tour, $isForChild = false) { 
        if($isForChild){
            return usd_currency()  ? $tour->usd_child_price : $tour->child_price;
        }   
        return usd_currency()  ? $tour->usd_adult_price : $tour->adult_price;
    }
endif;


if (! function_exists('database_size') ) :

    function database_size() {
        $tableName = config('database.connections.mysql.database');
        $sqlQuery = "SELECT table_schema '$tableName', SUM( data_length + index_length) / 1024 / 1024 'db_size_in_mb' FROM information_schema.TABLES WHERE table_schema='$tableName' GROUP BY table_schema;";

        $result = app('db')->select($sqlQuery);

        return isset($result[0]->db_size_in_mb) ? $result[0]->db_size_in_mb : 0;
    }

endif;

if (! function_exists('log_file_size') ) :

    function log_file_size() {
        $size = 0;
        foreach (glob(storage_path('logs/*.log')) as $log) {
            $size += filesize($log);
        }

        if ($size >= 1073741824)
        {
            $size = number_format($size / 1073741824, 2) . ' GB';
        }
        elseif ($size >= 1048576)
        {
            $size = number_format($size / 1048576, 2) . ' MB';
        }
        elseif ($size >= 1024)
        {
            $size = number_format($size / 1024, 2) . ' KB';
        }
        elseif ($size > 1)
        {
            $size = $size . ' bytes';
        }
        elseif ($size == 1)
        {
            $size = $size . ' byte';
        }
        else
        {
            $size = '0 bytes';
        }

        return $size;
    }

endif;

if (! function_exists('checkProductInCart') ) :

function checkProductInCart($productId, $productType, $cartType = CartLib::CART_KEY){
    $addedInCart = false;
    $cartItemList = Cart::getDefault($cartType);

    if ($cartItemList->isNotEmpty()) {
        foreach ($cartItemList as $cartItem) {
            if ($cartItem->id == $productId && $cartItem->type == $productType) {
                $addedInCart = true;
                break;
            } else {
                $addedInCart = false;
            }
        }
    }
    return $addedInCart;
}
endif;

function nrp($num, $decimal = 2)
{
    $sign = '';
    if($num < 0){
        $sign = '-';
        $num = abs($num);
    }

    if($decimal == 0)
        $trail = '';
    else{
        if((int)$num == $num)
            $trail = '.00';
        else
            $trail = str_pad(substr(round($num, $decimal), (strpos($num, '.'))), 3, '0');
    }

    $num = (int)$num;
    if($num <= 999)
        return $sign . $num . $trail;

    $result = strrev(',' . substr($num, -3));

    $number = strrev(substr($num, 0, -3));
    $digits = str_split($number);

    foreach ($digits as $i => $digit){
        $result .= $digit;
        if ($i%2 !== 0)
            $result .= ',';
    }
    $result = strrev($result);
    return $sign . ltrim($result, ',') . $trail;
}

if(!function_exists('getMonth')) {
    function getMonth($month) {
        if($month === 1) {
            return "January";
        }
        elseif ($month === 2) {
            return "February";
        }
        elseif ($month === 3) {
            return "March";
        }
        elseif ($month === 4) {
            return "April";
        }
        elseif ($month === 5) {
            return "May";
        }
        elseif ($month === 6) {
            return "June";
        }
        elseif ($month === 7) {
            return "July";
        }
        elseif ($month === 8) {
            return "August";
        }
        elseif ($month === 9) {
            return "September";
        }
        elseif ($month === 10) {
            return "October";
        }
        elseif ($month === 11) {
            return "November";
        }
        elseif ($month === 12) {
            return "December";
        }
    }
}

if(!function_exists('checkSuperAdmin')) {

    function checkSuperAdmin(): bool
    {
        $roles = auth()->user()->roles->pluck('id', 'name');

        if(Arr::has($roles, 'Super Admin')) {
           return true;
        }
        else {
           return false;
        }
    }
}

if(!function_exists('createMultiplePayment')) {
    function createMultiplePayment($fields, $model, $metas = null) {
        foreach ($fields as $paymentMethod) {
            $paymentMethod['customer_id'] = auth()->user()->id;
            $paymentMethod['status'] =  PaymentStatus::WITHOUT_CUSTOMER;
            $paymentMethod['created_at'] = $metas['created_at'] ?? $model->created_at;
            if ($paymentMethod['amount'] > 0){
                app(PaymentHistoryService::class)->updateOrCreate($paymentMethod['amount'], $model, $paymentMethod, $paymentMethod);
            }
        }
    }
}
