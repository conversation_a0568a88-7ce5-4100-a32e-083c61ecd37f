<?php

namespace Neputer\Providers;

use Foundation\Lib\Role;
use Illuminate\Support\Facades\Route;
use Foundation\Middleware\Impersonate;
use Neputer\Config\App;

/**
 * Class RouteServiceProvider
 * @package Neputer\Providers
 */
class RouteServiceProvider extends \App\Providers\RouteServiceProvider
{

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        parent::mapApiRoutes();

        parent::mapWebRoutes();

        $this->mapAdminRoutes();

    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapAdminRoutes()
    {
        $accessMiddleware  = Role::getMiddlewareString();
        Route::middleware([ 'web', 'auth', Impersonate::class, $accessMiddleware,  ])
            ->namespace($this->namespace.'\Admin')
            ->prefix(App::getAdminRoutePrefix())
            ->as('admin.')
            ->group(base_path('routes/admin.php'));
    }

}
