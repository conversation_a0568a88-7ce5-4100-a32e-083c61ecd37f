# Instructions to be followed

````
Please make seeder for every model
````


# Features

````
  /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->alias(ExchangeRate::class, 'exchange-rate');
    }
````

# UPDATES

### utils.toast

In Toast previously, was only able to show `info` styles messages.
Now you can send the type styles as a parameter. And
By default it will always use `info` styles so 
that previously used toast does not get messed up!

Types Are :
- warning
- success
- error
- info

````
utils.toast('hello world', 'warning')
````
