:root {
    --light-primary: #fff3e7;
    --black-200: #270722;
    --primary--200: #c78010;
    --primary: #a28307;
    --gray--200: #4f4f4f;
    --secondary: #0054a6;
    --black--100: #121921;
    --black--300: #333333;
    --black-500: #555555;
    --black-800: #808080;
}

/* header css */
.main_title_3 span em {
    background-color: var(--primary);
}

.main_title_2 span em {
    background-color: var(--primary);
}

.header {
    transition: all 0.1s ease;
    padding: 16px 0;
}

.header.home-header {
    background: var(--light-primary);
}

.header.page-header {
    background: white;
}

.header.page-header .dropdown.currencyDropdown .dropdown-toggle {
    background-color: #f2f2f2;
}

.header.main-header {
    transition: all 0.1s ease;
    position: relative;
    z-index: 1041;
}

.header.main-header.show {
    background: transparent;
    transition: all 0.1s ease;
    z-index: 1042;
}

.header.main-header .main-header-navmenu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.header.main-header .main-header-navmenu ul#top_menu {
    float: none;
}

.header.main-header .main-header-navmenu .main-menu {
    position: static;
}

.header.main-header .main-header-navmenu .nav-menu-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header.main-header #logo {
    float: none;
}

.header.main-header .btn_mobile {
    display: none;
}

.header.main-header .main-menu>ul>li span>a {
    color: var(--black-200) !important;
    padding: 0 10px;
}

.header.main-header .logo_sticky {
    display: none;
}

.header.main-header .logo_normal {
    height: 56px;
    width: auto;
    object-fit: cover;
    padding: 8px 8px 8px 0;
}

.header-checking {
    transition: all 0.3s ease-in-out;
    /* border-bottom: 1px solid #ededed; */
    padding: 15px 20px;
    position: relative;
    left: 0;
    top: 0;
    width: 100%;
}

.header-checking .main-menu>ul>li span>a {
    color: black !important;
}

.logins-btn {
    padding: 10px 16px !important;
    background: var(--primary) !important;
    color: white !important;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logins-btn:hover {
    background: var(--secondary) !important;
    opacity: 1 !important;
}

.hamburger-inner,
.hamburger-inner::after,
.hamburger-inner::before {
    background-color: var(--black-200) !important;
    width: 24px;
    height: 3px;
}

.hamburger-inner::before {
    top: -7px;
}

.hamburger-inner::after {
    bottom: -7px;
}

#top_menus {
    margin: 0 !important;
    position: relative;
    /* z-index: 99; */
    z-index: 1;
}

.dropdown.currencyDropdown .btn:focus {
    box-shadow: none !important;
}

/* home banner css */
.bannerSwiper {
    background: var(--light-primary);
    padding: 64px 0 64px 0;
    /* margin-bottom: 60px; */
}

.bannerSwiper .bannerCard .bannerCard__leftcontent {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--title {
    font-size: 40px;
    font-weight: 700;
    line-height: 1.4;
    color: var(--black-200);
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--title .highlight-text {
    background: none;
    color: var(--primary);
    padding: 0;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--contentwrapper {
    display: flex;
    gap: 16px;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--contentwrapper .subtitle {
    font-size: 16px;
    color: var(--gray--200);
    margin-top: 10px;
    max-width: 420px;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--contentwrapper .arrow-icon img {
    width: 72px;
    height: 72px;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .navigation-btns--wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
    position: relative;
    justify-content: flex-start;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .navigation-btns--wrapper .button-prev {
    position: static;
    width: 30px;
    border-radius: 4px;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    margin-top: 0;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .navigation-btns--wrapper .button-prev::after {
    display: none;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .navigation-btns--wrapper .button-next {
    position: static;
    width: 30px;
    border-radius: 4px;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    margin-top: 0;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

.bannerSwiper .bannerCard .bannerCard__leftcontent .navigation-btns--wrapper .button-next::after {
    display: none;
}

.bannerSwiper .bannerCard .rightBannerMainContainer {
    position: relative;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent {
    position: relative;
    padding: 0 60px 0 30px;
    color: var(--light-primary);
}

.bannerSwiper .bannerCard .rightBannerMainContainer .dotimage {
    position: absolute;
    bottom: 0;
    right: 0;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
        ". . imagefour"
        ". imagetwo imagefour"
        "imageone imagetwo imagefive"
        "imageone imagetwo imagefive"
        "imageone imagethree imagesix"
        "imageone imagethree imagesix";
    /* max-width: 500px; */

    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    aspect-ratio: 1 / 1;
    z-index: 1;
    position: relative;
    padding: 20px 0;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem {
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
    background-size: 400% 100%;
    animation: shimmer 2s infinite;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:first-child {
    grid-area: imageone;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:nth-child(2) {
    grid-area: imagetwo;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:nth-child(3) {
    grid-area: imagethree;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:nth-child(4) {
    grid-area: imagefour;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:nth-child(5) {
    grid-area: imagefive;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 .imageGridItem:nth-child(6) {
    grid-area: imagesix;
}

/* banner theme2 css */
.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
        ". imagetwo ."
        ". imagetwo imagefour"
        "imageone imagetwo imagefour"
        "imageone imagethree imagefour"
        "imageone imagethree imagefive"
        "imageone imagethree imagefive";
    /* max-width: 500px; */

    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    aspect-ratio: 1 / 1;
    z-index: 1;
    position: relative;
    padding: 20px 0;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem {
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
    background-size: 400% 100%;
    animation: shimmer 2s infinite;

}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:first-child {
    grid-area: imageone;
    border-radius: 46px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:first-child img {
    border-radius: 46px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(2) {
    grid-area: imagetwo;
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(2) img {
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(3) {
    grid-area: imagethree;
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(3) img {
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(4) {
    grid-area: imagefour;
    border-radius: 6px 46px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(4) img {
    border-radius: 6px 46px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(5) {
    grid-area: imagefive;
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(5) img {
    border-radius: 6px 6px 6px 6px;
}

/* banner theme3 css */
.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-template-areas:
        "imageone . ."
        "imageone imagethree ."
        "imageone imagethree imagefive"
        "imagetwo imagethree imagefive"
        "imagetwo imagefour imagefive"
        "imagetwo imagefour imagefive";

    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    aspect-ratio: 1 / 1;
    z-index: 1;
    position: relative;
    padding: 20px 0;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
        ". . imagefour"
        ". imagetwo imagefour"
        "imageone imagetwo imagefour"
        "imageone imagetwo imagefive"
        "imageone imagethree imagefive"
        "imageone imagethree imagefive";

    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    aspect-ratio: 1 / 1;
    z-index: 1;
    position: relative;
    padding: 20px 0;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem {
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
    animation: shimmer 2s infinite;
    background-size: 400% 100%;
}
@keyframes shimmer {
    0% {
      background-position: 100% 0;
    }
    100% {
      background-position: -100% 0;
    }
  }
.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:first-child {
    grid-area: imageone;
    border-radius: 46px 6px 6px 6px;

}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:first-child img {
    border-radius: 46px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(2) {
    grid-area: imagetwo;
    border-radius: 6px 6px 6px 46px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(2) img {
    border-radius: 6px 6px 6px 46px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(3) {
    grid-area: imagethree;
    border-radius: 6px 46px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(3) img {
    border-radius: 6px 46px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(4) {
    grid-area: imagefour;
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(4) img {
    border-radius: 6px 6px 6px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(5) {
    grid-area: imagefive;
    border-radius: 6px 46px 46px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(5) img {
    border-radius: 6px 46px 46px 6px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem {
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
    animation: shimmer 2s infinite;
    background-size: 400% 100%;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:first-child {
    grid-area: imageone;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:first-child img {
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(2) {
    grid-area: imagetwo;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(2) img {
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(3) {
    grid-area: imagethree;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(3) img {
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(4) {
    grid-area: imagefour;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(4) img {
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(5) {
    grid-area: imagefive;
    border-radius: 8px;
}

.bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 .imageGridItem:nth-child(5) img {
    border-radius: 8px;
}

a.btn_1,
.btn_1 {
    background: var(--primary);
}

a.btn_1:hover,
.btn_1:hover {
    background: var(--secondary);
}

.popular_tours--section {
    margin-top: 0;
}

/*
plan-trip--section css */
.plan_trip--section {
    background: var(--primary);
    padding-bottom: 12px;
}

.plan_trip--section .main_title_2 h2 {
    color: white;
    margin: 0 0 8px 0;
    font-weight: 600;
}

.plan_trip--section .main_title_2 p {
    color: white;
    margin: 0 0 8px 0;
    font-size: 16px;
}

.plan_trip--section .trip-card {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
}

.plan_trip--section .trip-card .img-wrapper {
    width: 80px;
    height: 80px;
    background-color: var(--light-primary);
    border-radius: 50%;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.plan_trip--section .trip-card .img-wrapper img {
    width: 42px;
    height: 42px;
    object-fit: cover;
}

.plan_trip--section .trip-card .title {
    color: #ffc107;
    font-weight: 600;
}

.plan_trip--section .trip-card .trip-info {
    color: #f6f6f6;
}

/* Popular tour card css */
.navigation-btns--wrapper {
    position: relative;
    margin-top: 26px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.navigation-btns--wrapper .view_all--btn {
    background-color: var(--light-primary);
    color: var(--primary--200);
    display: inline-flex;
    padding: 4px 8px;
    gap: 4px;
    align-items: center;
    border-radius: 6px;
    font-weight: 500;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.navigation-btns--wrapper .view_all--btn:hover {
    background-color: var(--primary);
    color: #fff !important;
}

.navigation-btns--wrapper .view_all--btn .icon {
    width: 14px;
    height: 14px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navigation-btns--wrapper .navigationBtns {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: center;
}

.navigation-btns--wrapper .navigationBtns .swiper-button-prevs.swiper-button-disabled {
    border: 1px solid #bfbebe;
    background: #fff;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    opacity: 0.4;
    font-size: 18px;
}

.navigation-btns--wrapper .navigationBtns .swiper-button-prevs {
    opacity: 1;
    border: 1px solid #bfbebe;
    background: #fff;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    font-size: 18px;
}

.navigation-btns--wrapper .navigationBtns .swiper-button-nexts.swiper-button-disabled {
    border: 1px solid #bfbebe;
    background: #fff;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    opacity: 0.4;
    font-size: 18px;
}

.navigation-btns--wrapper .navigationBtns .swiper-button-nexts {
    opacity: 1;
    border: 1px solid #bfbebe;
    background: #fff;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    font-size: 18px;
}

.popularTourSlider {
    padding: 2px;
}

.popularTourSlider .swiper-wrapper .swiper-slide {
    height: auto;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card {
    background: var(--light-primary);
    border-radius: 8px;
    position: relative;
    border: 1px solid #e9ebef;
    box-shadow: 2px 2px 4px 0 rgba(128, 130, 141, 0.06);
    height: 100%;
    transition: all 0.13s ease-in-out;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card:hover {
    transform: translateY(-0.25rem);
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-container {
    position: relative;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-container .tour-category {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: var(--secondary);
    color: white;
    font-weight: 500;
    padding: 3px 10px;
    border-radius: 16px;
    font-size: 12px;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-wrapper {
    height: 210px;
    border-radius: 8px;
    display: flex;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-wrapper img {
    width: 100%;
    height: 100%;
    border-radius: 8px 8px 0 0;
    object-fit: cover;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content {
    padding: 20px 16px;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .title-wrapper {
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-decoration: none;
    font-weight: 600;
    color: var(--black-200);
    line-height: 1.3;
    margin-bottom: 8px;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-duration {
    display: flex;
    gap: 4px;
    align-items: center;
    color: #333;
    margin-bottom: 10px;
    font-size: 13px;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-duration .icon-wrapper {
    font-size: 14px;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-price{
    font-size: 12px;
}

.popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-price .bold-text {
    color: var(--primary);
    font-size: 18px;
    font-weight: 600;
}

/* destination css */
.section-paddingContainer {
    padding-top: 68px;
    padding-bottom: 68px;
}

.categories-section {
    padding-top: 0px !important;
}

.destination_GridContainer {
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.destination_GridContainer .destination_GridItem {
    position: relative;
    display: flex;
    transition: all 0.2s ease-in-out;
}

.destination_GridContainer .destination_GridItem:hover {
    background-color: #00000041;
    transition: all 0.3s ease;
}

.destination_GridContainer .destination_GridItem img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
}

.destination_GridContainer .destination_GridItem .destination_title {
    position: relative;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 60px 10px;
    color: white;
    text-shadow: 0 3px 5px #000;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* cta section */
.cta-sectionwrapper .cta-section {
    padding: 68px 60px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    /* background: linear-gradient(90deg, #d4a017, #b8860b); */
    position: relative;
    z-index: 2;
}

.cta-sectionwrapper .cta-container {
    position: relative;
}

.cta-sectionwrapper .cta-container .overlay-img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 1;
    object-fit: cover;
    border-radius: 8px;
}

.cta-sectionwrapper .cta-section {
    display: flex;
    gap: 26px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

.cta-sectionwrapper .cta-section .left-section {
    max-width: 540px;
}

.cta-sectionwrapper .cta-section .left-section .title {
    color: white;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
}

.cta-sectionwrapper .cta-section .left-section .sub-text {
    color: #f0f0e9;
    font-size: 16px;
}

.cta-sectionwrapper .cta-section a.btn_1,
.btn_1 {
    background: var(--primary);
}

.cta-sectionwrapper .cta-section a.btn_1:hover,
.btn_1:hover {
    background: var(--secondary);
}

/* description readmore and less section css */
.descriptionFullContainer {
    margin-bottom: 36px;
}

.descriptionFullContainer .description--wrapper {
    margin-bottom: 16px;
    max-height: none;
    overflow: hidden;
    transition: height 0.6s cubic-bezier(0.44, 0.99, 0.48, 1);
    font-size: 16px;
}

.descriptionFullContainer .toggle--btn {
    padding: 0 10px;
    margin-bottom: 16px;
    position: relative;
    border: none;
    outline: none;
    background-color: transparent;
    color: var(--primary);
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
    transition: all 0.6s cubic-bezier(0.44, 0.99, 0.48, 1);
    font-weight: 500;
    border: 0;
}

.descriptionFullContainer .toggle--btn .icon-wrapper {
    font-size: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.6s cubic-bezier(0.44, 0.99, 0.48, 1);
}

.filters_listing.sticky_horizontals {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 2;
}

.secondary_nav.sticky_horizontals {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 2;
}

/* package card grid css */
.packageTour_GridContainer {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    margin-bottom: 60px;
}

.packageTour_GridContainer .popularTour_card {
    background: #fff;
    border-radius: 8px;
    position: relative;
    border: 1px solid #e9ebef;
    box-shadow: 2px 2px 4px 0 rgba(128, 130, 141, 0.06);
    height: 100%;
    transition: all 0.13s ease-in-out;
}

.packageTour_GridContainer .popularTour_card:hover {
    transform: translateY(-0.25rem);
}

.packageTour_GridContainer .popularTour_card .img-container {
    position: relative;
}

.packageTour_GridContainer .popularTour_card .img-container .tour-category {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: var(--secondary);
    color: white;
    font-weight: 500;
    padding: 3px 10px;
    border-radius: 16px;
    font-size: 12px;
}

.packageTour_GridContainer .popularTour_card .img-wrapper {
    height: 180px;
    border-radius: 8px;
    display: flex;
}

.packageTour_GridContainer .popularTour_card .img-wrapper img {
    width: 100%;
    height: 100%;
    border-radius: 8px 8px 0 0;
    object-fit: cover;
}

.packageTour_GridContainer .popularTour_card .tour-content {
    padding: 20px 16px;
}

.packageTour_GridContainer .popularTour_card .tour-content .title-wrapper {
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-decoration: none;
    font-weight: 600;
    color: var(--black-200);
    line-height: 1.3;
    margin-bottom: 8px;
}

.packageTour_GridContainer .popularTour_card .tour-content .tour-duration {
    display: flex;
    gap: 4px;
    align-items: center;
    color: #333;
    margin-bottom: 10px;
    font-size: 13px;
}

.packageTour_GridContainer .popularTour_card .tour-content .tour-duration .icon-wrapper {
    font-size: 14px;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.packageTour_GridContainer .popularTour_card .tour-content .tour-price {
    font-size: 12px;
}

.packageTour_GridContainer .popularTour_card .tour-content .tour-price .bold-text {
    color: var(--primary);
    font-size: 18px;
    font-weight: 600;
}

footer .footer_logo {
    object-fit: cover;
    height: 36px;
    width: auto;
}

footer .links li a:hover {
    color: var(--primary);
}

footer ul.links li a:hover:after {
    color: var(--primary);
}

footer .contacts li a:hover {
    color: var(--primary);
}

#toTop:hover {
    background-color: var(--primary);
}

.blog_sectionContainer .box_news:hover .blog_title {
    color: var(--primary);
}

.main-menu ul ul li:hover>a {
    color: var(--primary);
}

.info_containerSection .boxed_list {
    color: var(--primary--200);
}

.info_containerSection .boxed_list:hover i {
    color: var(--secondary);
}

.hero_in .wrapper h1 span {
    background-color: var(--primary);
}

.contact_info h4 {
    color: var(--primary) !important;
}

article.blog .post_info h3 a:hover {
    color: var(--primary) !important;
}

.blog_detail a {
    color: var(--primary);
}

.dropdownmenusection {
    display: none;
}

/* megameu header css web */
.header__mainContainer {
    background-color: var(--light-primary);
}

.header-bottom {
    z-index: 2;
}

.header-bottom--wrapper {
    flex-wrap: nowrap !important;
}

.header .header-bottom__logo-wrapper img {
    height: 100%;
    width: auto;
}

.header .header-bottom--wrapper .nav-menu {
    position: relative;
    flex-grow: 1;
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 20px;
    margin-bottom: 0;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link {
    color: var(--black-200);
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link .icon-wrapper {
    font-size: 19px;
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link:hover {
    color: var(--primary--200);
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link.dropdown-toggle {
    background: transparent;
}

.header.show .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link {
    color: #fff;
}

.header.show .header-bottom--wrapper .nav-menu .nav-menu__item .nav-menu__link:hover {
    color: #ffc107;
}

.header.show .header-bottom--wrapper .nav-menu .nav-menu__item.active-menu .nav-menu__link {
    color: #ffc107;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item.active-menu .nav-menu__link {
    color: var(--primary--200);
}

.header.show .header-bottom--wrapper .nav-menu .nav-menu__item.active-menu .nav-menu__link .icon-wrapper {
    transform: rotate(180deg);
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item.active-menu .nav-menu__link .icon-wrapper {
    transform: rotate(180deg);
}

.dropdownmenusection.active {
    display: block;
    position: absolute;
    top: 62px;
    left: 0;
    right: 240px;
    background-color: white;
}

#megamenuoverlay.show {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: rgba(17, 17, 17, 0.6);
    transition: all 0.3s ease;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item.dropdownbutton {
    position: relative;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .drop-section {
    position: absolute;
    display: none;
    position: absolute;
    top: 40px;
    left: 0;
    background: white;
    padding: 15px;
    z-index: 1041;
    min-width: 200px;
    padding: 0;
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.175);
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .drop-section li a {
    color: #555;
    font-size: 14px;
    font-weight: 400;
    padding: 14px;
    border-bottom: 1px solid #ededed;
    display: flex;
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .drop-section li a:hover {
    color: var(--primary--200);
}

.header .header-bottom--wrapper .nav-menu .nav-menu__item .drop-section.active {
    display: block;
}

.dropdownmenusection .dropdownmenusection__container {
    display: flex;
    width: 100%;
}

.dropdownmenusection .dropdownmenusection__container .navmenusection {
    width: 100%;
    max-width: 25%;
    flex: 0 0 25%;
    padding: 0;
    margin: 16px 0;
    gap: 8px;
    height: 100%;
}

.dropdownmenusection .dropdownmenusection__container .navmenusection .tab_menu-btn {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    border-radius: 0;
    padding: 12px 18px;
    transition: all 0.2s ease;
    border-right: 4px solid transparent;
}

.dropdownmenusection .dropdownmenusection__container .navmenusection .tab_menu-btn:hover {
    background-color: #f8f8fa;
}

.dropdownmenusection .dropdownmenusection__container .navmenusection .tab_menu-btn.active {
    background-color: #f8f8fa;
    border-right: 4px solid var(--primary--200);
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent {
    border-left: 1px solid #e5e5e5;
    background-color: #f8f8fa;
    width: 100%;
    max-width: 75%;
    flex: 0 0 75%;
    padding: 20px;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .headingwrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 28px;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .headingwrapper .megamenu__link-btn {
    padding: 10px 16px;
    background: var(--secondary);
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .headingwrapper .megamenu__link-btn:hover {
    background: var(--primary);
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .headingwrapper .megamenu__link-btn .arrow-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .headingwrapper .close__megamenu--btn {
    border: 0;
    background-color: transparent;
    padding: 0;
    font-size: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #333;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper {
    position: relative;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper::before {
    content: "";
    border-right: 1px solid #e5e5e5;
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper .submenu-listwrappers {
    padding: 0;
    margin: 0 0;
    list-style: none;
    display: block !important;
    column-count: 2;
    column-gap: 32px;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper .submenu-listwrappers li {
    padding-bottom: 10px;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper .submenu-listwrappers li a {
    font-size: 14px;
    color: #555;
    text-decoration: none;
    font-weight: 500;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper .submenu-listwrappers li a .submenu-block__item__span {
    color: #999;
    font-size: 12px;
    text-transform: uppercase;
}

.dropdownmenusection .dropdownmenusection__container .navmenucontent .submenu-block__rowwrapper .submenu-listwrappers li a:hover {
    color: var(--primary--200);
}

.header__mainpageContainer {
    background-color: #fff;
}

.header.page-header .dropdown.currencyDropdown .dropdown-toggle {
    background-color: #f2f2f2;
}
.header--sm.homeheader-sm{
   background: var(--light-primary);
}
.header--sm {
    display: none;
}

.header--sm .header--wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.header--sm .header--wrapper .header__logo-wrapper {
    display: flex;
    align-items: center;
    height: 46px;
}

.header--sm .header--wrapper .header__logo-wrapper .header__logo-image {
    height: 100%;
    width: auto;
    object-fit: cover;
    object-position: center;
    flex-shrink: 0;
}

.header--sm .header--wrapper .menu-toggle .menu-bar {
    width: 26px;
    height: 26px;
    font-size: 24px;
    color: var(--black-200);
    display: flex;
    justify-content: center;
    align-items: center;
}

.header--sm .header--wrapper .menu-toggle .offcanvas {
    width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    visibility: hidden;
    background: #fff;
    height: 100%;
    transition: all 0.3s ease-in-out;
    z-index: 1045;
    overflow-y: auto;
    transform: translateX(100%);
    padding: 16px;
}

.header--sm .header--wrapper .menu-toggle .offcanvas.active {
    visibility: visible;
    transform: none;
}

.header--sm .header--wrapper .menu-toggle .offcanvas.active .offcanvas-body ul li .dropdown-toggle{
    border: 1px solid #e3e3e3;
    margin-top: 8px;
}

.header--sm .header--wrapper .menu-toggle .offcanvas .offcanvas-header {
    background-color: white;
    justify-content: flex-end;
    display: flex;
}

.header--sm .header--wrapper .menu-toggle .offcanvas .offcanvas-header .btn-closes {
    color: #232323;
    background-color: transparent;
    border: 0;
}

.header--sm .header--wrapper .menu-toggle .offcanvas .offcanvas-header .btn-closes .close-icon {
    width: 20px;
    height: 20px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.offcanvas-body .menu-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.offcanvas-body .menu-list li .menu-list-item {
    color: #1e1f24;
    text-decoration: none;
    padding: 6px 0;
    position: relative;
    display: block;
    font-size: 16px;
    font-weight: 500;
}

.offcanvas-body .menu-list li .menu-list-item.has-menu::after {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m13.172 12l-4.95-4.95l1.414-1.413L16 12l-6.364 6.364l-1.414-1.415z'/%3E%3C/svg%3E");
    font-size: 14px;
    line-height: 1;
    color: #1e1f24;
    position: absolute;
    right: 0;
    top: 7px;
}

.offcanvas-body .menu-list li .submenu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 1;
    padding: 0 0 16px 0;
    margin: 0;
    list-style: none;
    overflow-y: auto;
}

.offcanvas-body .menu-list li .submenu.active {
    display: block;
    background-color: white;
}

.offcanvas-body .menu-list li .submenu .back-wrapper .back-btn {
    border: 0;
    background-color: transparent;
    display: inline-flex;
    gap: 8px;
    align-items: center;
    color: #1e1f24;
    font-size: 16px;
    font-weight: 500;
}

.offcanvas-body .menu-list li .submenu li {
    padding: 0px 16px 6px 16px;
}

.offcanvas-body .menu-list li .submenu li .submenu-item {
    color: #1e1f24;
    text-decoration: none;
    padding: 3px 0;
    position: relative;
    display: block;
    font-size: 16px;
    font-weight: 500;
    width: 100%;
}

.offcanvas-body .menu-list li .submenu li .submenu-item.has-menu::after {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m13.172 12l-4.95-4.95l1.414-1.413L16 12l-6.364 6.364l-1.414-1.415z'/%3E%3C/svg%3E");
    font-size: 14px;
    line-height: 1;
    color: #1e1f24;
    position: absolute;
    right: 0;
    top: 7px;
}

.offcanvas-body .menu-list li .submenu li .submenu-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.offcanvas-body .menu-list li .submenu li .submenu-wrapper .icon-wrapper {
    width: 18px;
    height: 18px;
    font-size: 16px;
    color: #1e1f24;
}

.offcanvas-body .menu-list li .submenu li .submenu-wrapper .submenu-item {
    color: #1e1f24;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
}

.offcanvas-body .menu-list li .submenu li .sub-submenu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 1;
    padding: 0 0 16px 0;
    margin: 0;
    list-style: none;
    overflow-y: auto;
}

.offcanvas-body .menu-list li .submenu li .sub-submenu.active {
    display: block;
    background-color: white;
}

.offcanvas-body .menu-list li .submenu li .sub-submenu .menu--title a {
    padding: 6px 16px;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1e1f24;
    text-decoration: none;
}

.offcanvas-body .menu-list li .submenu li .sub-submenu li {
    padding: 6px 16px;
    display: flex;
    color: #80828d;
    align-items: center;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.offcanvas-body .menu-list li .submenu li .sub-submenu li a {
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    color: #1e1f24;
    display: flex;
}

.offcanvas-body .menu-list li .submenu-header {
    padding: 16px;
    background-color: white;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    position: sticky;
    top: 0;
}

.offcanvas-body .menu-list li .submenu-header .btn-closes {
    color: #1e1f24;
    background-color: transparent;
    border: 0;
}

.offcanvas-body .menu-list li .submenu-header .btn-closes .close-icon {
    width: 20px;
    height: 20px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* itinerary detal page css */
.itinerary_banner--section .banner__gallery {
    position: relative;
}

.itinerary_banner--section .banner__gallery .gallery__btn {
    display: flex;
    align-items: center;
    gap: 4px;
    position: absolute;
    bottom: 16px;
    right: 16px;
    background-color: #fff;
    color: var(--black--100);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--black--100);
    font-weight: 500;
    font-size: 12px;
    transition: all 0.3s ease-in-out;
    z-index: 1;
}

.itinerary_banner--section .banner__gallery .gallery__btn .icon-wrapper {
    width: 20px;
    height: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.itinerary_banner--section .banner__gallery .gallery__btn:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
}

.itinerary_banner--section .banner__gallery .banner__grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-gap: 5px;
    max-height: 569px;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img {
    display: block;
    width: 100%;
    height: 282px;
    cursor: pointer;
    grid-column: span 3;
    margin: 0;
    background: #000;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail {
    position: relative;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon {
    position: relative;
    background-color: var(--primary);
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    z-index: 1;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon .icon-wrapper {
    font-size: 22px;
    width: 24px;
    height: 24px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon-wrapper::after {
    content: "";
    position: absolute;
    z-index: 0;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 52px;
    height: 52px;
    background-color: var(--primary);
    border-radius: 50%;
    animation: pulse-border 1500ms ease-out infinite;
    z-index: -1;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #2125297a;
    top: 0;
    left: 0;
    border-radius: 16px;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img:first-child {
    grid-row: span 2;
    height: 569px;
    grid-column: span 6;
}

.itinerary_banner--section .banner__gallery .banner__grid .banner__img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.breadcrumb__wrapper {
    padding: 16px 0 26px;
}

.breadcrumb__wrapper .breadcrumbs {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 0;
    white-space: nowrap;
}
.breadcrumb__wrapper .breadcrumbs li{
    line-height: 1.4;
}
.breadcrumb__wrapper .breadcrumbs li a {
    color: #333;
    font-size: 13px;
    font-weight: 500;

}

.breadcrumb__wrapper .breadcrumbs li.active {
    color: var(--secondary);
    font-size: 13px;
    margin-bottom: 0;
    display: inline;
    font-weight: 500;
    /* display: -webkit-box; */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;

}

.breadcrumb__wrapper .breadcrumbs li:not(:last-child)::after {
    content: "";
    display: inline-block;
    margin: 0 6px;
    width: 6px;
    height: 9px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNCIgaGVpZ2h0PSI3IiB2aWV3Qm94PSIwIDAgNCA3IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAuMTM3MzI0IDYuODYyOTFDMC4zMTk3OTggNy4wNDU3IDAuNjEzOTkxIDcuMDQ1NyAwLjc5NjQ2NSA2Ljg2MjkxTDMuODkxMDcgMy43NjI5OUM0LjAzNjMxIDMuNjE3NTEgNC4wMzYzMSAzLjM4MjQ5IDMuODkxMDcgMy4yMzcwMUwwLjc5NjQ2NSAwLjEzNzA5QzAuNjEzOTkxIC0wLjA0NTY5NjYgMC4zMTk3OTkgLTAuMDQ1Njk2NiAwLjEzNzMyNCAwLjEzNzA5Qy0wLjA0NTE0OTMgMC4zMTk4NzcgLTAuMDQ1MTQ5MyAwLjYxNDU3NSAwLjEzNzMyNCAwLjc5NzM2MkwyLjgzMzQ3IDMuNTAxODdMMC4xMzM2MDEgNi4yMDYzN0MtMC4wNDUxNDkzIDYuMzg1NDIgLTAuMDQ1MTQ5OCA2LjY4Mzg1IDAuMTM3MzI0IDYuODYyOTFaIiBmaWxsPSIjOTA5MDkxIi8+Cjwvc3ZnPgo=);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tourdetails__title {
    font-size: 28px;
    font-weight: 600;
    color: var(--black--100);
    margin-bottom: 16px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tourdetails__desc p {
    font-size: 15px;
    margin-bottom: 20px;
    color: var(--black--300);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts {
    background: #ddebfd;
    padding: 25px;
    border-radius: 4px;
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 28px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item {
    display: flex;
    gap: 12px;
    align-items: center;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item .icon-wrapper {
    width: 28px;
    height: 28px;
    font-size: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--black-300);
    flex-shrink: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item .icon-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item .tour-facts__contents {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item .tour-facts__contents .tour-facts__title {
    font-size: 12px;
    color: var(--black-500);
    line-height: 1.2;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid .tour-facts__item .tour-facts__contents .tour-facts__text {
    font-size: 15px;
    font-weight: 500;
    color: var(--black--300);
    line-height: 1.3;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights .tour-highlights--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights .highlights__list--wrapper ul {
    margin: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights .highlights__list--wrapper ul li {
    margin-bottom: 12px;
    color: var(--black--300);
    font-size: 14px;
    font-weight: 400;
    position: relative;
    padding-left: 28px;
    list-style: none;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights .highlights__list--wrapper ul li::before {
    position: absolute;
    left: 0;
    top: 3px;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 1024 1024'%3E%3Cpath fill='%23a28307' fill-opacity='0.15' d='M512 140c-205.4 0-372 166.6-372 372s166.6 372 372 372s372-166.6 372-372s-166.6-372-372-372m154.7 378.4l-246 178c-5.3 3.8-12.7 0-12.7-6.5V643c0-10.2 4.9-19.9 13.2-25.9L566.6 512L421.2 406.8c-8.3-6-13.2-15.6-13.2-25.9V334c0-6.5 7.4-10.3 12.7-6.5l246 178c4.4 3.2 4.4 9.7 0 12.9'/%3E%3Cpath fill='%23a28307' d='M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448s448-200.6 448-448S759.4 64 512 64m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372s372 166.6 372 372s-166.6 372-372 372'/%3E%3Cpath fill='%23a28307' d='m666.7 505.5l-246-178c-5.3-3.8-12.7 0-12.7 6.5v46.9c0 10.3 4.9 19.9 13.2 25.9L566.6 512L421.2 617.1c-8.3 6-13.2 15.7-13.2 25.9v46.9c0 6.5 7.4 10.3 12.7 6.5l246-178c4.4-3.2 4.4-9.7 0-12.9'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .description__contents p {
    font-size: 15px;
    color: var(--black--300);
    line-height: 1.4;
    margin-bottom: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .description__contents p a {
    font-size: 15px;
    color: var(--primary);
    line-height: 1.6;
    margin-bottom: 20px;
    text-decoration: underline;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .tour-overview__toggleBtnwrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .tour-overview__toggleBtnwrapper::before {
    display: block;
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: -98px;
    height: 100px;
    z-index: 1;
    background: linear-gradient(to bottom, #f6f6f600, #f6f6f6);
    transition: all 0.3s ease-in;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description.expanded .tour-overview__toggleBtnwrapper::before {
    display: none;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .tour-overview__toggleBtnwrapper .tour-overview__toggle--btn {
    font-size: 14px;
    color: var(--secondary);
    border: 0;
    background: transparent;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease-in;
    margin-top: 10px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__description .tour-overview__toggleBtnwrapper .tour-overview__toggle--btn .icon-wrapper {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .included-price--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .costdetail--list.included-list ul {
    margin-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .costdetail--list.included-list ul li {
    margin-bottom: 16px;
    color: var(--black--300);
    font-size: 15px;
    font-weight: 400;
    position: relative;
    padding-left: 28px;
    list-style: none;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .costdetail--list.included-list ul li::before {
    position: absolute;
    left: 0;
    top: 4px;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='17' viewBox='0 0 16 16'%3E%3Cg fill='none' stroke='%230054a6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'%3E%3Cpath d='M14.25 8.75c-.5 2.5-2.385 4.854-5.03 5.38A6.25 6.25 0 0 1 3.373 3.798C5.187 1.8 8.25 1.25 10.75 2.25'/%3E%3Cpath d='m5.75 7.75l2.5 2.5l6-6.5'/%3E%3C/g%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .costdetail--list.included-list ul li a {
    color: var(--primary);
    text-decoration: underline;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper {
    margin-bottom: 36px;
    background-color: #fef7e6;
    padding: 22px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .excluded-price--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .costdetail--list.excluded-list ul {
    margin-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .costdetail--list.excluded-list ul li {
    margin-bottom: 16px;
    color: var(--black--300);
    font-size: 15px;
    font-weight: 400;
    position: relative;
    padding-left: 28px;
    list-style: none;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .costdetail--list.excluded-list ul li:last-child {
    margin-bottom: 0;

}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .costdetail--list.excluded-list ul li::before {
    position: absolute;
    left: 0;
    top: 3px;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='19' height='19' viewBox='0 0 24 24'%3E%3Cpath fill='%23a28307' d='M12 20c-4.41 0-8-3.59-8-8s3.59-8 8-8s8 3.59 8 8s-3.59 8-8 8m0-18C6.47 2 2 6.47 2 12s4.47 10 10 10s10-4.47 10-10S17.53 2 12 2m2.59 6L12 10.59L9.41 8L8 9.41L10.59 12L8 14.59L9.41 16L12 13.41L14.59 16L16 14.59L13.41 12L16 9.41z'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .costdetail--list.excluded-list ul li a {
    color: var(--primary);
    text-decoration: underline;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .itinerary_titlewrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .itinerary_titlewrapper .tour_itinerary--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 0px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .itinerary_titlewrapper .accordion_toggle--btn {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    color: var(--primary);
    border: 1px solid var(--primary);
    border-radius: 4px;
    padding: 3px 12px;
    font-weight: 500;
    white-space: nowrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card {
    position: relative;
    padding: 0px 0px 0px 48px;
    border: 0 !important;
    background-color: transparent;
    margin: 0;
    overflow: visible;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card::after {
    position: absolute;
    content: "";
    left: 16px;
    top: 18px;
    height: 100%;
    border-left: 1px dashed rgba(128, 130, 141, 0.62);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card::before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24'%3E%3Cpath fill='%23fff' d='M12 3a7 7 0 0 0-7 7c0 2.862 1.782 5.623 3.738 7.762A26 26 0 0 0 12 20.758q.262-.201.615-.49a26 26 0 0 0 2.647-2.504C17.218 15.623 19 12.863 19 10a7 7 0 0 0-7-7m0 20.214l-.567-.39l-.003-.002l-.006-.005l-.02-.014l-.075-.053l-.27-.197a28 28 0 0 1-3.797-3.44C5.218 16.875 3 13.636 3 9.999a9 9 0 0 1 18 0c0 3.637-2.218 6.877-4.262 9.112a28 28 0 0 1-3.796 3.44a17 17 0 0 1-.345.251l-.021.014l-.006.005l-.002.001zM12 8a2 2 0 1 0 0 4a2 2 0 0 0 0-4m-4 2a4 4 0 1 1 8 0a4 4 0 0 1-8 0'/%3E%3C/svg%3E");
    position: absolute;
    top: 18px;
    left: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    font-size: 24px;
    z-index: 1;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header {
    padding: 0;
    background-color: transparent;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header .accordion-button {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 18px 14px 18px 2px;
    gap: 32px;
    text-align: left;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--black--100);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header .accordion-button::after {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 24 24'%3E%3Cpath fill='%23757575' d='M19 12.998h-6v6h-2v-6H5v-2h6v-6h2v6h6z'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header .accordion-button:not(.collapsed)::after {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 24 24'%3E%3Cpath fill='%23757575' d='M19 12.998H5v-2h14z'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header .accordion-button:hover {
    text-decoration: none;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card:last-child {
    overflow: hidden;
    padding: 0px 0px 0px 48px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card:first-child .card-header {
    border-top: 0;
    border-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents {
    margin-bottom: 28px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-stats {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-stats .stats-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    font-weight: 500;
    color: #626262;
    flex-wrap: wrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-stats .stats-item .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: var(--secondary);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer p {
    font-size: 15px;
    color: var(--black--300);
    margin-bottom: 20px;
    line-height: 1.5;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer .itinerary__accordion-gallerywrapper {
    margin-bottom: 28px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer .itinerary__accordion-gallerywrapper .titlewrapper {
    font-size: 18px;
    font-weight: 600;
    color: var(--black--100);
    margin-bottom: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer .itinerary__accordion-gallerywrapper .gallery-wrap {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 12px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer .itinerary__accordion-gallerywrapper .gallery-wrap .img-wrapper {
    height: 160px;
    max-width: 100%;
    width: 100%;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary__accordion-maincontainer .itinerary__accordion-gallerywrapper .gallery-wrap .img-wrapper img {
    height: 100%;
    max-width: 100%;
    width: 100%;
    object-fit: cover;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper {
    margin-bottom: 26px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .titlewrapper {
    font-size: 18px;
    font-weight: 600;
    color: var(--black--100);
    margin-bottom: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper {
    margin-bottom: 10px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide {
    height: auto;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper {
    height: 100%;
    background: #fff;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activities_img-wrapper {
    height: 140px;
    overflow: hidden;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activities_img-wrapper img {
    width: 100%;
    height: 100%;
    border-radius: 8px 8px 0 0;
    object-fit: cover;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activitites_content {
    padding: 14px 12px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activitites_content .title {
    font-size: 14px;
    margin-bottom: 4px;
    line-height: 1.5;
    color: var(--black--300);
    font-weight: 600;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activitites_content .activities-desc--sm {
    font-size: 12px;
    margin-bottom: 9px;
    line-height: 1.5;
    color: var(--black-500);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-wrapper .swiper-slide .activities__cardwrapper .activitites_content .itinerary_activities--btn {
    border: 0;
    border-bottom: 1px solid var(--black--300);
    font-size: 12px;
    background: #fff;
    color: var(--black-300);
    font-weight: 500;
    padding: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-pagination {
    position: relative;
    bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-pagination .swiper-pagination-bullet {
    margin: 0 2px;
    height: 9px;
    width: 9px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .itinerary__accordion-contents .itinerary_activities--wrapper .activities_slider--containerBox .activities__card--grid .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--secondary);
    width: 20px;
    border-radius: 8px;
}

/* activities modal css */

.itineraryactivities__modal .modal-dialog .modal-content .modal-header {
    justify-content: end;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-header .btn-closes {
    border: 0;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: red;
    font-size: 14px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-header .btn-closes .icon-wrapper {
    font-size: 16px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body {
    padding: 20px 0 !important;
    margin-top: 0 !important;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .maintitle {
    margin-bottom: 16px;
    font-weight: 500;
    font-size: 16px;
    color: var(--black--100);
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .image-wrapper {
    width: 100%;
    margin-bottom: 16px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .image-wrapper img {
    width: 100%;
    height: 240px;
    object-fit: cover;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent {
    padding: 2px 20px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .trip-title {
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 15px;
    color: var(--black--100);
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_listwrapper {
    margin-bottom: 12px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_listwrapper .activity_flexwrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_listwrapper .activity_flexwrapper .activity_listitem {
    display: flex;
    align-items: center;
    gap: 8px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_listwrapper .activity_flexwrapper .activity_listitem .icon-wrapper {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #80828d;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_listwrapper .activity_flexwrapper .activity_listitem .text-content {
    font-size: 14px;
    color: var(--black--300);
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activity_shortinfo {
    padding: 8px;
    font-size: 13px;
    background: #f6f6f6;
    border-radius: 4px;
    margin-bottom: 10px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activies_description p {
    font-size: 14px;
    color: var(--black--100);
    line-height: 1.6;
    margin-bottom: 20px;
}

.itineraryactivities__modal .modal-dialog .modal-content .modal-body .activity_tripcontent .activies_description p:last-child {
    margin-bottom: 0;
}

/* itinerary activities modal css ending */

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_itinerary--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: transparent;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card:first-child {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .card-header {
    padding: 0;
    background-color: transparent;
    border-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .card-header .btn-link {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 13px 14px 13px 8px !important;
    gap: 32px;
    text-align: left;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--black--100);
    padding: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .card-header .btn-link::after {
    transition: all 0.3s ease;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='23' height='23' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m7 10l5 5m0 0l5-5'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .card-header .btn-link:not(.collapsed):after {
    transition: all 0.3s ease;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='23' height='23' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m17 14l-5-5m0 0l-5 5'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .accordionContents ul {
    padding: 0 13px 0 18px;
    list-style: disc;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .accordionContents ul li {
    padding-bottom: 8px;
    font-size: 15px;
    color: var(--black--300);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .accordionContents p {
    font-size: 15px;
    color: var(--black--300);
    margin-bottom: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_equipmentInfo--accordion .card .accordionContents {
    padding: 0 13px 0 8px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 0px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .title-wrapper .downloadmap-pdf {
    font-size: 14px;
    color: var(--black--100);
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    justify-content: center;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .title-wrapper .downloadmap-pdf .icon-wrapper {
    font-size: 22px;
    width: 22px;
    height: 22px;
    color: var(--primary--200);
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    justify-content: center;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .tourmap__imgwrapper {
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.125);
    width: 100%;
    max-width: 800px;
    height: auto;
    margin: 0 auto;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .tourmap__imgwrapper img {
    height: auto;
    object-fit: cover;
    width: 100%;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_video__section {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_video__section .title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 24px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_video__section .video-wrapper {
    width: 100%;
    height: 100%;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_video__section .video-wrapper .plyr--video .plyr__poster {
    background-size: cover;
    width: 100%;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection {
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faq-titlewrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faq-titlewrapper .tour_faq--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 0px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faq-titlewrapper .faqaccordion_toggle--btn {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    color: var(--primary);
    border: 1px solid var(--primary);
    border-radius: 4px;
    padding: 3px 12px;
    font-weight: 500;
    white-space: nowrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .tripfaq__accordionheader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--secondary);
    background-color: #e9e9e9;
    padding: 15px;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card {
    margin-bottom: 0;
    background-color: transparent;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap:last-child .accordion-wrapper .card {
    margin-bottom: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: transparent;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card:not(:last-child) {
    margin-bottom: 0;
    border-bottom: 0px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-header {
    padding: 0;
    background-color: transparent;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-header .btn-link {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 14px 16px !important;
    gap: 32px;
    text-align: left;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    color: var(--black--100);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-header .btn-link::after {
    transition: all 0.3s ease;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='23' height='23' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m7 10l5 5m0 0l5-5'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-header .btn-link:not(.collapsed):after {
    transition: all 0.3s ease;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='23' height='23' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m17 14l-5-5m0 0l-5 5'/%3E%3C/svg%3E");
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body {
    padding: 16px 16px 0 16px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body p {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--black-300);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body p a {
    color: var(--primary);
    text-decoration: underline;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body ul {
    padding: 0 13px 0 18px;
    list-style: disc;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body ul li {
    padding-bottom: 8px;
    font-size: 15px;
    color: var(--black--300);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faqaccordion__mainwrapper .accordion .accordion-wrap .accordion-wrapper .card .card-body ul li:last-child {
    padding-bottom: 0;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper {
    position: sticky;
    top: 80px;
    z-index: 9;
    margin-bottom: 36px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card {
    padding: 34px 20px 20px 20px;
    background-color: #fff;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    position: relative;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .tourprice__badge {
    background-color: var(--primary--200);
    color: white;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    padding: 4px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -16px;
    right: 19px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .tourprice__badge::after {
    content: "";
    position: absolute;
    right: -22px;
    width: 0;
    height: 0;
    border-left: 16px solid var(--primary--200);
    border-right: 9px solid transparent;
    border-top: 15px solid transparent;
    top: 0px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price {
    margin-bottom: 4px;
    display: flex;
    flex-direction: column;
    gap: 62x;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .country-select {
    margin-bottom: 16px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price .trip-price__pax {
    color: var(--black--300);
    font-size: 14px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price .trip-price__amount {
    color: var(--black--100);
    font-size: 24px;
    font-weight: 600;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price .trip-price__amount .trip-price__striked {
    font-size: 15px;
    color: var(--black-500);
    text-decoration: line-through;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price .trip-price__amount .discounttag {
    position: relative;
    background-color: #dc2626;
    font-size: 11px;
    padding: 1px 4px;
    color: #fff;
    margin-left: 14px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip__price .trip-price__amount .discounttag::before {
    content: "";
    position: absolute;
    border-left: 10px solid transparent;
    border-right: 10px solid #dc2626;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    top: 0px;
    left: -15px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .review-wrapper {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .review-wrapper .review-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .review-wrapper .review-icons .icon-wrapper {
    width: 14px;
    height: 14px;
    color: var(--primary--200);
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .review-wrapper .totalpreviewlink {
    color: var(--secondary);
    font-size: 14px;
    font-weight: 500;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip_price--info {
    margin-bottom: 20px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip_price--info li {
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--black--100);
    font-size: 13px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .trip_price--info li .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: var(--black-500);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-direction: column;
    margin-bottom: 26px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper .btn--lg {
    padding: 12px 16px;
    border-radius: 6px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: #fff;
    font-weight: 500;
    border: 0;
    box-shadow: none;
    transition: all 0.3s ease-in-out;
}

.btn-primary {
    background-color: var(--primary);
}

.btn-primary:hover {
    background-color: #957600;
}

.btn-secondary {
    background-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: #004494;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper .btn--lg.btn--primary {
    background-color: var(--primary);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper .btn--lg.btn--primary:hover {
    background-color: #957600;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper .btn--lg.btn--secondary:hover {
    background-color: #004494;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .btn-wrapper .btn--lg.btn--secondary {
    background-color: var(--secondary);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .instant-contact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .instant-contact .icon-wrapper {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .instant-contact .content-wrapper .text-content {
    font-size: 14px;
    color: var(--black--300);
}

.tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper .tripdetail__side--card .instant-contact .content-wrapper .contactlink {
    font-size: 14px;
    color: var(--black--300);
}

/* offcanvas itinerary */
/* Offcanvas styles */
/* .header.main-header {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
} */

/* Overlay styles */
.offcanvas-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1050;
    transition: opacity 0.3s ease;
}

.offcanvas-overlay.show {
    display: block;
    opacity: 1;
}

/* Offcanvas menu styles */
.tripitinerary-offcanvas {
    position: fixed;
    top: 0;
    right: 0px;
    max-width: 440px;
    width: 100%;
    height: 100%;
    background: #fff;
    transition: transform 0.3s ease, visibility 0s ease 0.3s, right 0.3s ease;
    z-index: 1060;
    transform: translateX(100%);
    visibility: hidden;

    overflow-x: hidden;
}

.tripitinerary-offcanvas.show {
    right: 0;
    transform: translateX(0);
    visibility: visible;
    transition: transform 0.3s ease, visibility 0s ease 0s, right 0.3s ease;
}

/* Disable body scroll when offcanvas is open */
body.offcanvas-open {
    overflow: hidden;
    padding-right: 17px;
}

.tripitinerary-offcanvas .inquiryFormWrapper {
    padding: 20px;
}

.tripitinerary-offcanvas .inquiryFormWrapper .btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: end;
    margin-bottom: 26px;
}

.tripitinerary-offcanvas .inquiryFormWrapper .btn-wrapper .close-offcanvas {
    border: 0;
    background: none;
    font-size: 24px;
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tripitinerary-offcanvas .inquiryFormWrapper .heading-wrapper {
    margin-bottom: 26px;
}

.tripitinerary-offcanvas .inquiryFormWrapper .heading-wrapper .title-wrapper {
    font-size: 22px;
    color: var(--black--100);
    margin-bottom: 8px;
    font-weight: 600;
}

.tripitinerary-offcanvas .inquiryFormWrapper .heading-wrapper .sub-title {
    margin-bottom: 0;
    font-size: 14px;
    color: var(--black-500);
}

.tripitinerary-offcanvas .inquiryFormWrapper .form-wrapper .form-group .form-control:focus {
    box-shadow: none !important;
}

.tripitinerary-offcanvas .datepaneloffcanvas {
    padding: 20px;
}

.tripitinerary-offcanvas .datepaneloffcanvas .btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: end;
    margin-bottom: 26px;
}

.tripitinerary-offcanvas .datepaneloffcanvas .btn-wrapper .close-offcanvas {
    border: 0;
    background: none;
    font-size: 24px;
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tripitinerary-offcanvas .datepaneloffcanvas .heading-wrapper {
    margin-bottom: 26px;
}

.tripitinerary-offcanvas .datepaneloffcanvas .heading-wrapper .title-wrapper {
    font-size: 22px;
    color: var(--black--100);
    margin-bottom: 8px;
    font-weight: 600;
}

.tripitinerary-offcanvas .datepaneloffcanvas .form-group .form-control:focus {
    box-shadow: none !important;
}

.tripitinerary-offcanvas .datepaneloffcanvas .box_details .form-group .booking-trip__form-label {
    font-weight: 600;
    font-size: 15px;
    color: var(--black--500);
    margin-bottom: 12px;
}

.tripitinerary-offcanvas .datepaneloffcanvas .box_details .form-group .booking-trip__form-label .booking-trip__small-text {
    font-size: 13px;
    color: var(--black--500);
    display: block;
    font-weight: 400;
}

.tripitinerary-offcanvas .booking .form-group i {
    bottom: 1px;
    top: inherit;
}

.tripitinerary-offcanvas .booking .form-group {
    margin: 0 0 16px 0;
}

.tripitinerary-offcanvas .datepaneloffcanvas .booking-trip__features {
    list-style: none;
    padding: 0;
    margin: 26px 0 0 0;
}

.tripitinerary-offcanvas .datepaneloffcanvas .booking-trip__features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    color: var(--black--300);
}

.tripitinerary-offcanvas .datepaneloffcanvas .booking-trip__features li .booking-trip__icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--black--300);
}

.tour-detail--section .similaTripSliderWrapper {
    margin-top: 16px;
    margin-bottom: 46px;
}

.tour-detail--section .similaTripSliderWrapper .similaTrip--title {
    font-size: 22px;
    font-weight: 500;
    color: var(--black--100);
    margin-bottom: 26px;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide {
    height: auto !important;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard {
    height: 100%;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .imageoverlay {
    position: relative;
    overflow: hidden;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .imageoverlay .img-wrapper {
    width: 100%;
    border-radius: 4px;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .imageoverlay .img-wrapper img {
    width: 100%;
    height: 100%;
    aspect-ratio: 3/3;
    border-radius: 4px;
    object-fit: cover;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .imageoverlay .img-wrapper::after {
    content: "";
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg,
            rgba(0, 0, 0, 0) 67.71%,
            rgba(0, 0, 0, 0.6) 89.06%);
    position: absolute;
    left: 0;
    bottom: 0;
    border-radius: 4px;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .imageoverlay .trip-date {
    position: absolute;
    bottom: 10px;
    left: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    z-index: 1;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .related--trips__content {
    margin-top: 16px;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .related--trips__content .triptitle {
    font-size: 18px;
    font-weight: 600;
    color: var(--black--300);
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-decoration: none;
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .related--trips__content .trip-price {
    font-size: 14px;
    color: var(--black--500);
}

.tour-detail--section .similaTripSliderWrapper .similartripslider .swiper-wrapper .swiper-slide .relatedtripCard .related--trips__content .trip-price span {
    font-weight: 600;
}

.itineraryTabNavigation {
    border-bottom: 1px solid #e9ebef;
    background-color: #fff;
    transition: all 0.5s ease-out;
    transform: translateY(-1000%);
    opacity: 0;
    visibility: hidden;
    display: none;
    width: 100%;
    transition: all 0.5s ease-out;
}

.itineraryTabNavigation.sticky-background {
    display: block;
    position: fixed;
    top: -1px;
    z-index: 999;
    opacity: 1;
    visibility: visible;
    transition: all 0.5s ease-out;
    width: 100%;
    left: 0;
    transform: translateY(0);
    box-shadow: 0 0 10px 0 rgb(7 2 36 / 10%);
}

.itineraryTabNavigation .itinerary_nav .navigation ul {
    margin: 0;
    padding: 0;
    align-items: center;
    max-width: 100%;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    white-space: nowrap;
    height: 50px;
}

.itineraryTabNavigation .itinerary_nav .navigation ul::-webkit-scrollbar {
    display: none;
}

.itineraryTabNavigation .itinerary_nav .navigation ul {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.itineraryTabNavigation .itinerary_nav .navigation ul li {
    height: 100%;
    display: inline-block;
    list-style: none;
    margin-right: 15px;
}

.itineraryTabNavigation .itinerary_nav .navigation ul li a {
    display: inline-flex;
    height: 100%;
    line-height: 100%;
    font-weight: 500;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.8);
    padding: 11px;
    border-radius: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    align-items: center;
    white-space: nowrap;
    gap: 4px;
}

.itineraryTabNavigation .itinerary_nav .navigation ul li a .icon-wrapper {
    width: 18px;
    height: 18px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.itineraryTabNavigation .itinerary_nav .navigation ul li a .icon-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.itineraryTabNavigation .itinerary_nav .navigation ul li a.active {
    background-color: var(--black--100);
    color: #fff;
    /* pointer-events: none; */
    cursor: pointer;
}

/* responsive navigation tab css */
.itinerary_nav--sm {
    display: none;
}

.itinerary_nav--sm .itinerary-nav-headerwrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    height: 50px;
}

.itinerary_nav--sm .itinerary_nav_dropdownmenu {
    display: none;
}

.itinerary_nav--sm .itinerary_nav_dropdownmenu.show-dropdown {
    display: block;
}

.itinerary_nav--sm .itinerary-nav-headerwrapper .titlewrap {
    color: var(--black--100);
    font-size: 15px;
    font-weight: 500;
    text-transform: uppercase;
}

.itinerary_nav--sm .itinerary-nav-headerwrapper .icon-wrapper {
    width: 22px;
    height: 22px;
    font-size: 22px;
    color: var(--black--100);
}

.itinerary_nav--sm .itinerary_nav_dropdownmenu.show-dropdown ul {
    padding: 0 0 20px 0;
    margin: 0;
}

.itinerary_nav--sm .itinerary_nav_dropdownmenu.show-dropdown ul li .nav-link {
    color: var(--black--100);
    padding: 10px 0;
}

.itinerary_nav--sm .itinerary_nav_dropdownmenu.show-dropdown ul li .nav-link.active {
    color: var(--primary);
}

.booking__btn--stickywrapper {
    display: none;
    justify-content: space-between;
    gap: 16px;
    flex-direction: column;
    background-color: #fff;
    position: fixed;
    width: 100%;
    bottom: -1px;
    z-index: 999;
    box-shadow: 0 -4px 4px #00000012;
    padding: 18px 16px;
}
.booking__btn--stickywrapper .trip-price{
    font-size: 14px;
    color: #1c1c1c;
}
.booking__btn--stickywrapper .trip-price span{
    font-weight: 600;
    font-size: 15px;
}
.booking__btn--stickywrapper .btn-wrapper{
    display: flex;
    align-items: center;
    gap: 12px;
}
.booking__btn--stickywrapper .btn-wrapper .btn--lg {
    padding: 12px 16px;
    border-radius: 6px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: #fff;
    font-weight: 500;
    border: 0;
    box-shadow: none;
    transition: all 0.3s ease-in-out;
}
.booking__btn--stickywrapper .btn-wrapper .btn--lg.btn--secondary{
    background-color: var(--secondary);
}
.booking__btn--stickywrapper .btn-wrapper .btn--lg.btn--primary{
    background-color: var(--primary);
}

/* gallery modal css */
.gallerymodalwrapper{
    padding-right: 0 !important;
}
.gallerymodalwrapper .modal-dialog{
 width: 100%;
 height: 100%;
 border-radius: 0;
 max-width: 100%;
 margin: 0;
 background-color: #fff;
}
.gallerymodalwrapper .modal-dialog .modal-content{
    border-radius: 0;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-header{
    text-align: center;
    position: relative;
    text-align: center;
    position: relative;
    align-items: center;
    justify-content: center;
    top: 0;
    width: 100%;
    position: sticky;
    z-index: 1;
    background: white;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-header .titlewrapper{
    font-size: 17px;
    color: var(--black--100);
    font-weight: 500;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-header .backIcon-wrapper{
    position: absolute;
    top: 50%;
    left: 16px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 0;
    background-color: white;

}
.gallerymodalwrapper .modal-dialog .modal-content .modal-header .backIcon-wrapper .icon{
    font-size: 20px;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body{
    max-width: 740px;
    margin:40px auto;
    padding: 0 !important;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer{
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 8px;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img:nth-child(3n+1){
    grid-column: span 12;

}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img{
    grid-column: span 6;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img img{
width: 100%;
height: 100%;
object-fit: cover;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail{
    position: relative;
}

.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail::after{
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #2125297a;
    top: 0;
    left: 0;
    border-radius: 16px;

}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail .play-icon-wrapper{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail .play-icon-wrapper::after{
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 52px;
    height: 52px;
    background-color: var(--primary);
    border-radius: 50%;
    animation: pulse-border 1500ms ease-out infinite;
    z-index: -1;
}

.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail .play-icon-wrapper .play-icon{
    position: relative;
    background-color: var(--primary);
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: flex
;
    align-items: center;
    justify-content: center;
    z-index: 1;
}
.gallerymodalwrapper .modal-dialog .modal-content .modal-body .galleryimage__gridcontainer .banner__img.videothumbnail .play-icon-wrapper .play-icon .icon-wrapper{
    font-size: 22px;
    width: 24px;
    height: 24px;
    color: white;
    display: flex
;
    align-items: center;
    justify-content: center;
    position: relative;
}
/* Responsive */
@media screen and (max-width: 1200px) {
    .dropdownmenusection.active {
        right: 80px;
    }
}

@media screen and (max-width: 991px) {
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img:first-child {
        height: 409px;
    }

    .itinerary_banner--section .banner__gallery .banner__grid .banner__img {
        height: 202px;
    }

    .header.main-header {
        display: none;
    }

    .header--sm {
        display: block;
    }

    .packageTour_GridContainer {
        gap: 16px;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        margin-bottom: 42px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-wrapper {
        height: 180px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content {
        padding: 16px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .title-wrapper {
        font-size: 16px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 {
        padding: 0;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 {
        padding: 0;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 {
        padding: 0;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 {
        padding: 0;
    }

    .cta-sectionwrapper .cta-section {
        padding: 48px 32px;
    }

    .destination_GridContainer {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .destination_GridContainer .destination_GridItem .destination_title {
        padding: 46px 10px;
    }

    .currencyDropdown.show .dropdown-menu.show {
        position: relative !important;
        float: none !important;
        transform: translate3d(0px, 0px, 0px) !important;
        margin-top: 2px !important;
    }

    #logo img {
        height: 60px !important;
        margin-top: 0 !important;
        padding: 14px 2px 14px 2px !important;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--title {
        font-size: 36px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent {
        position: relative;
        padding: 0 30px 0 10px;
    }

    .header.main-header .btn_mobile {
        display: flex;
    }

    .header.main-header .main-header-navmenu .nav-menu-wrapper {
        justify-content: space-between;
        gap: 20px;
        flex-direction: row-reverse;
        width: 100%;
    }

    .header.main-header .main-header-navmenu ul#top_menu {
        display: none;
    }
}

@media screen and (max-width: 768px) {
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon{
        width: 46px;
        height: 46px;
    }
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon-wrapper::after{
        width: 46px;
        height: 46px;
    }
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img.videothumbnail .play-icon .icon-wrapper{
        font-size: 20px;
    }
    .booking__btn--stickywrapper {
        display: flex;
    }
    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--side .sticky-wrapper{
        display: none;
    }
    .itinerary_banner--section .banner__gallery .gallery__btn {
        bottom: 12px;
        right: 12px;
    }

    .itinerary_banner--section .banner__gallery .gallery__btn .textcontent {
        display: none;
    }

    .itinerary_banner--section .banner__gallery .banner__grid .banner__img:first-child {
        grid-column: span 7;
        height: 409px;
    }

    .itinerary_banner--section .banner__gallery .banner__grid .banner__img {
        grid-column: span 5;
        height: 202px;
    }

    .itinerary_banner--section .banner__gallery .banner__grid .banner__img:nth-child(4) {
        display: none;
    }

    .itinerary_banner--section .banner__gallery .banner__grid .banner__img:nth-child(5) {
        display: none;
    }

    .itinerary_nav--sm {
        display: block;
    }

    /* .itineraryTabNavigation.sticky-background{
        display: none;
    } */
    .itineraryTabNavigation.sticky-background .itinerary_nav {
        display: none;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts .tour-facts__grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 22px;
    }


    .packageTour_GridContainer {
        gap: 10px;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        margin-bottom: 42px;
    }

    .plan_trip--section .trip-card .img-wrapper {
        width: 60px;
        height: 60px;
    }

    .plan_trip--section .trip-card .img-wrapper img {
        width: 22px;
        height: 22px;
        object-fit: cover;
    }

    .cta-sectionwrapper .cta-section {
        padding: 36px 28px;
    }

    .cta-sectionwrapper .cta-section .left-section .title {
        font-size: 24px;
    }

    .destination_GridContainer {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .destination_GridContainer .destination_GridItem .destination_title {
        padding: 42px 10px;
        font-size: 18px;
    }

    .bannerSwiper {
        padding: 38px 0 60px 0;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--title {
        font-size: 36px;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent {
        margin-bottom: 48px;
        flex-direction: column-reverse;
        gap: 26px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent {
        position: relative;
        padding: 0 40px 0 0;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme4 {
        gap: 10px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 {
        gap: 10px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme3 {
        gap: 10px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 {
        gap: 10px;
    }
}

@media screen and (max-width: 576px) {
    .gallerymodalwrapper .modal-dialog .modal-content .modal-body{
        margin: 20px 0;
    }
    .plyr__control svg{
        height: 14px;
        width: 14px;
    }
    .tour-detail--section .similaTripSliderWrapper .similaTrip--title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper {
        padding: 20px 16px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_video__section .title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_equipmentInfo__section .tour_itinerary--title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_map__section .title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faq-titlewrapper .tour_faq--title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card .card-header .accordion-button {
        font-size: 15px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .accordion .card {
        padding: 0px 0px 0px 42px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .itinerary_titlewrapper .tour_itinerary--title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .included-price__wrapper .costdetail--list.included-list ul li {
        padding-left: 26px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-facts {
        padding: 24px 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-highlights .tour-highlights--title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tour-overview__contents .tour-overview__title {
        font-size: 20px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_costdetail__section .excluded-price__wrapper .excluded-price--title {
        font-size: 20px;
    }

    .breadcrumb__wrapper {
        padding: 16px 0 20px;
    }

    .breadcrumb__wrapper .breadcrumbs li a {
        font-size: 12px;
    }

    .breadcrumb__wrapper .breadcrumbs li.active {
        font-size: 12px;
    }

    .breadcrumb__wrapper .breadcrumbs li:not(:last-child)::after {
        margin: 0 6px;
    }
    .itinerary_banner--section .banner__gallery .banner__grid{
        max-height: inherit;
    }
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img{
        grid-column: span 6;
        height: 100%;
    }
    .itinerary_banner--section .banner__gallery .banner__grid .banner__img:first-child {
        grid-column: span 12;
        height: 100%;
    }


    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour_itinerary__section .itinerary_titlewrapper {
        flex-wrap: wrap;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tourdetails__overview .tourdetails__title {
        font-size: 26px;
    }

    .tour-detail--section .tourdetails__wrapper .tourdetails__wrapper--main .tour__faqsection .faq-titlewrapper {
        flex-wrap: wrap;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-container .tour-category {
        font-size: 10px;
        bottom: 6px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .img-wrapper {
        height: 156px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content {
        padding: 14px 12px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .title-wrapper {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-duration .icon-wrapper {
        font-size: 14px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-duration {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-price {
        font-size: 12px;
    }

    .popularTourSlider .swiper-wrapper .swiper-slide .popularTour_card .tour-content .tour-price .bold-text {
        font-size: 16px;
    }

    .hamburger-inner,
    .hamburger-inner::after,
    .hamburger-inner::before {
        width: 20px;
        height: 3px;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--title {
        font-size: 30px;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--contentwrapper .arrow-icon img {
        width: 62px;
        height: 62px;
    }

    .bannerSwiper .bannerCard .bannerCard__leftcontent .bannerCard__leftcontent--contentwrapper {
        gap: 10px;
    }

    /* banner theme2 css */
    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:first-child{
        border-radius: 36px 6px 6px 6px;
    }
    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:first-child img {
        border-radius: 36px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(2) img {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(3) img {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(4) img {
        border-radius: 6px 36px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(5) img {
        border-radius: 6px 6px 6px 6px;
    }
    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(2) {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(3)  {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(4) {
        border-radius: 6px 36px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme2 .imageGridItem:nth-child(5){
        border-radius: 6px 6px 6px 6px;
    }

    /* banner theme3 css */

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:first-child img {
        border-radius: 36px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(2) img {
        border-radius: 6px 6px 6px 36px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(3) img {
        border-radius: 6px 36px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(4) img {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(5) img {
        border-radius: 6px 36px 36px 6px;
    }
    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:first-child  {
        border-radius: 36px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(2)  {
        border-radius: 6px 6px 6px 36px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(3) {
        border-radius: 6px 36px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(4)  {
        border-radius: 6px 6px 6px 6px;
    }

    .bannerSwiper .bannerCard .bannerCard__rightcontent .imageGridContainer.theme1 .imageGridItem:nth-child(5) {
        border-radius: 6px 36px 36px 6px;
    }

    .section-paddingContainer {
        padding-top: 48px;
        padding-bottom: 48px;
    }

    .packageTour_GridContainer {
        gap: 10px;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        margin-bottom: 42px;
    }

    .packageTour_GridContainer .popularTour_card .img-container .tour-category {
        font-size: 10px;
        bottom: 6px;
    }

    .packageTour_GridContainer .popularTour_card .img-wrapper {
        height: 156px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content {
        padding: 14px 12px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content .title-wrapper {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content .tour-duration .icon-wrapper {
        font-size: 14px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content .tour-duration {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content .tour-price {
        font-size: 12px;
    }

    .packageTour_GridContainer .popularTour_card .tour-content .tour-price .bold-text {
        font-size: 16px;
    }
}
