/* Global Search Styles */
.search-menu-item {
    position: relative;
}

.global-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-toggle-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.search-toggle-btn:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.search-toggle-icon {
    font-size: 18px;
    color: #333;
    transition: color 0.3s ease;
}

.search-toggle-btn:hover .search-toggle-icon {
    color: #007bff;
}

.search-input-container {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.search-input-container.active {
    width: 300px;
    opacity: 1;
}

.global-search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 16px;
    font-size: 14px;
    background: transparent;
    min-width: 0;
}

.global-search-input::placeholder {
    color: #999;
}

.search-close-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-close-btn:hover {
    color: #333;
}

.search-spinner {
    position: absolute;
    right: 45px;
    display: flex;
    align-items: center;
}

.spinner-icon {
    font-size: 16px;
    color: #007bff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.search-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    width: 350px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.search-dropdown.show {
    opacity: 1;
    transform: translateY(0);
}

.search-result-item {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.search-result-item:hover,
.search-result-item.highlighted {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-image {
    width: 60px;
    height: 45px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
    background-color: #f0f0f0;
}

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 4px;
}

.search-result-title {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin: 0;
    line-height: 1.3;
}

.search-result-category-tag {
    background-color: #007bff;
    color: white;
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: capitalize;
    display: inline-block;
    margin-bottom: 2px;
}

.search-result-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-top: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .search-menu-item {
        display: none;
    }
}

@media (max-width: 768px) {
    .search-input-container.active {
        width: 250px;
    }

    .search-dropdown {
        width: 300px;
    }
}

/* Mobile Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

/* Mobile Search Icon Button */
.mobile-search-icon-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: #333;
}

.mobile-search-icon-btn:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.mobile-search-icon-btn:active {
    transform: scale(0.95);
}

.mobile-search-icon-btn:focus {
    outline: none;
    background-color: rgba(0, 123, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.mobile-search-icon-btn iconify-icon {
    font-size: 20px;
    transition: all 0.3s ease;
}

/* Ensure mobile search icon matches hamburger menu styling */
.header-actions .mobile-search-icon-btn {
    color: var(--black-200, #270722);
}

.header-actions .mobile-search-icon-btn:hover {
    color: var(--primary--200, #c78010);
}

/* Hide mobile search icon on desktop */
@media (min-width: 992px) {
    .mobile-search-icon-btn {
        display: none;
    }
}

/* Show mobile search icon only on mobile */
@media (max-width: 991px) {
    .header-actions {
        display: flex;
    }
}

/* Search Modal Styles */
.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-modal.active {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 1;
}

.search-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.search-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.search-modal.active .search-modal-content {
    transform: translateY(0);
}

.search-modal-header {
    padding: 20px 20px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    transition: border-color 0.3s ease;
}

.search-input-wrapper:focus-within {
    border-color: #007bff;
    background: white;
}

.search-input-icon {
    margin-left: 12px;
    font-size: 20px;
    color: #6b7280;
}

.modal-search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 12px;
    font-size: 16px;
    background: transparent;
    color: #111827;
}

.modal-search-input::placeholder {
    color: #9ca3af;
}

.search-modal-close {
    background: none;
    border: none;
    padding: 12px;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.3s ease;
}

.search-modal-close:hover {
    color: #374151;
}

.search-modal-subtitle {
    margin-top: 8px;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-shortcut {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.search-modal-body {
    max-height: 50vh;
    overflow-y: auto;
    padding: 0;
}

.search-section {
    padding: 20px;
}

.search-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 16px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
}

.category-item:hover {
    background: #f9fafb;
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
}

.category-name {
    font-size: 14px;
    font-weight: 500;
}

.search-results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.search-results-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.modal-search-result-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.modal-search-result-item:hover,
.modal-search-result-item.highlighted {
    background: #f9fafb;
    text-decoration: none;
    color: inherit;
}

.modal-search-result-image {
    width: 60px;
    height: 45px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
    background: #f3f4f6;
}

.modal-search-result-content {
    flex: 1;
    min-width: 0;
}

.modal-search-result-header {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 4px;
}

.modal-search-result-category {
    background: #007bff;
    color: white;
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: capitalize;
    display: inline-block;
    width: fit-content;
}

.modal-search-result-title {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    line-height: 1.3;
}

.modal-search-result-description {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-modal-footer {
    padding: 12px 20px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.search-shortcuts {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
}

.shortcut-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

kbd {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    padding: 1px 4px;
    font-size: 11px;
    font-family: monospace;
    color: #111827;
}

.search-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

/* Responsive */
@media (max-width: 768px) {
    .search-modal.active {
        padding-top: 2vh;
        align-items: flex-start;
    }

    .search-modal-content {
        width: 95%;
        max-height: 95vh;
        margin: 0 auto;
        border-radius: 12px 12px 0 0;
    }

    .search-modal-header {
        padding: 16px;
    }

    .modal-search-input {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 14px 12px;
    }

    .search-modal-subtitle {
        font-size: 13px;
    }

    .search-shortcut {
        display: none; /* Hide keyboard shortcuts on mobile */
    }

    .category-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .category-item {
        padding: 14px 12px;
        font-size: 15px;
    }

    .modal-search-result-item {
        padding: 12px;
    }

    .modal-search-result-image {
        width: 50px;
        height: 50px;
    }

    .modal-search-result-title {
        font-size: 14px;
    }

    .modal-search-result-description {
        font-size: 11px;
    }

    .search-shortcuts {
        display: none;
    }

    .search-modal-footer {
        padding: 8px 16px;
    }

    /* Improve touch targets */
    .search-modal-close {
        padding: 16px;
        font-size: 18px;
        min-width: 44px;
        min-height: 44px;
    }

    /* Prevent zoom on input focus for iOS */
    .modal-search-input {
        font-size: 16px !important;
    }

    /* Better touch scrolling */
    .search-modal-body {
        -webkit-overflow-scrolling: touch;
    }

    /* Larger touch targets for category items */
    .category-item {
        min-height: 44px;
        padding: 16px 12px;
    }

    /* Better touch feedback */
    .category-item:active,
    .modal-search-result-item:active {
        background-color: #e9ecef;
        transform: scale(0.98);
        transition: all 0.1s ease;
    }
}