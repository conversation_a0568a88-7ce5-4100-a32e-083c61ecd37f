<div class="hr-line-dashed"></div>
<div class="form-group row">
    <div class="pull-left">
        <a data-toggle="collapse" class="col-sm-10" id="sms-settings" href="#SmsSettings"
           role="button" aria-expanded="false" aria-controls="SmsSettings">
            <b>Order Template</b> <i class="fa fa-angle-down rotate-icon"></i>
        </a>
    </div>
</div>
<div class="hr-line-dashed"></div>
<div class="collapse" id="SmsSettings">
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Order is Created .</b>
            <code> Placeholder available : <b><span
                        class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$orderIsCreated) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][order_is_created]" cols="8"
                      rows="2">{{ old('sms.template.order_is_created') ?? $data['settings']['sms']['template']['order_is_created'] ?? config('sms.template.order_is_created') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.order_is_created'))
            <label class="error"
                   for="content['sms']['template']['order_is_created']"> {{ $errors->first('sms.template.order_is_created') }}</label>
        @endif
    </div>

    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Ordered Received .</b>
            <code> Placeholder available : <b><span
                        class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$orderIsReceived) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][order_is_received]" cols="8"
                      rows="2">{{ old('sms.template.order_is_received') ?? $data['settings']['sms']['template']['order_is_received'] ?? config('sms.template.order_is_received') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.order_is_received'))
            <label class="error"
                   for="content['sms']['template']['order_is_received']"> {{ $errors->first('sms.template.order_is_received') }}</label>
        @endif
    </div>

    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Ordered Redeemed .</b>
            <code> Placeholder available : <b><span
                        class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$orderIsRedeemed) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][order_is_redeemed]" cols="8"
                      rows="2">{{ old('sms.template.order_is_redeemed') ?? $data['settings']['sms']['template']['order_is_redeemed'] ?? config('sms.template.order_is_redeemed') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.order_is_redeemed'))
            <label class="error"
                   for="content['sms']['template']['order_is_redeemed']"> {{ $errors->first('sms.template.order_is_redeemed') }}</label>
        @endif
    </div>
</div>
