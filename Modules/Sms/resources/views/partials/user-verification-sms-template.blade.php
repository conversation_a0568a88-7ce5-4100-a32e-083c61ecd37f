<div class="form-group row">
    <div class="pull-left">
        <a data-toggle="collapse" class="col-sm-10" id="sms-settings" href="#SmsOTPSettings"
           role="button" aria-expanded="false" aria-controls="SmsSettings">
            <b>User Verification Template</b> <i class="fa fa-angle-down rotate-icon"></i>
        </a>
    </div>
</div>
<div class="hr-line-dashed"></div>
<div class="collapse" id="SmsOTPSettings">
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Send Otp Code .</b>
            <code> Placeholder available : <b><span
                        class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$sendOtpCode) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][send_otp_code]" cols="8"
                      rows="2">{{ old('sms.template.send_otp_code') ?? $data['settings']['sms']['template']['send_otp_code'] ?? config('sms.template.send_otp_code') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.send_otp_code'))
            <label class="error"
                   for="content['sms']['template']['send_otp_code']"> {{ $errors->first('sms.template.send_otp_code') }}</label>
        @endif
    </div>
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Send Verified sms to phone.</b>
            <code> Placeholder available : <b><span
                        class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$phoneIsVerified) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][phone_is_verified]" cols="8"
                      rows="2">{{ old('sms.template.phone_is_verified') ?? $data['settings']['sms']['template']['phone_is_verified'] ?? config('sms.template.phone_is_verified') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.phone_is_verified'))
            <label class="error"
                   for="content['sms']['template']['phone_is_verified']"> {{ $errors->first('sms.template.phone_is_verified') }}</label>
        @endif
    </div>
</div>
