<div class="hr-line-dashed"></div>
<div class="form-group row">
    <div class="pull-left">
        <a data-toggle="collapse" class="col-sm-10" id="sms-settings" href="#SmsSettings"
           role="button" aria-expanded="false" aria-controls="SmsSettings">
            <b>Followup Template</b> <i class="fa fa-angle-down rotate-icon"></i>
        </a>
    </div>
</div>
<div class="hr-line-dashed"></div>
<div class="collapse" id="SmsSettings">
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Due is Created .</b>
            <code> Placeholder available : <b><span
                            class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$orderIsCreated) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][due_created]" cols="8"
                      rows="2">{{ old('sms.template.due_created') ?? $data['settings']['sms']['template']['due_created'] ?? config('sms.template.due_created') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.due_created'))
            <label class="error"
                   for="content['sms']['template']['due_created']"> {{ $errors->first('sms.template.due_created') }}</label>
        @endif
    </div>

    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>Due Received .</b>
            <code> Placeholder available : <b><span
                            class="template-placeholder">{!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$dueIsReceived) !!}</span></b></code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][order_is_received]" cols="8"
                      rows="2">{{ old('sms.template.due_received') ?? $data['settings']['sms']['template']['due_received'] ?? config('sms.template.due_received') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.due_received'))
            <label class="error"
                   for="content['sms']['template']['due_received']"> {{ $errors->first('sms.template.due_received') }}</label>
        @endif
    </div>
</div>
