<div class="form-group row">
    <div class="pull-left">
        <a data-toggle="collapse" class="col-sm-10" id="sms-settings" href="#kycStatusSettings"
           role="button" aria-expanded="false" aria-controls="SmsSettings">
            <b>KYC Status Update Template</b> <i class="fa fa-angle-down rotate-icon"></i>
        </a>
    </div>
</div>
<div class="hr-line-dashed"></div>
<div class="collapse" id="kycStatusSettings">
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>KYC is verified .</b>
            <code>
                Placeholder available :
                <b>
                    <span class="template-placeholder">
                        {!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$kycIsVerified) !!}
                    </span>
                </b>
            </code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][kyc_is_verified]" cols="8"
                      rows="2">{{ old('sms.template.kyc_is_verified') ?? $data['settings']['sms']['template']['kyc_is_verified'] ?? config('sms.template.kyc_is_verified') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.kyc_is_verified'))
            <label class="error"
                   for="content['sms']['template']['kyc_is_verified']"> {{ $errors->first('sms.template.kyc_is_verified') }}</label>
        @endif
    </div>
    <div class="row form-group">
        <label class="col-sm-12 col-form-label">
            <b>KYC is rejected .</b>
            <code>
                Placeholder available :
                <b>
                    <span class="template-placeholder">
                        {!! implode('</span>,<span class="template-placeholder">', \Modules\Sms\Template::$kycIsRejected) !!}
                    </span>
                </b>
            </code>
        </label>
        <div class="col-sm-12">
            <textarea class="form-control sms-template" name="sms[template][kyc_is_rejected]" cols="8"
                      rows="2">{{ old('sms.template.kyc_is_rejected') ?? $data['settings']['sms']['template']['kyc_is_rejected'] ?? config('sms.template.kyc_is_rejected') ?? null }}</textarea>
        </div>
        @if($errors->has('sms.template.kyc_is_rejected'))
            <label class="error"
                   for="content['sms']['template']['kyc_is_rejected']"> {{ $errors->first('sms.template.kyc_is_rejected') }}</label>
        @endif
    </div>
</div>
