<div class="form-group row">
    <label class="col-sm-12 col-form-label is_required"><b>Is Enabled</b></label>
    <div class="col-sm-12">
        <div class="i-checks mt-2">
            <label
                for="fastsms_is_active">
                <input type="radio" name="sms[fastsms][is_enabled]" value="1" id="fastsms_is_active"
                       @if(isset($data['settings']['sms']['fastsms']['is_enabled']) && $data['settings']['sms']['fastsms']['is_enabled'] == 1) checked
                       @elseif(!isset($data['settings']['sms']['fastsms']['is_enabled'])) checked @endif>

                <i></i> Yes </label>
            <label
                for="fastsms_is_inactive">
                <input type="radio" name="sms[fastsms][is_enabled]" id="fastsms_is_inactive" value="0"
                       @if(isset($data['settings']['sms']['fastsms']['is_enabled']) && $data['settings']['sms']['fastsms']['is_enabled'] == 0) checked @endif>
                <i></i> No </label>
            <div>
                @if($errors->has('status'))
                    <label class="has-error" for="status">{{ $errors->first('status') }}</label>
                @endif
            </div>
        </div>

    </div>
</div>
<div class="form-group row">
    <label class="col-sm-12 col-form-label">Endpoint URL <span class="required">*</span></label>
    <div class="col-sm-12">
        {!! Form::text('sms[fastsms][endpoint]', $data['settings']['sms']['fastsms']['endpoint'] ?? config('sms.fastsms.endpoint') ?? null, ['class' => 'form-control','placeholder' => 'Enter the endpoint for fastsms', 'autocomplete' => 'off']) !!}
    </div>
    @if($errors->has('sms.fastsms.endpoint'))
        <label class="error" for="sms.fastsms.endpoint"> {{ $errors->first('sms.fastsms.endpoint') }}</label>
    @endif
</div>
<div class="hr-line-dashed"></div>

<div class="form-group row">
    <label class="col-sm-12 col-form-label">Token <span class="required">*</span></label>
    <div class="col-sm-12">
        {!! Form::text('sms[fastsms][token]', $data['settings']['sms']['fastsms']['token'] ?? config('sms.fastsms.token') ?? null, ['class' => 'form-control','placeholder' => 'Enter the token for fastsms', 'autocomplete' => 'off']) !!}
    </div>
    @if($errors->has('sms.fastsms.token'))
        <label class="error" for="sms.fastsms.token"> {{ $errors->first('sms.fastsms.token') }}</label>
    @endif
</div>

