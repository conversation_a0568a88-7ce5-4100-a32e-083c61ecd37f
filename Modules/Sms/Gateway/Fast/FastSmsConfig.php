<?php

namespace Modules\Sms\Gateway\Fast;

use Neputer\Supports\ConfigInstance;

/**
 * Class FastSmsConfig
 * @package Modules\Sms\Gateway\Sparrow
 */
final class FastSmsConfig extends ConfigInstance
{

    const CONFIG_KEY = 'sms';

    public static function getStatus()
    {
        return static::pull(self::CONFIG_KEY, 'fastsms.is_enabled') ?? true;
    }

    public static function getEndpoint()
    {
        return static::pull(self::CONFIG_KEY, 'fastsms.endpoint');
    }

    public static function getToken()
    {
        return static::pull(self::CONFIG_KEY, 'fastsms.token');
    }

    public static function getIdentity()
    {
        return static::pull(self::CONFIG_KEY, 'fastsms.identity'); // from
    }

    public static function dbKey(): string
    {
        return 'fastsms_last_response';
    }

}
