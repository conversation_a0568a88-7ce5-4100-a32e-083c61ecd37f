<?php

return  [
    'sparrowsms' => [
        'is_enabled'   => true,
        'endpoint' => 'http://api.sparrowsms.com/v2/sms/',
        'token' => 'jlMbFWZEikAOnYSEGGKX',
        'identity' => 'InfoSMS', // from
    ],

    'aakash' => [
        'is_enabled'   => true,
        'endpoint' => 'https://aakashsms.com/admin/public/sms/v3/send',
        'token' => 'ac1acf02e5e2d9956ff11051a04ede7ffaec8a46875c31929544e592f01ca703',
    ],

    'fastsms' => [
        'is_enabled'   => true,
        'endpoint' => 'https://app.fastsms.com.np/api/v1/sms/send',
        'token' => '',
    ],

    // If template is added we need to add patterns for the template in Modules\Sms\Gateway\Sparrow\Template
    'template' => [
        'due_created' => 'Hello {CUSTOMER_NAME}, your due is created. Your Due Amount is : {DUE_AMOUNT}. Thank You! '.env('APP_NAME', 'Orrog').' TEAM',
        'due_received' => 'Hello {CUSTOMER_NAME}, Your due ({FOLLOWUP_ID}) is delivered. Thank You! '.env('APP_NAME', 'Orrog').' TEAM',
        'send_otp_code' => 'Kindly use otp {OTP_CODE} for verification. Thank You!',
        'phone_is_verified' => 'Your account for kunyo.co is verified. Please, visit website to login.',
        'order_is_redeemed' => 'Your request for redeem to order {ORDER_ID} is successfully completed.',
    ],
];
