<?php

namespace Modules\Sms;

use Foundation\Lib\Meta;
use Illuminate\Support\Arr;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

final class SmsTracker
{

    const SETTING_ORDER_IS_CREATED   = 'sms_order_is_created';
    const SETTING_ORDER_IS_CREATED_STATUS   = 'sms_order_is_created_status';

    const SETTING_ORDER_IS_RECEIVED  = 'sms_order_is_received';
    const SETTING_ORDER_IS_RECEIVED_STATUS  = 'sms_order_is_received_status';

    const SETTING_ORDER_IS_REDEEMED  = 'sms_order_is_redeemed';
    const SETTING_ORDER_IS_REDEEMED_STATUS  = 'sms_order_is_redeemed_status';

    const SETTING_OTP_CODE           = 'sms_send_otp_code';
    const SETTING_OTP_CODE_STATUS           = 'sms_send_otp_code_status';

    const SETTING_PHONE_VERIFICATION = 'sms_phone_is_verified';
    const SETTING_PHONE_VERIFICATION_STATUS = 'sms_phone_is_verified_status';

    const SETTING_KYC_VERIFICATION   = 'sms_kyc_verification'; // Rejected/Accepted
    const SETTING_KYC_VERIFICATION_STATUS   = 'sms_kyc_verification_status'; // Rejected/Accepted

    const SETTING_MARKETING_SMS      = 'sms_marketing';
    const SETTING_MARKETING_SMS_STATUS      = 'sms_marketing_status';

    public static function increment( string $key, $status )
    {
        if (Arr::get($status, 'status') == ResponseAlias::HTTP_OK) {
            Meta::set( $key, Meta::get($key, 0) + 1 );
        }
    }

    public static function get( string $key )
    {
        return Meta::get($key, 0);
    }

    public static function total()
    {
        return array_sum([
            SmsTracker::get( self::SETTING_ORDER_IS_CREATED, 0 ),
            SmsTracker::get( self::SETTING_ORDER_IS_RECEIVED, 0 ),
            SmsTracker::get( self::SETTING_ORDER_IS_REDEEMED, 0 ),
            SmsTracker::get( self::SETTING_OTP_CODE, 0 ),
            SmsTracker::get( self::SETTING_PHONE_VERIFICATION, 0 ),
            SmsTracker::get( self::SETTING_KYC_VERIFICATION, 0 ),
            SmsTracker::get( self::SETTING_MARKETING_SMS, 0 ),
        ]);
    }

    public static function order()
    {
        return array_sum([
            SmsTracker::get( self::SETTING_ORDER_IS_CREATED, 0 ),
            SmsTracker::get( self::SETTING_ORDER_IS_RECEIVED, 0 ),
            SmsTracker::get( self::SETTING_ORDER_IS_REDEEMED, 0 ),
        ]);
    }

}
