name: QA

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ${{ vars.LOW_RUNNER }}
    name: Deployment
    steps:
      - name: Deploy to Server
        env:
          QA_BRANCH: ${{ vars.QA_BRANCH }}
          QA_DESTINATION: ${{ vars.QA_DESTINATION }}
          AWS_ARTIFACTS_SECRET_ACCESS_KEY: ${{ secrets.AWS_ARTIFACTS_SECRET_ACCESS_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.QA_SERVER_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          echo "${{ vars.QA_SERVER_DEPLOYMENT_SCRIPT }}" | tr -d '\r' | ssh -o 'StrictHostKeyChecking=no' -T ${{ vars.QA_SERVER_USER }}@${{ vars.QA_SERVER_IP }} -p ${{ vars.QA_SERVER_PORT }}

          rm ~/.ssh/id_rsa
