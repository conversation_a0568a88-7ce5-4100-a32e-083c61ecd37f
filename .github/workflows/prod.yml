name: Prod

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ${{ vars.LOW_RUNNER }}
    name: Deployment
    steps:
      - name: Deploy to Prod Server
        env:
          PROD_BRANCH: ${{ vars.PROD_BRANCH }}
          PROD_DESTINATION: ${{ vars.PROD_DESTINATION }}
          AWS_ARTIFACTS_SECRET_ACCESS_KEY: ${{ secrets.AWS_ARTIFACTS_SECRET_ACCESS_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PROD_SERVER_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          echo "${{ vars.PROD_SERVER_DEPLOYMENT_SCRIPT }}" | tr -d '\r' | ssh -o 'StrictHostKeyChecking=no' -T ${{ vars.PROD_SERVER_USER }}@${{ vars.PROD_SERVER_IP }} -p ${{ vars.PROD_SERVER_PORT }}

          rm ~/.ssh/id_rsa