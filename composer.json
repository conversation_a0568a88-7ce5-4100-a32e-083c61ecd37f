{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-curl": "*", "ext-json": "*", "arcanedev/log-viewer": "^10.0", "barryvdh/laravel-dompdf": "^2.0", "binarytorch/larecipe": "^2.6", "doctrine/dbal": "^3.6", "hidehalo/nanoid-php": "^1.1", "intervention/image": "^2.7", "laravel/framework": "^10.0", "laravel/helpers": "^1.6", "laravel/passport": "*", "laravel/pulse": "^1.4", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.21", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "laravelcollective/html": "^6.4", "league/flysystem-aws-s3-v3": "^3.12", "maatwebsite/excel": "*", "predis/predis": "^2.1", "razorpay/razorpay": "^2.9", "rollbar/rollbar-laravel": "^1.3", "sentry/sentry-laravel": "^4.10", "spatie/data-transfer-object": "^3.9", "spatie/laravel-analytics": "^4.1", "spatie/laravel-backup": "^8.1", "spatie/laravel-sitemap": "^7.0", "stripe/stripe-php": "^15.3", "symfony/yaml": "^6.2", "unisharp/laravel-filemanager": "^2.9", "yajra/laravel-datatables-oracle": "^10.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"files": ["Neputer/Supports/helpers.php", "app/Foundation/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Neputer\\": "Neputer/", "Foundation\\": "app/Foundation/", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}